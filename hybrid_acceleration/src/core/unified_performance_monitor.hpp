/**
 * @file unified_performance_monitor.hpp
 * @brief 统一性能监控系统 - 实时性能分析和自动调优
 */

#pragma once

#include "intelligent_strategy_selector.hpp"
#include "adaptive_load_balancer.hpp"
#include <memory>
#include <vector>
#include <map>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <algorithm>
#include <iostream>
#include <ostream>
#include <string>
#include <mutex>

namespace HybridNTT {

/**
 * @brief 性能指标结构
 */
struct PerformanceMetrics {
    // 基础性能指标
    double executionTime = 0.0;        // 执行时间 (秒)
    double throughput = 0.0;           // 吞吐量 (MB/s)
    double speedup = 1.0;              // 相对于串行的加速比
    double efficiency = 1.0;           // 并行效率
    
    // 资源利用率
    double cpuUtilization = 0.0;       // CPU利用率 (%)
    double memoryUtilization = 0.0;    // 内存利用率 (%)
    double gpuUtilization = 0.0;       // GPU利用率 (%)
    double networkUtilization = 0.0;   // 网络利用率 (%)
    
    // 缓存性能
    double l1CacheHitRate = 0.0;       // L1缓存命中率 (%)
    double l2CacheHitRate = 0.0;       // L2缓存命中率 (%)
    double l3CacheHitRate = 0.0;       // L3缓存命中率 (%)
    
    // 并行性能
    double loadBalanceRatio = 1.0;     // 负载均衡比率
    double communicationOverhead = 0.0; // 通信开销 (%)
    double synchronizationOverhead = 0.0; // 同步开销 (%)
    
    // 算法特定指标
    int problemSize = 0;
    int modulus = 0;
    IntelligentParallelStrategy strategy;
    IntelligentNTTVariant variant;
    IntelligentReductionType reduction;
    
    std::chrono::time_point<std::chrono::high_resolution_clock> timestamp;
};

/**
 * @brief 性能趋势分析结果
 */
struct PerformanceTrend {
    double averageSpeedup = 1.0;
    double maxSpeedup = 1.0;
    double minSpeedup = 1.0;
    double speedupVariance = 0.0;
    
    double averageEfficiency = 1.0;
    double maxEfficiency = 1.0;
    double minEfficiency = 1.0;
    
    std::string bestStrategy;
    std::string worstStrategy;
    
    // 性能改进建议
    std::vector<std::string> recommendations;
};

/**
 * @brief 统一性能监控系统
 */
class UnifiedPerformanceMonitor {
private:
    std::shared_ptr<IntelligentStrategySelector> strategySelector_;
    std::shared_ptr<AdaptiveLoadBalancer> loadBalancer_;
    
    // 性能数据存储
    std::vector<PerformanceMetrics> performanceHistory_;
    std::mutex historyMutex_;
    
    // 监控配置
    bool realTimeMonitoring_ = true;
    int maxHistorySize_ = 10000;
    double performanceThreshold_ = 0.8; // 性能警告阈值
    
    // 自动调优参数
    bool autoTuningEnabled_ = true;
    double tuningAggressiveness_ = 0.1;
    int tuningWindowSize_ = 50;
    
    // 基准性能（串行执行）
    std::map<std::pair<int, int>, double> serialBaselines_;

public:
    explicit UnifiedPerformanceMonitor(
        std::shared_ptr<IntelligentStrategySelector> selector = nullptr,
        std::shared_ptr<AdaptiveLoadBalancer> balancer = nullptr)
        : strategySelector_(selector), loadBalancer_(balancer) {}
    
    /**
     * @brief 开始性能监控
     */
    void startMonitoring(int problemSize, int modulus, 
                        const IntelligentOptimizationStrategy& strategy) {
        if (!realTimeMonitoring_) return;
        
        PerformanceMetrics metrics;
        metrics.problemSize = problemSize;
        metrics.modulus = modulus;
        metrics.strategy = strategy.parallelStrategy;
        metrics.variant = strategy.nttVariant;
        metrics.reduction = strategy.reductionType;
        metrics.timestamp = std::chrono::high_resolution_clock::now();
        
        // 开始资源监控
        startResourceMonitoring(metrics);
    }
    
    /**
     * @brief 结束性能监控并记录结果
     */
    void endMonitoring(int problemSize, int modulus, 
                      const IntelligentOptimizationStrategy& strategy,
                      double executionTime, double dataSize) {
        PerformanceMetrics metrics;
        metrics.problemSize = problemSize;
        metrics.modulus = modulus;
        metrics.strategy = strategy.parallelStrategy;
        metrics.variant = strategy.nttVariant;
        metrics.reduction = strategy.reductionType;
        metrics.executionTime = executionTime;
        metrics.throughput = dataSize / executionTime; // MB/s
        metrics.timestamp = std::chrono::high_resolution_clock::now();
        
        // 结束资源监控
        endResourceMonitoring(metrics);
        
        // 计算性能指标
        calculatePerformanceMetrics(metrics);
        
        // 记录性能数据
        recordPerformanceMetrics(metrics);
        
        // 实时性能分析
        if (realTimeMonitoring_) {
            analyzeRealTimePerformance(metrics);
        }
        
        // 自动调优
        if (autoTuningEnabled_) {
            performAutoTuning(metrics);
        }
    }
    
    /**
     * @brief 生成性能报告
     */
    void generatePerformanceReport(const std::string& filename = "performance_report.txt") {
        std::lock_guard<std::mutex> lock(historyMutex_);
        
        if (performanceHistory_.empty()) {
            std::cout << "没有性能数据可生成报告" << std::endl;
            return;
        }
        
        std::ofstream report(filename);
        if (!report.is_open()) {
            std::cerr << "无法创建性能报告文件: " << filename << std::endl;
            return;
        }
        
        report << "=== NTT并行优化性能报告 ===" << std::endl;
        report << "生成时间: " << getCurrentTimeString() << std::endl;
        report << "总测试次数: " << performanceHistory_.size() << std::endl;
        report << std::endl;
        
        // 总体性能统计
        generateOverallStatistics(report);
        
        // 策略性能对比
        generateStrategyComparison(report);
        
        // 性能趋势分析
        generateTrendAnalysis(report);
        
        // 性能优化建议
        generateOptimizationRecommendations(report);
        
        report.close();
        std::cout << "性能报告已生成: " << filename << std::endl;
    }
    
    /**
     * @brief 获取性能趋势分析
     */
    PerformanceTrend analyzePerformanceTrend() {
        std::lock_guard<std::mutex> lock(historyMutex_);
        
        PerformanceTrend trend;
        
        if (performanceHistory_.empty()) {
            return trend;
        }
        
        // 计算加速比统计
        std::vector<double> speedups;
        std::vector<double> efficiencies;
        std::map<IntelligentParallelStrategy, std::vector<double>> strategySpeedups;
        
        for (const auto& metrics : performanceHistory_) {
            speedups.push_back(metrics.speedup);
            efficiencies.push_back(metrics.efficiency);
            strategySpeedups[metrics.strategy].push_back(metrics.speedup);
        }
        
        // 加速比统计
        trend.averageSpeedup = std::accumulate(speedups.begin(), speedups.end(), 0.0) / speedups.size();
        trend.maxSpeedup = *std::max_element(speedups.begin(), speedups.end());
        trend.minSpeedup = *std::min_element(speedups.begin(), speedups.end());
        
        // 计算方差
        double speedupSum = 0.0;
        for (double speedup : speedups) {
            speedupSum += (speedup - trend.averageSpeedup) * (speedup - trend.averageSpeedup);
        }
        trend.speedupVariance = speedupSum / speedups.size();
        
        // 效率统计
        trend.averageEfficiency = std::accumulate(efficiencies.begin(), efficiencies.end(), 0.0) / efficiencies.size();
        trend.maxEfficiency = *std::max_element(efficiencies.begin(), efficiencies.end());
        trend.minEfficiency = *std::min_element(efficiencies.begin(), efficiencies.end());
        
        // 找出最佳和最差策略
        double bestAvgSpeedup = 0.0;
        double worstAvgSpeedup = std::numeric_limits<double>::max();
        
        for (const auto& [strategy, speedupList] : strategySpeedups) {
            if (!speedupList.empty()) {
                double avgSpeedup = std::accumulate(speedupList.begin(), speedupList.end(), 0.0) / speedupList.size();
                if (avgSpeedup > bestAvgSpeedup) {
                    bestAvgSpeedup = avgSpeedup;
                    trend.bestStrategy = "策略" + std::to_string(static_cast<int>(strategy));
                }
                if (avgSpeedup < worstAvgSpeedup) {
                    worstAvgSpeedup = avgSpeedup;
                    trend.worstStrategy = "策略" + std::to_string(static_cast<int>(strategy));
                }
            }
        }
        
        // 生成性能改进建议
        generatePerformanceRecommendations(trend);
        
        return trend;
    }
    
    /**
     * @brief 打印实时性能统计
     */
    void printRealTimeStatistics() {
        std::lock_guard<std::mutex> lock(historyMutex_);
        
        if (performanceHistory_.empty()) {
            std::cout << "暂无性能数据" << std::endl;
            return;
        }
        
        // 获取最近的性能数据
        const auto& latest = performanceHistory_.back();
        
        std::cout << "\n=== 实时性能监控 ===" << std::endl;
        std::cout << "问题规模: " << latest.problemSize << std::endl;
        std::cout << "模数: " << latest.modulus << std::endl;
        std::cout << "执行时间: " << std::fixed << std::setprecision(6) << latest.executionTime << " 秒" << std::endl;
        std::cout << "吞吐量: " << std::fixed << std::setprecision(2) << latest.throughput << " MB/s" << std::endl;
        std::cout << "加速比: " << std::fixed << std::setprecision(2) << latest.speedup << "x" << std::endl;
        std::cout << "并行效率: " << std::fixed << std::setprecision(1) << latest.efficiency * 100 << "%" << std::endl;
        
        std::cout << "\n资源利用率:" << std::endl;
        std::cout << "CPU: " << std::fixed << std::setprecision(1) << latest.cpuUtilization << "%" << std::endl;
        std::cout << "内存: " << std::fixed << std::setprecision(1) << latest.memoryUtilization << "%" << std::endl;
        if (latest.gpuUtilization > 0) {
            std::cout << "GPU: " << std::fixed << std::setprecision(1) << latest.gpuUtilization << "%" << std::endl;
        }
        
        std::cout << "\n负载均衡:" << std::endl;
        std::cout << "负载均衡比率: " << std::fixed << std::setprecision(2) << latest.loadBalanceRatio << std::endl;
        std::cout << "通信开销: " << std::fixed << std::setprecision(1) << latest.communicationOverhead << "%" << std::endl;
        std::cout << "同步开销: " << std::fixed << std::setprecision(1) << latest.synchronizationOverhead << "%" << std::endl;
    }

private:
    /**
     * @brief 开始资源监控
     */
    void startResourceMonitoring(PerformanceMetrics& metrics);
    
    /**
     * @brief 结束资源监控
     */
    void endResourceMonitoring(PerformanceMetrics& metrics);
    
    /**
     * @brief 计算性能指标
     */
    void calculatePerformanceMetrics(PerformanceMetrics& metrics);
    
    /**
     * @brief 记录性能指标
     */
    void recordPerformanceMetrics(const PerformanceMetrics& metrics);
    
    /**
     * @brief 实时性能分析
     */
    void analyzeRealTimePerformance(const PerformanceMetrics& metrics);
    
    /**
     * @brief 执行自动调优
     */
    void performAutoTuning(const PerformanceMetrics& metrics);
    
    /**
     * @brief 生成总体统计
     */
    void generateOverallStatistics(std::ofstream& report);
    
    /**
     * @brief 生成策略对比
     */
    void generateStrategyComparison(std::ofstream& report);
    
    /**
     * @brief 生成趋势分析
     */
    void generateTrendAnalysis(std::ofstream& report);
    
    /**
     * @brief 生成优化建议
     */
    void generateOptimizationRecommendations(std::ofstream& report);
    
    /**
     * @brief 生成性能改进建议
     */
    void generatePerformanceRecommendations(PerformanceTrend& trend);
    
    /**
     * @brief 获取当前时间字符串
     */
    std::string getCurrentTimeString();
};

} // namespace HybridNTT
