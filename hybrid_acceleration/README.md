# 智能多层次混合并行NTT加速框架

## 🎯 创新特点

### 1. 硬件感知智能调度系统
- **自动硬件检测**：CPU核心数、SIMD能力、GPU型号、内存大小、NUMA拓扑
- **性能特征建模**：基于硬件特性建立性能预测模型
- **动态策略选择**：根据问题规模和硬件配置自动选择最优并行策略

### 2. 四层混合并行架构
```
Level 4: MPI 分布式并行 (节点间)
   ↓
Level 3: OpenMP/pthread 共享内存并行 (节点内)
   ↓  
Level 2: SIMD 向量并行 (指令级)
   ↓
Level 1: CUDA GPU 大规模并行 (可选)
```

### 3. 自适应算法选择引擎
- **算法组合优化**：radix-2/4、DIF/DIT、Barrett/Montgomery的智能组合
- **参数自调优**：块大小、线程数、向量化度等参数的自动优化
- **负载均衡策略**：动态工作窃取和任务重分配

### 4. 内存层次优化
- **NUMA感知分配**：考虑NUMA架构的内存分配策略
- **多级缓存优化**：L1/L2/L3缓存友好的数据布局
- **预取和流水线**：智能数据预取和计算-通信重叠

## 🚀 技术创新亮点

### 1. 工作窃取负载均衡系统
- **层次化任务分解**：递归分解大任务为适合并行的子任务
- **动态负载均衡**：线程间自动窃取任务，避免负载不均
- **任务粒度自适应**：根据系统负载动态调整任务大小
- **优先级调度**：关键路径任务优先执行

### 2. NUMA感知内存管理器
- **智能内存分配**：根据NUMA拓扑优化数据分布
- **数据局部性优化**：自动迁移数据到最优NUMA节点
- **缓存行对齐**：避免false sharing，提升缓存效率
- **大页内存支持**：减少TLB miss，提升内存访问性能

### 3. 通信计算重叠优化
- **异步MPI通信**：非阻塞通信与计算并行执行
- **流水线处理**：数据传输与计算流水线化
- **智能数据分块**：优化通信粒度，减少通信开销
- **拓扑感知通信**：根据网络拓扑优化通信模式

### 4. 自适应性能调优
- **运行时策略优化**：基于历史性能数据的参数自动调整
- **硬件特征学习**：机器学习驱动的性能预测
- **动态阈值调整**：根据系统状态调整并行化阈值
- **多目标优化**：平衡执行时间、内存使用和能耗

## 📁 目录结构

```
hybrid_acceleration/
├── src/
│   ├── core/
│   │   ├── hardware_detector.hpp         # 硬件检测模块
│   │   ├── performance_model.hpp         # 性能预测模型
│   │   ├── strategy_selector.hpp         # 策略选择器
│   │   └── adaptive_scheduler.hpp        # 自适应调度器
│   ├── algorithms/
│   │   ├── hybrid_ntt_engine.hpp         # 混合NTT引擎
│   │   ├── algorithm_variants.hpp        # 算法变体集合
│   │   └── optimization_suite.hpp        # 优化策略套件
│   ├── parallel/
│   │   ├── mpi_coordinator.hpp           # MPI协调器
│   │   ├── openmp_executor.hpp           # OpenMP执行器
│   │   ├── simd_accelerator.hpp          # SIMD加速器
│   │   └── cuda_engine.hpp               # CUDA引擎
│   └── utils/
│       ├── memory_manager.hpp            # 内存管理器
│       ├── profiler.hpp                  # 性能分析器
│       └── io_interface.hpp              # 统一I/O接口
├── tests/
├── benchmarks/
├── config/
└── docs/
```

## 🔬 核心技术创新

### 1. 智能硬件感知
```cpp
class HardwareDetector {
    // 检测CPU特性（核心数、频率、缓存、SIMD支持）
    // 检测GPU特性（型号、计算能力、内存）
    // 检测网络特性（带宽、延迟、拓扑）
    // 生成硬件配置文件
};
```

### 2. 性能预测建模
```cpp
class PerformanceModel {
    // 基于历史数据的机器学习模型
    // 考虑问题规模、硬件特性、算法复杂度
    // 预测不同策略组合的性能表现
};
```

### 3. 动态负载均衡
```cpp
class AdaptiveScheduler {
    // 工作窃取算法
    // 动态任务重分配
    // 通信-计算重叠优化
};
```

## 📈 预期性能提升

相比单一并行技术的预期加速比：
- **小规模问题** (n ≤ 1K): 5-15x 
- **中规模问题** (1K < n ≤ 16K): 20-50x
- **大规模问题** (n > 16K): 50-200x

## 🎯 使用场景

### 1. 异构计算环境
- CPU + GPU 混合集群
- 不同型号GPU的混合使用
- NUMA架构的多处理器系统

### 2. 动态负载场景  
- 问题规模变化的批处理
- 实时系统中的自适应计算
- 多租户环境的资源共享

### 3. 高性能计算应用
- 大规模信号处理
- 密码学计算
- 科学计算仿真 