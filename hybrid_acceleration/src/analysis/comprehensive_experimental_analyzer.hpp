/**
 * @file comprehensive_experimental_analyzer.hpp
 * @brief 综合实验分析系统 - 统计显著性测试和深度性能分析
 */

#pragma once

#include "../core/unified_performance_monitor.hpp"
#include "../visualization/advanced_performance_visualizer.hpp"
#include <vector>
#include <map>
#include <string>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <random>
#include <fstream>
#include <sstream>
#include <iostream>
#include <ostream>

namespace HybridNTT {

/**
 * @brief 实验设计结构
 */
struct ExperimentalDesign {
    std::vector<int> problemSizes;
    std::vector<int> moduli;
    std::vector<IntelligentParallelStrategy> strategies;
    std::vector<IntelligentNTTVariant> variants;
    std::vector<IntelligentReductionType> reductions;
    
    int repetitions = 10;           // 每个配置的重复次数
    double confidenceLevel = 0.95;  // 置信水平
    bool randomizeOrder = true;     // 随机化实验顺序
    
    // 实验控制参数
    double warmupRatio = 0.1;       // 预热比例
    int maxExecutionTime = 300;     // 最大执行时间（秒）
    bool enableOutlierDetection = true; // 异常值检测
};

/**
 * @brief 统计测试结果
 */
struct StatisticalTestResult {
    double pValue = 1.0;
    double testStatistic = 0.0;
    bool isSignificant = false;
    double effectSize = 0.0;
    double confidenceIntervalLower = 0.0;
    double confidenceIntervalUpper = 0.0;
    std::string testType;
    std::string interpretation;
};

/**
 * @brief 性能对比结果
 */
struct PerformanceComparison {
    std::string strategy1;
    std::string strategy2;
    double meanDifference = 0.0;
    double percentageImprovement = 0.0;
    StatisticalTestResult statisticalTest;
    
    // 效果大小分类
    enum class EffectSize {
        NEGLIGIBLE,  // 可忽略 (< 0.2)
        SMALL,       // 小 (0.2 - 0.5)
        MEDIUM,      // 中等 (0.5 - 0.8)
        LARGE        // 大 (> 0.8)
    } effectSizeCategory;
};

/**
 * @brief 综合实验分析器
 */
class ComprehensiveExperimentalAnalyzer {
private:
    std::shared_ptr<UnifiedPerformanceMonitor> performanceMonitor_;
    std::shared_ptr<AdvancedPerformanceVisualizer> visualizer_;
    
    // 实验数据存储
    std::vector<PerformanceMetrics> experimentalData_;
    std::map<std::string, std::vector<double>> groupedResults_;
    
    // 统计分析配置
    double significanceLevel_ = 0.05;
    int bootstrapSamples_ = 10000;
    bool useNonParametricTests_ = false;
    
    // 随机数生成器
    std::mt19937 rng_;

public:
    explicit ComprehensiveExperimentalAnalyzer(
        std::shared_ptr<UnifiedPerformanceMonitor> monitor,
        std::shared_ptr<AdvancedPerformanceVisualizer> visualizer)
        : performanceMonitor_(monitor), visualizer_(visualizer), rng_(std::random_device{}()) {}
    
    /**
     * @brief 执行综合实验分析
     */
    void performComprehensiveAnalysis(const ExperimentalDesign& design) {
        std::cout << "开始综合实验分析..." << std::endl;
        
        // 1. 数据收集和预处理
        collectAndPreprocessData(design);
        
        // 2. 描述性统计分析
        performDescriptiveAnalysis();
        
        // 3. 统计显著性测试
        performStatisticalSignificanceTests();
        
        // 4. 效果大小分析
        performEffectSizeAnalysis();
        
        // 5. 多重比较校正
        performMultipleComparisonCorrection();
        
        // 6. 回归分析
        performRegressionAnalysis();
        
        // 7. 方差分析 (ANOVA)
        performANOVAAnalysis();
        
        // 8. 非参数统计测试
        performNonParametricTests();
        
        // 9. Bootstrap置信区间
        performBootstrapAnalysis();
        
        // 10. 生成综合报告
        generateComprehensiveReport();
        
        std::cout << "综合实验分析完成！" << std::endl;
    }
    
    /**
     * @brief 执行策略性能对比分析
     */
    std::vector<PerformanceComparison> compareStrategies(
        const std::vector<IntelligentParallelStrategy>& strategies) {
        
        std::vector<PerformanceComparison> comparisons;
        
        // 两两比较所有策略
        for (size_t i = 0; i < strategies.size(); ++i) {
            for (size_t j = i + 1; j < strategies.size(); ++j) {
                auto comparison = compareStrategyPair(strategies[i], strategies[j]);
                comparisons.push_back(comparison);
            }
        }
        
        return comparisons;
    }
    
    /**
     * @brief 执行可扩展性分析
     */
    void performScalabilityAnalysis() {
        std::cout << "执行可扩展性分析..." << std::endl;
        
        // 分析强可扩展性 (Strong Scaling)
        analyzeStrongScaling();
        
        // 分析弱可扩展性 (Weak Scaling)
        analyzeWeakScaling();
        
        // 分析Amdahl定律拟合
        analyzeAmdahlsLaw();
        
        // 分析Gustafson定律拟合
        analyzeGustafsonsLaw();
        
        std::cout << "可扩展性分析完成" << std::endl;
    }
    
    /**
     * @brief 执行算法复杂度分析
     */
    void performComplexityAnalysis() {
        std::cout << "执行算法复杂度分析..." << std::endl;
        
        // 理论复杂度 vs 实际性能
        analyzeTheoreticalVsActualComplexity();
        
        // 缓存性能分析
        analyzeCachePerformance();
        
        // 内存访问模式分析
        analyzeMemoryAccessPatterns();
        
        std::cout << "算法复杂度分析完成" << std::endl;
    }
    
    /**
     * @brief 执行硬件特性影响分析
     */
    void performHardwareImpactAnalysis() {
        std::cout << "执行硬件特性影响分析..." << std::endl;
        
        // CPU特性影响分析
        analyzeCPUFeatureImpact();
        
        // 内存层次结构影响分析
        analyzeMemoryHierarchyImpact();
        
        // SIMD指令集影响分析
        analyzeSIMDImpact();
        
        // GPU加速效果分析
        analyzeGPUAccelerationImpact();
        
        std::cout << "硬件特性影响分析完成" << std::endl;
    }
    
    /**
     * @brief 生成性能预测模型
     */
    void generatePerformancePredictionModel() {
        std::cout << "生成性能预测模型..." << std::endl;
        
        // 线性回归模型
        buildLinearRegressionModel();
        
        // 多项式回归模型
        buildPolynomialRegressionModel();
        
        // 机器学习模型 (简化版)
        buildMLPredictionModel();
        
        // 模型验证和交叉验证
        validatePredictionModels();
        
        std::cout << "性能预测模型生成完成" << std::endl;
    }
    
    /**
     * @brief 执行异常值检测和分析
     */
    void performOutlierAnalysis() {
        std::cout << "执行异常值检测和分析..." << std::endl;
        
        // Z-score方法检测异常值
        detectOutliersZScore();
        
        // IQR方法检测异常值
        detectOutliersIQR();
        
        // 修正的Z-score方法
        detectOutliersModifiedZScore();
        
        // 异常值影响分析
        analyzeOutlierImpact();
        
        std::cout << "异常值分析完成" << std::endl;
    }
    
    /**
     * @brief 生成实验设计建议
     */
    ExperimentalDesign generateOptimalExperimentalDesign(
        const std::vector<int>& availableProblemSizes,
        const std::vector<IntelligentParallelStrategy>& availableStrategies,
        int maxExperiments = 1000) {
        
        ExperimentalDesign design;
        
        // 使用统计功效分析确定样本大小
        int optimalRepetitions = calculateOptimalSampleSize();
        
        // 使用拉丁超立方采样选择实验点
        auto selectedPoints = latinHypercubeSampling(availableProblemSizes, availableStrategies, maxExperiments);
        
        // 配置实验设计
        design.problemSizes = selectedPoints.first;
        design.strategies = selectedPoints.second;
        design.repetitions = optimalRepetitions;
        design.confidenceLevel = 0.95;
        design.randomizeOrder = true;
        design.enableOutlierDetection = true;
        
        return design;
    }

private:
    /**
     * @brief 数据收集和预处理
     */
    void collectAndPreprocessData(const ExperimentalDesign& design);
    
    /**
     * @brief 描述性统计分析
     */
    void performDescriptiveAnalysis();
    
    /**
     * @brief 统计显著性测试
     */
    void performStatisticalSignificanceTests();
    
    /**
     * @brief 效果大小分析
     */
    void performEffectSizeAnalysis();
    
    /**
     * @brief 多重比较校正
     */
    void performMultipleComparisonCorrection();
    
    /**
     * @brief 回归分析
     */
    void performRegressionAnalysis();
    
    /**
     * @brief 方差分析
     */
    void performANOVAAnalysis();
    
    /**
     * @brief 非参数统计测试
     */
    void performNonParametricTests();
    
    /**
     * @brief Bootstrap分析
     */
    void performBootstrapAnalysis();
    
    /**
     * @brief 生成综合报告
     */
    void generateComprehensiveReport();
    
    /**
     * @brief 比较两个策略
     */
    PerformanceComparison compareStrategyPair(IntelligentParallelStrategy strategy1, 
                                             IntelligentParallelStrategy strategy2);
    
    /**
     * @brief 强可扩展性分析
     */
    void analyzeStrongScaling();
    
    /**
     * @brief 弱可扩展性分析
     */
    void analyzeWeakScaling();
    
    /**
     * @brief Amdahl定律分析
     */
    void analyzeAmdahlsLaw();
    
    /**
     * @brief Gustafson定律分析
     */
    void analyzeGustafsonsLaw();
    
    /**
     * @brief 理论vs实际复杂度分析
     */
    void analyzeTheoreticalVsActualComplexity();
    
    /**
     * @brief 缓存性能分析
     */
    void analyzeCachePerformance();
    
    /**
     * @brief 内存访问模式分析
     */
    void analyzeMemoryAccessPatterns();
    
    /**
     * @brief CPU特性影响分析
     */
    void analyzeCPUFeatureImpact();
    
    /**
     * @brief 内存层次结构影响分析
     */
    void analyzeMemoryHierarchyImpact();
    
    /**
     * @brief SIMD影响分析
     */
    void analyzeSIMDImpact();
    
    /**
     * @brief GPU加速影响分析
     */
    void analyzeGPUAccelerationImpact();
    
    /**
     * @brief 构建线性回归模型
     */
    void buildLinearRegressionModel();
    
    /**
     * @brief 构建多项式回归模型
     */
    void buildPolynomialRegressionModel();
    
    /**
     * @brief 构建机器学习预测模型
     */
    void buildMLPredictionModel();
    
    /**
     * @brief 验证预测模型
     */
    void validatePredictionModels();
    
    /**
     * @brief Z-score异常值检测
     */
    void detectOutliersZScore();
    
    /**
     * @brief IQR异常值检测
     */
    void detectOutliersIQR();
    
    /**
     * @brief 修正Z-score异常值检测
     */
    void detectOutliersModifiedZScore();
    
    /**
     * @brief 异常值影响分析
     */
    void analyzeOutlierImpact();
    
    /**
     * @brief 计算最优样本大小
     */
    int calculateOptimalSampleSize();
    
    /**
     * @brief 拉丁超立方采样
     */
    std::pair<std::vector<int>, std::vector<IntelligentParallelStrategy>> 
    latinHypercubeSampling(const std::vector<int>& problemSizes,
                          const std::vector<IntelligentParallelStrategy>& strategies,
                          int numSamples);
    
    /**
     * @brief 计算Cohen's d效果大小
     */
    double calculateCohensD(const std::vector<double>& group1, const std::vector<double>& group2);
    
    /**
     * @brief 执行t检验
     */
    StatisticalTestResult performTTest(const std::vector<double>& group1, const std::vector<double>& group2);
    
    /**
     * @brief 执行Mann-Whitney U检验
     */
    StatisticalTestResult performMannWhitneyTest(const std::vector<double>& group1, const std::vector<double>& group2);
    
    /**
     * @brief Bootstrap置信区间计算
     */
    std::pair<double, double> calculateBootstrapCI(const std::vector<double>& data, double confidence = 0.95);
};

} // namespace HybridNTT
