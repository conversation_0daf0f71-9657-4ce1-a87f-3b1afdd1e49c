====================================================
    智能多层次混合并行NTT加速框架
    Intelligent Multi-Level Hybrid Parallel NTT
====================================================
开始硬件检测...
硬件检测完成。

======== 硬件配置摘要 ========
CPU:
  厂商: Intel
  型号: Intel(R) Core(TM) i9-10900X CPU @ 3.70GHz
  逻辑核心: 20
  物理核心: 10
  SIMD支持: AVX2 AVX SSE
GPU:
  无可用GPU设备
内存:
  总内存: 63992 MB
  可用内存: 942 MB
  NUMA节点: 1
  大页支持: 是
网络:
  MPI支持: 否
============================


========================================
          策略分析测试
========================================

--- 问题规模: n = 4 ---
正在分析最优策略 (n=4, p=7340033)...
已选择最优策略，预估性能评分: 187500 (置信度: 0.8)

======== 策略详情 ========
并行策略: OpenMP + SIMD
NTT算法: Radix-2 DIT
模运算策略: 朴素
线程数: 20
块大小: 64
向量宽度: 8
共享内存: 否
预计算: 否
流水线: 是
性能评分: 187500
置信度: 0.8
========================

硬件评分: 374.984

--- 问题规模: n = 1024 ---
正在分析最优策略 (n=1024, p=7340033)...
已选择最优策略，预估性能评分: 710.227 (置信度: 0.8)

======== 策略详情 ========
并行策略: SIMD
NTT算法: Radix-2 DIT
模运算策略: 朴素
线程数: 1
块大小: 64
向量宽度: 8
共享内存: 否
预计算: 否
流水线: 否
性能评分: 710.227
置信度: 0.8
========================

硬件评分: 374.984

--- 问题规模: n = 16384 ---
正在分析最优策略 (n=16384, p=7340033)...
已选择最优策略，预估性能评分: 128.571 (置信度: 0.8)

======== 策略详情 ========
并行策略: OpenMP + SIMD
NTT算法: Radix-2 DIT
模运算策略: 朴素
线程数: 20
块大小: 256
向量宽度: 8
共享内存: 否
预计算: 是
流水线: 是
性能评分: 128.571
置信度: 0.8
========================

硬件评分: 374.984

--- 问题规模: n = 131072 ---
正在分析最优策略 (n=131072, p=7340033)...
已选择最优策略，预估性能评分: 32.3127 (置信度: 0.8)

======== 策略详情 ========
并行策略: OpenMP + SIMD
NTT算法: Radix-4 DIT
模运算策略: 朴素
线程数: 20
块大小: 1024
向量宽度: 8
共享内存: 否
预计算: 是
流水线: 是
性能评分: 32.3127
置信度: 0.8
========================

硬件评分: 374.984

========================================
          正确性验证测试
========================================
正在分析最优策略 (n=64, p=998244353)...
已选择最优策略，预估性能评分: 3906.25 (置信度: 0.8)
大小     64: ✓ 正确
正在分析最优策略 (n=256, p=998244353)...
已选择最优策略，预估性能评分: 1136.36 (置信度: 0.8)
大小    256: ✓ 正确
正在分析最优策略 (n=1024, p=998244353)...
已选择最优策略，预估性能评分: 710.227 (置信度: 0.8)
大小   1024: ✓ 正确
正在分析最优策略 (n=4096, p=998244353)...
已选择最优策略，预估性能评分: 177.557 (置信度: 0.8)
大小   4096: ✓ 正确
✓ 所有正确性测试通过！

========================================
          性能基准测试
========================================

======== 性能基准测试 ========
    大小平均时间(μs)吞吐量(MB/s)      加速比
----------------------------------------------------
正在分析最优策略 (n=256, p=998244353)...
已选择最优策略，预估性能评分: 1136.36 (置信度: 0.8)
       256          71.15        14.4           1.00
正在分析最优策略 (n=512, p=998244353)...
已选择最优策略，预估性能评分: 1010.10 (置信度: 0.80)
       512         148.47        13.8           0.48
正在分析最优策略 (n=1024, p=998244353)...
已选择最优策略，预估性能评分: 710.23 (置信度: 0.80)
      1024         337.63        12.1           0.21
正在分析最优策略 (n=2048, p=998244353)...
已选择最优策略，预估性能评分: 645.66 (置信度: 0.80)
      2048         694.66        11.8           0.10
正在分析最优策略 (n=4096, p=998244353)...
已选择最优策略，预估性能评分: 177.56 (置信度: 0.80)
      4096        1459.80        11.2           0.05
正在分析最优策略 (n=8192, p=998244353)...
已选择最优策略，预估性能评分: 276.92 (置信度: 0.80)
      8192        2470.03        13.3           0.03
====================================================

========================================
          标准测试用例
========================================
错误：无法打开输入文件 ../nttdata/0.in
测试用例 0 执行失败: 文件读取失败
测试用例 0 失败！
错误：无法打开输入文件 ../nttdata/1.in
测试用例 1 执行失败: 文件读取失败
测试用例 1 失败！
错误：无法打开输入文件 ../nttdata/2.in
测试用例 2 执行失败: 文件读取失败
测试用例 2 失败！
错误：无法打开输入文件 ../nttdata/3.in
测试用例 3 执行失败: 文件读取失败
测试用例 3 失败！

❌ 部分测试用例失败！

========================================
          性能对比测试
========================================

--- 规模测试: n = 4 ---
正在分析最优策略 (n=4, p=7340033)...
已选择最优策略，预估性能评分: 187500.00 (置信度: 0.80)
已选择策略: OpenMP + SIMD
执行时间: 1510.00 微秒

--- 规模测试: n = 1024 ---
正在分析最优策略 (n=1024, p=7340033)...
已选择最优策略，预估性能评分: 710.23 (置信度: 0.80)
已选择策略: 混合优化
执行时间: 606.00 微秒

--- 规模测试: n = 4096 ---
正在分析最优策略 (n=4096, p=7340033)...
已选择最优策略，预估性能评分: 177.56 (置信度: 0.80)
已选择策略: 混合优化
执行时间: 2383.00 微秒

--- 规模测试: n = 16384 ---
正在分析最优策略 (n=16384, p=7340033)...
已选择最优策略，预估性能评分: 128.57 (置信度: 0.80)
已选择策略: OpenMP + SIMD
执行时间: 12630.00 微秒

--- 规模测试: n = 65536 ---
正在分析最优策略 (n=65536, p=7340033)...
已选择最优策略，预估性能评分: 34.33 (置信度: 0.80)
已选择策略: OpenMP + SIMD
执行时间: 35421.00 微秒

--- 规模测试: n = 131072 ---
正在分析最优策略 (n=131072, p=7340033)...
已选择最优策略，预估性能评分: 32.31 (置信度: 0.80)
已选择策略: OpenMP + SIMD
执行时间: 35486.00 微秒

========================================
          框架特性总结
========================================
✓ 硬件自适应检测与配置
✓ 智能策略选择与优化
✓ 多层次混合并行加速
✓ 自动算法变体选择
✓ 动态负载均衡调度
✓ 兼容原有项目接口

硬件配置评分: 375.0 分
硬件配置良好，建议使用多线程+SIMD策略

========================================
          详细性能分析
========================================

======== 性能报告 ========
总NTT调用次数: 18
平均执行时间: 0.00 微秒
当前策略配置:
  线程数: 20
  块大小: 1024
  向量宽度: 8
========================


--- NUMA内存管理统计 ---
total_allocations: 0
numa_interleaved_allocations: 0
total_deallocations: 0
numa_local_allocations: 0
huge_page_allocations: 0
cache_aligned_allocations: 0
memory_migrations: 0

--- 并行执行统计 ---
mpi_communication_time: 0.00
task_count: 0.00
cuda_kernel_time: 0.00
task_execution_time: 0.00

--- 自适应调优演示 ---
大小 1024 调优后执行时间: 29.77 μs
大小 4096 调优后执行时间: 130.66 μs
大小 16384 调优后执行时间: 596.40 μs

========================================
          技术创新总结
========================================
1. 四层混合并行架构: MPI + OpenMP + SIMD + CUDA
2. 硬件感知智能调度: 自动检测并优化硬件配置
3. 工作窃取负载均衡: 动态任务分配和负载平衡
4. NUMA感知内存管理: 优化数据局部性和内存访问
5. 自适应性能调优: 运行时策略优化和参数调整
6. 多级缓存优化: 预取和缓存行对齐优化
7. 通信计算重叠: MPI通信与计算并行执行
8. 智能算法选择: 根据问题规模自动选择最优算法
========================================

性能结果已保存到 hybrid_results.csv
输出文件已保存到 files/ 目录

程序执行完成。
