/**
 * @file advanced_performance_visualizer.hpp
 * @brief 高级性能可视化系统 - 生成发布质量的性能图表
 */

#pragma once

#include "../core/unified_performance_monitor.hpp"
#include <vector>
#include <string>
#include <map>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <cmath>
#include <iostream>
#include <ostream>
#include <climits>

namespace HybridNTT {

/**
 * @brief 图表类型枚举
 */
enum class ChartType {
    SPEEDUP_COMPARISON,      // 加速比对比图
    EFFICIENCY_ANALYSIS,     // 并行效率分析图
    SCALABILITY_STUDY,       // 可扩展性研究图
    THROUGHPUT_BENCHMARK,    // 吞吐量基准测试图
    RESOURCE_UTILIZATION,    // 资源利用率图
    ALGORITHM_COMPARISON,    // 算法对比图
    LOAD_BALANCE_ANALYSIS,   // 负载均衡分析图
    PERFORMANCE_HEATMAP,     // 性能热力图
    TREND_ANALYSIS,          // 性能趋势图
    STATISTICAL_DISTRIBUTION // 统计分布图
};

/**
 * @brief 图表配置结构
 */
struct ChartConfig {
    ChartType type;
    std::string title;
    std::string xlabel;
    std::string ylabel;
    std::string filename;
    
    // 图表样式配置
    int width = 1200;
    int height = 800;
    bool showGrid = true;
    bool showLegend = true;
    std::string colorScheme = "viridis";
    
    // 数据过滤配置
    int minProblemSize = 0;
    int maxProblemSize = INT_MAX;
    std::vector<IntelligentParallelStrategy> includedStrategies;
    
    // 统计配置
    bool showErrorBars = true;
    bool showTrendLine = true;
    double confidenceLevel = 0.95;
};

/**
 * @brief 数据点结构
 */
struct DataPoint {
    double x;
    double y;
    double error = 0.0;
    std::string label;
    std::string category;
    
    DataPoint(double x_val, double y_val, const std::string& lbl = "", const std::string& cat = "")
        : x(x_val), y(y_val), label(lbl), category(cat) {}
};

/**
 * @brief 数据系列结构
 */
struct DataSeries {
    std::string name;
    std::vector<DataPoint> points;
    std::string color;
    std::string style; // line, scatter, bar, etc.
    
    DataSeries(const std::string& series_name) : name(series_name) {}
};

/**
 * @brief 高级性能可视化器
 */
class AdvancedPerformanceVisualizer {
private:
    std::shared_ptr<UnifiedPerformanceMonitor> performanceMonitor_;
    
    // 可视化配置
    std::string outputDirectory_ = "performance_charts/";
    std::string pythonScriptPath_ = "generate_charts.py";
    
    // 颜色方案
    std::vector<std::string> colorPalette_ = {
        "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd",
        "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"
    };
    
    // 统计分析缓存
    std::map<std::string, std::vector<double>> statisticsCache_;

public:
    explicit AdvancedPerformanceVisualizer(std::shared_ptr<UnifiedPerformanceMonitor> monitor)
        : performanceMonitor_(monitor) {
        createOutputDirectory();
    }
    
    /**
     * @brief 生成所有性能图表
     */
    void generateAllCharts() {
        std::cout << "开始生成高级性能可视化图表..." << std::endl;
        
        // 1. 加速比对比图
        generateSpeedupComparisonChart();
        
        // 2. 并行效率分析图
        generateEfficiencyAnalysisChart();
        
        // 3. 可扩展性研究图
        generateScalabilityStudyChart();
        
        // 4. 吞吐量基准测试图
        generateThroughputBenchmarkChart();
        
        // 5. 资源利用率图
        generateResourceUtilizationChart();
        
        // 6. 算法对比图
        generateAlgorithmComparisonChart();
        
        // 7. 负载均衡分析图
        generateLoadBalanceAnalysisChart();
        
        // 8. 性能热力图
        generatePerformanceHeatmap();
        
        // 9. 性能趋势图
        generateTrendAnalysisChart();
        
        // 10. 统计分布图
        generateStatisticalDistributionChart();
        
        // 生成Python可视化脚本
        generatePythonVisualizationScript();
        
        std::cout << "所有性能图表生成完成！" << std::endl;
        std::cout << "图表文件保存在: " << outputDirectory_ << std::endl;
        std::cout << "运行 'python " << pythonScriptPath_ << "' 生成最终图表" << std::endl;
    }
    
    /**
     * @brief 生成加速比对比图
     */
    void generateSpeedupComparisonChart() {
        ChartConfig config;
        config.type = ChartType::SPEEDUP_COMPARISON;
        config.title = "NTT Parallel Strategies Speedup Comparison";
        config.xlabel = "Problem Size (log scale)";
        config.ylabel = "Speedup (x)";
        config.filename = "speedup_comparison.png";
        
        auto dataSeries = extractSpeedupData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 加速比对比图已生成" << std::endl;
    }
    
    /**
     * @brief 生成并行效率分析图
     */
    void generateEfficiencyAnalysisChart() {
        ChartConfig config;
        config.type = ChartType::EFFICIENCY_ANALYSIS;
        config.title = "Parallel Efficiency Analysis";
        config.xlabel = "Number of Threads";
        config.ylabel = "Parallel Efficiency (%)";
        config.filename = "efficiency_analysis.png";
        
        auto dataSeries = extractEfficiencyData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 并行效率分析图已生成" << std::endl;
    }
    
    /**
     * @brief 生成可扩展性研究图
     */
    void generateScalabilityStudyChart() {
        ChartConfig config;
        config.type = ChartType::SCALABILITY_STUDY;
        config.title = "Scalability Study: Performance vs Problem Size";
        config.xlabel = "Problem Size";
        config.ylabel = "Execution Time (seconds)";
        config.filename = "scalability_study.png";
        
        auto dataSeries = extractScalabilityData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 可扩展性研究图已生成" << std::endl;
    }
    
    /**
     * @brief 生成吞吐量基准测试图
     */
    void generateThroughputBenchmarkChart() {
        ChartConfig config;
        config.type = ChartType::THROUGHPUT_BENCHMARK;
        config.title = "Throughput Benchmark Comparison";
        config.xlabel = "Parallel Strategy";
        config.ylabel = "Throughput (MB/s)";
        config.filename = "throughput_benchmark.png";
        
        auto dataSeries = extractThroughputData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 吞吐量基准测试图已生成" << std::endl;
    }
    
    /**
     * @brief 生成资源利用率图
     */
    void generateResourceUtilizationChart() {
        ChartConfig config;
        config.type = ChartType::RESOURCE_UTILIZATION;
        config.title = "Resource Utilization Analysis";
        config.xlabel = "Parallel Strategy";
        config.ylabel = "Utilization (%)";
        config.filename = "resource_utilization.png";
        
        auto dataSeries = extractResourceUtilizationData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 资源利用率图已生成" << std::endl;
    }
    
    /**
     * @brief 生成算法对比图
     */
    void generateAlgorithmComparisonChart() {
        ChartConfig config;
        config.type = ChartType::ALGORITHM_COMPARISON;
        config.title = "NTT Algorithm Variants Performance Comparison";
        config.xlabel = "Algorithm Variant";
        config.ylabel = "Relative Performance";
        config.filename = "algorithm_comparison.png";
        
        auto dataSeries = extractAlgorithmComparisonData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 算法对比图已生成" << std::endl;
    }
    
    /**
     * @brief 生成负载均衡分析图
     */
    void generateLoadBalanceAnalysisChart() {
        ChartConfig config;
        config.type = ChartType::LOAD_BALANCE_ANALYSIS;
        config.title = "Load Balance Analysis";
        config.xlabel = "Number of Workers";
        config.ylabel = "Load Balance Ratio";
        config.filename = "load_balance_analysis.png";
        
        auto dataSeries = extractLoadBalanceData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 负载均衡分析图已生成" << std::endl;
    }
    
    /**
     * @brief 生成性能热力图
     */
    void generatePerformanceHeatmap() {
        ChartConfig config;
        config.type = ChartType::PERFORMANCE_HEATMAP;
        config.title = "Performance Heatmap: Strategy vs Problem Size";
        config.xlabel = "Problem Size";
        config.ylabel = "Parallel Strategy";
        config.filename = "performance_heatmap.png";
        
        auto dataSeries = extractHeatmapData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 性能热力图已生成" << std::endl;
    }
    
    /**
     * @brief 生成性能趋势图
     */
    void generateTrendAnalysisChart() {
        ChartConfig config;
        config.type = ChartType::TREND_ANALYSIS;
        config.title = "Performance Trend Analysis Over Time";
        config.xlabel = "Test Number";
        config.ylabel = "Performance Score";
        config.filename = "trend_analysis.png";
        
        auto dataSeries = extractTrendData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 性能趋势图已生成" << std::endl;
    }
    
    /**
     * @brief 生成统计分布图
     */
    void generateStatisticalDistributionChart() {
        ChartConfig config;
        config.type = ChartType::STATISTICAL_DISTRIBUTION;
        config.title = "Performance Statistical Distribution";
        config.xlabel = "Speedup";
        config.ylabel = "Frequency";
        config.filename = "statistical_distribution.png";
        
        auto dataSeries = extractStatisticalDistributionData();
        generateChart(config, dataSeries);
        
        std::cout << "✓ 统计分布图已生成" << std::endl;
    }

private:
    /**
     * @brief 创建输出目录
     */
    void createOutputDirectory();
    
    /**
     * @brief 生成图表
     */
    void generateChart(const ChartConfig& config, const std::vector<DataSeries>& dataSeries);
    
    /**
     * @brief 提取加速比数据
     */
    std::vector<DataSeries> extractSpeedupData();
    
    /**
     * @brief 提取效率数据
     */
    std::vector<DataSeries> extractEfficiencyData();
    
    /**
     * @brief 提取可扩展性数据
     */
    std::vector<DataSeries> extractScalabilityData();
    
    /**
     * @brief 提取吞吐量数据
     */
    std::vector<DataSeries> extractThroughputData();
    
    /**
     * @brief 提取资源利用率数据
     */
    std::vector<DataSeries> extractResourceUtilizationData();
    
    /**
     * @brief 提取算法对比数据
     */
    std::vector<DataSeries> extractAlgorithmComparisonData();
    
    /**
     * @brief 提取负载均衡数据
     */
    std::vector<DataSeries> extractLoadBalanceData();
    
    /**
     * @brief 提取热力图数据
     */
    std::vector<DataSeries> extractHeatmapData();
    
    /**
     * @brief 提取趋势数据
     */
    std::vector<DataSeries> extractTrendData();
    
    /**
     * @brief 提取统计分布数据
     */
    std::vector<DataSeries> extractStatisticalDistributionData();
    
    /**
     * @brief 生成Python可视化脚本
     */
    void generatePythonVisualizationScript();
    
    /**
     * @brief 计算统计显著性
     */
    bool calculateStatisticalSignificance(const std::vector<double>& group1, 
                                         const std::vector<double>& group2,
                                         double alpha = 0.05);
    
    /**
     * @brief 获取策略名称
     */
    std::string getStrategyName(IntelligentParallelStrategy strategy);
    
    /**
     * @brief 获取算法变体名称
     */
    std::string getVariantName(IntelligentNTTVariant variant);
    
    /**
     * @brief 获取约简类型名称
     */
    std::string getReductionName(IntelligentReductionType reduction);
};

} // namespace HybridNTT
