#include "src/algorithms/complete_ntt_engine.hpp"
#include "src/core/simple_hardware_detector.hpp"
#include "src/core/simple_strategy_selector.hpp"
#include <iostream>
#include <vector>
#include <memory>

using namespace HybridNTT;

int main() {
    auto detector = std::make_shared<HardwareDetector>();
    auto selector = std::make_shared<StrategySelector>(detector);
    CompleteNTTEngine engine(detector, selector);

    std::vector<int> data = {1, 2, 3, 4};

    try {
        engine.split_radix_ntt(data, 998244353, false);
        std::cout << "split_radix_ntt works!" << std::endl;
    } catch (...) {
        std::cout << "split_radix_ntt failed!" << std::endl;
    }

    return 0;
}
