Found 30 test cases in nttdata/
Loaded test case 0: n=4, p=998244353
Loaded test case 1: n=8, p=1004535809
Loaded test case 2: n=16, p=469762049
Loaded test case 3: n=32, p=985661441
Loaded test case 4: n=64, p=998244353
Loaded test case 5: n=128, p=1004535809
Loaded test case 6: n=256, p=469762049
Loaded test case 7: n=512, p=998244353
Loaded test case 8: n=1024, p=1004535809
Loaded test case 9: n=2048, p=998244353
Loaded test case 10: n=4096, p=998244353
Loaded test case 11: n=8192, p=1004535809
Loaded test case 12: n=16384, p=998244353
Loaded test case 13: n=100, p=998244353
Loaded test case 14: n=1000, p=1004535809
Loaded test case 15: n=1, p=998244353
Loaded test case 16: n=2, p=1004535809
Loaded test case 17: n=3, p=469762049
Loaded test case 18: n=32768, p=998244353
Loaded test case 19: n=65536, p=998244353
Loaded test case 20: n=256, p=754974721
Loaded test case 21: n=1024, p=5767169
Loaded test case 22: n=256, p=998244353
Loaded test case 23: n=128, p=7340033
Loaded test case 24: n=64, p=5767169
Loaded test case 25: n=64, p=469762049
Loaded test case 26: n=64, p=985661441
Loaded test case 27: n=128, p=1004535809
Loaded test case 28: n=64, p=985661441
Loaded test case 29: n=64, p=1004535809

============================================================
           CORRECTNESS TESTING
============================================================

=== Testing Serial Radix-2 DIT ===
Test case 0: ✓ PASS (n=4, time=8μs)
Test case 1: ✓ PASS (n=8, time=11μs)
Test case 2: ✓ PASS (n=16, time=16μs)
Test case 3: ✓ PASS (n=32, time=27μs)
Test case 4: ✓ PASS (n=64, time=50μs)
Test case 5: ✓ PASS (n=128, time=101μs)
Test case 6: ✓ PASS (n=256, time=206μs)
Test case 7: ✗ FAIL (n=512, time=275μs)
Test case 8: ✗ FAIL (n=1024, time=548μs)
Test case 9: ✓ PASS (n=2048, time=2058μs)
Test case 10: ✓ PASS (n=4096, time=4131μs)
Test case 11: ✓ PASS (n=8192, time=12035μs)
Test case 12: ✓ PASS (n=16384, time=10834μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=622μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=21477μs)
Test case 19: ✓ PASS (n=65536, time=118304μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=623μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=115μs)
Test case 24: ✓ PASS (n=64, time=56μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=56μs)
Test case 29: ✓ PASS (n=64, time=53μs)

=== Testing Serial Radix-2 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=55μs)
Test case 5: ✓ PASS (n=128, time=117μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=305μs)
Test case 8: ✗ FAIL (n=1024, time=538μs)
Test case 9: ✓ PASS (n=2048, time=1331μs)
Test case 10: ✓ PASS (n=4096, time=2322μs)
Test case 11: ✓ PASS (n=8192, time=4467μs)
Test case 12: ✓ PASS (n=16384, time=8981μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=9μs)
Test case 18: ✓ PASS (n=32768, time=21566μs)
Test case 19: ✓ PASS (n=65536, time=35008μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=118μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing Serial Radix-4 DIT ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=621μs)
Test case 9: ✓ PASS (n=2048, time=1647μs)
Test case 10: ✓ PASS (n=4096, time=2307μs)
Test case 11: ✓ PASS (n=8192, time=4673μs)
Test case 12: ✓ PASS (n=16384, time=8981μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=630μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17733μs)
Test case 19: ✓ PASS (n=65536, time=34896μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=54μs)
Test case 26: ✓ PASS (n=64, time=53μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=54μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing Serial Radix-4 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=317μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1738μs)
Test case 10: ✓ PASS (n=4096, time=2325μs)
Test case 11: ✓ PASS (n=8192, time=4427μs)
Test case 12: ✓ PASS (n=16384, time=8785μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17677μs)
Test case 19: ✓ PASS (n=65536, time=35281μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing OpenMP Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=119μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1202μs)
Test case 10: ✓ PASS (n=4096, time=2625μs)
Test case 11: ✓ PASS (n=8192, time=4414μs)
Test case 12: ✓ PASS (n=16384, time=8803μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18159μs)
Test case 19: ✓ PASS (n=65536, time=35265μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=248μs)
Test case 23: ✓ PASS (n=128, time=115μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=54μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing OpenMP Task Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=117μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1850μs)
Test case 10: ✓ PASS (n=4096, time=2229μs)
Test case 11: ✓ PASS (n=8192, time=4465μs)
Test case 12: ✓ PASS (n=16384, time=8858μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=628μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17889μs)
Test case 19: ✓ PASS (n=65536, time=34997μs)
Test case 20: ✗ FAIL (n=256, time=246μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=54μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing SIMD AVX ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=13μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=317μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1182μs)
Test case 10: ✓ PASS (n=4096, time=2362μs)
Test case 11: ✓ PASS (n=8192, time=4439μs)
Test case 12: ✓ PASS (n=16384, time=8862μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18515μs)
Test case 19: ✓ PASS (n=65536, time=35090μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=619μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=54μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing SIMD NEON ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1169μs)
Test case 10: ✓ PASS (n=4096, time=2361μs)
Test case 11: ✓ PASS (n=8192, time=4403μs)
Test case 12: ✓ PASS (n=16384, time=9043μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=626μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17666μs)
Test case 19: ✓ PASS (n=65536, time=38020μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=619μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing pthread Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=54μs)
Test case 5: ✓ PASS (n=128, time=117μs)
Test case 6: ✓ PASS (n=256, time=245μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=626μs)
Test case 9: ✓ PASS (n=2048, time=1179μs)
Test case 10: ✓ PASS (n=4096, time=3338μs)
Test case 11: ✓ PASS (n=8192, time=4464μs)
Test case 12: ✓ PASS (n=16384, time=8923μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17621μs)
Test case 19: ✓ PASS (n=65536, time=35119μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=115μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=54μs)
Test case 26: ✓ PASS (n=64, time=53μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=54μs)
Test case 29: ✓ PASS (n=64, time=53μs)

=== Testing MPI Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1976μs)
Test case 10: ✓ PASS (n=4096, time=2312μs)
Test case 11: ✓ PASS (n=8192, time=4465μs)
Test case 12: ✓ PASS (n=16384, time=8960μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17736μs)
Test case 19: ✓ PASS (n=65536, time=35897μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=115μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=53μs)
Test case 27: ✓ PASS (n=128, time=118μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing Hybrid All ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=118μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1796μs)
Test case 10: ✓ PASS (n=4096, time=2343μs)
Test case 11: ✓ PASS (n=8192, time=4478μs)
Test case 12: ✓ PASS (n=16384, time=9007μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=21834μs)
Test case 19: ✓ PASS (n=65536, time=95553μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=625μs)
Test case 22: ✓ PASS (n=256, time=264μs)
Test case 23: ✓ PASS (n=128, time=128μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=118μs)
Test case 28: ✓ PASS (n=64, time=65μs)
Test case 29: ✓ PASS (n=64, time=52μs)

============================================================
CORRECTNESS TEST SUMMARY: 0/11 strategies passed
============================================================

============================================================
           PERFORMANCE BENCHMARK
============================================================
    Size    Strategy   Time(μs)     SpeedupThroughput(MB/s)
------------------------------------------------------------
     256      <USER>       <GROUP>.6        1.00            7.8
     256      OpenMP       207.0        1.21            9.4
     256    SIMD AVX       201.0        1.24            9.7
     256     pthread       196.8        1.27            9.9
     256      Hybrid       196.8        1.27            9.9
------------------------------------------------------------
     512      <USER>       <GROUP>.2        1.00           14.5
     512      OpenMP       268.0        1.00           14.6
     512    SIMD AVX       266.4        1.01           14.7
     512     pthread       266.6        1.01           14.7
     512      Hybrid       267.0        1.01           14.6
------------------------------------------------------------
    1024      <USER>       <GROUP>.0        1.00           14.5
    1024      OpenMP       536.2        1.00           14.6
    1024    SIMD AVX       538.0        1.00           14.5
    1024     pthread       538.6        1.00           14.5
    1024      Hybrid       536.2        1.00           14.6
------------------------------------------------------------
    2048      <USER>     <GROUP>.0        1.00            1.3
    2048      OpenMP      1181.4        9.85           13.2
    2048    SIMD AVX      1231.4        9.45           12.7
    2048     pthread      1161.8       10.02           13.4
    2048      Hybrid      1181.6        9.85           13.2
------------------------------------------------------------
    4096      <USER>      <GROUP>.4        1.00           14.0
    4096      OpenMP      2240.4        1.00           13.9
    4096    SIMD AVX      2241.4        1.00           13.9
    4096     pthread      2290.6        0.98           13.6
    4096      Hybrid      2474.0        0.90           12.6
------------------------------------------------------------
    8192      <USER>      <GROUP>.4        1.00           14.1
    8192      OpenMP      4470.6        0.99           14.0
    8192    SIMD AVX      4413.6        1.00           14.2
    8192     pthread      4424.0        1.00           14.1
    8192      Hybrid      4424.4        1.00           14.1
------------------------------------------------------------
   16384      <USER>      <GROUP>.0        1.00           14.2
   16384      OpenMP      9162.8        0.96           13.6
   16384    SIMD AVX      8856.6        1.00           14.1
   16384     pthread      8885.0        0.99           14.1
   16384      Hybrid      8818.4        1.00           14.2
------------------------------------------------------------
    1000      <USER>       <GROUP>.8        1.00           12.3
    1000      OpenMP       552.2        1.12           13.8
    1000    SIMD AVX       536.2        1.16           14.2
    1000     pthread       536.4        1.16           14.2
    1000      Hybrid       536.2        1.16           14.2
------------------------------------------------------------
   32768      <USER>     <GROUP>.0        1.00           13.2
   32768      OpenMP     20922.8        0.91           11.9
   32768    SIMD AVX     19543.2        0.97           12.8
   32768     pthread     19194.6        0.99           13.0
   32768      Hybrid     18183.6        1.04           13.7
------------------------------------------------------------
   65536      <USER>     <GROUP>.8        1.00           11.4
   65536      OpenMP     35557.6        1.23           14.1
   65536    SIMD AVX     41221.0        1.06           12.1
   65536     pthread     37585.2        1.16           13.3
   65536      Hybrid     39004.8        1.12           12.8
------------------------------------------------------------
     256      <USER>       <GROUP>.6        1.00            8.3
     256      OpenMP       232.0        1.02            8.4
     256    SIMD AVX       234.2        1.01            8.3
     256     pthread       198.8        1.19            9.8
     256      Hybrid       196.8        1.20            9.9
------------------------------------------------------------
    1024      <USER>       <GROUP>.6        1.00           14.7
    1024      OpenMP       531.4        1.00           14.7
    1024    SIMD AVX       532.6        1.00           14.7
    1024     pthread       534.4        1.00           14.6
    1024      Hybrid       531.8        1.00           14.7
------------------------------------------------------------
     256      <USER>       <GROUP>.2        1.00            9.8
     256      OpenMP       198.0        1.01            9.9
     256    SIMD AVX       197.8        1.01            9.9
     256     pthread       197.8        1.01            9.9
     256      Hybrid       198.2        1.01            9.9
------------------------------------------------------------

All tests completed successfully!
