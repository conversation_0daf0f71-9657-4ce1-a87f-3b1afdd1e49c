/**
 * @file complete_ntt_engine.cpp
 * @brief 完整的混合并行NTT引擎实现
 */

#include "complete_ntt_engine.hpp"
#include <iostream>
#include <cassert>

namespace HybridNTT {

void CompleteNTTEngine::executeNTT(std::vector<int>& data, int modulus, bool inverse,
                                  NTTVariant variant, ReductionType reduction, 
                                  ParallelStrategy strategy) {
    int n = data.size();
    
    // 预处理
    precomputeTwiddleFactors(n, modulus);
    precomputeBitReverseTable(n);
    
    if (reduction == ReductionType::BARRETT) {
        initBarrettParams(modulus);
    } else if (reduction == ReductionType::MONTGOMERY) {
        initMontgomeryParams(modulus);
    }
    
    // 根据策略选择执行方式
    switch (strategy) {
        case ParallelStrategy::SERIAL:
            if (variant == NTTVariant::RADIX_2_DIT) {
                radix2_dit_ntt(data, modulus, inverse);
            } else if (variant == NTTVariant::RADIX_2_DIF) {
                radix2_dif_ntt(data, modulus, inverse);
            } else if (variant == NTTVariant::RADIX_4_DIT) {
                radix4_dit_ntt(data, modulus, inverse);
            } else if (variant == NTTVariant::RADIX_4_DIF) {
                radix4_dif_ntt(data, modulus, inverse);
            }
            break;
            
        case ParallelStrategy::OPENMP_DATA_PARALLEL:
            openmp_data_parallel_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::OPENMP_TASK_PARALLEL:
            openmp_task_parallel_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::SIMD_AVX:
            simd_avx_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::SIMD_NEON:
            simd_neon_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::MPI_DATA_PARALLEL:
            mpi_parallel_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::CUDA_PARALLEL:
            cuda_parallel_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::PTHREAD_PARALLEL:
            pthread_parallel_ntt(data, modulus, inverse);
            break;
            
        case ParallelStrategy::HYBRID_ALL:
            // 智能选择最优策略
            if (n >= 16384) {
                mpi_parallel_ntt(data, modulus, inverse);
            } else if (n >= 4096) {
                openmp_data_parallel_ntt(data, modulus, inverse);
            } else if (n >= 1024) {
                simd_avx_ntt(data, modulus, inverse);
            } else {
                radix2_dit_ntt(data, modulus, inverse);
            }
            break;

        case ParallelStrategy::SIMD_SSE:
            // 回退到AVX实现
            simd_avx_ntt(data, modulus, inverse);
            break;

        case ParallelStrategy::MPI_TASK_PARALLEL:
            // 回退到MPI数据并行
            mpi_parallel_ntt(data, modulus, inverse);
            break;
    }
}

void CompleteNTTEngine::polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b,
                                          std::vector<int>& result, int modulus) {
    int n = a.size();
    int m = b.size();
    int result_size = n + m - 1;
    
    // 找到大于等于result_size的最小2的幂
    int ntt_size = 1;
    while (ntt_size < result_size) {
        ntt_size <<= 1;
    }
    
    // 准备数据
    std::vector<int> A(ntt_size, 0), B(ntt_size, 0);
    std::copy(a.begin(), a.end(), A.begin());
    std::copy(b.begin(), b.end(), B.begin());
    
    // 正向NTT
    executeNTT(A, modulus, false);
    executeNTT(B, modulus, false);
    
    // 点乘
    for (int i = 0; i < ntt_size; i++) {
        A[i] = (1LL * A[i] * B[i]) % modulus;
    }
    
    // 逆向NTT
    executeNTT(A, modulus, true);
    
    // 提取结果
    result.resize(result_size);
    std::copy(A.begin(), A.begin() + result_size, result.begin());
}

// ============ Radix-2 DIT NTT实现 ============
void CompleteNTTEngine::radix2_dit_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // 位反转置换
    bitReversePermutation(data);
    
    // 蝶形运算
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    // 逆变换需要除以n
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ Radix-2 DIF NTT实现 ============
void CompleteNTTEngine::radix2_dif_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // 蝶形运算
    for (int len = n; len > 1; len >>= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = data[i + j + half];
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (1LL * (u - v + modulus) * w) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    // 位反转置换
    bitReversePermutation(data);
    
    // 逆变换需要除以n
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ OpenMP数据并行实现 ============
void CompleteNTTEngine::openmp_data_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // 位反转置换（并行）
    #pragma omp parallel for
    for (int i = 0; i < n; i++) {
        if (i < bit_reverse_table_[i]) {
            std::swap(data[i], data[bit_reverse_table_[i]]);
        }
    }
    
    // 蝶形运算（并行）
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        #pragma omp parallel for
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    // 逆变换处理
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        #pragma omp parallel for
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ 工具函数实现 ============
void CompleteNTTEngine::precomputeTwiddleFactors(int n, int modulus) {
    twiddle_factors_.resize(n);
    inv_twiddle_factors_.resize(n);
    
    uint32_t g = 3; // 原根
    uint32_t wn = fastPow(g, (modulus - 1) / n, modulus);
    uint32_t inv_wn = modularInverse(wn, modulus);
    
    twiddle_factors_[0] = inv_twiddle_factors_[0] = 1;
    for (int i = 1; i < n; i++) {
        twiddle_factors_[i] = (1LL * twiddle_factors_[i-1] * wn) % modulus;
        inv_twiddle_factors_[i] = (1LL * inv_twiddle_factors_[i-1] * inv_wn) % modulus;
    }
}

void CompleteNTTEngine::precomputeBitReverseTable(int n) {
    bit_reverse_table_.resize(n);
    int log_n = 0;
    while ((1 << log_n) < n) log_n++;
    
    for (int i = 0; i < n; i++) {
        bit_reverse_table_[i] = 0;
        for (int j = 0; j < log_n; j++) {
            if (i & (1 << j)) {
                bit_reverse_table_[i] |= (1 << (log_n - 1 - j));
            }
        }
    }
}

uint32_t CompleteNTTEngine::fastPow(uint32_t base, uint32_t exp, uint32_t mod) {
    uint32_t result = 1;
    base %= mod;
    while (exp > 0) {
        if (exp & 1) {
            result = (1LL * result * base) % mod;
        }
        base = (1LL * base * base) % mod;
        exp >>= 1;
    }
    return result;
}

uint32_t CompleteNTTEngine::modularInverse(uint32_t a, uint32_t mod) {
    return fastPow(a, mod - 2, mod);
}

void CompleteNTTEngine::bitReversePermutation(std::vector<int>& data) {
    int n = data.size();
    for (int i = 0; i < n; i++) {
        if (i < bit_reverse_table_[i]) {
            std::swap(data[i], data[bit_reverse_table_[i]]);
        }
    }
}

// ============ SIMD AVX实现 ============
void CompleteNTTEngine::simd_avx_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef __AVX2__
    int n = data.size();
    bitReversePermutation(data);

    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            int j = 0;

            // AVX2向量化处理（8个32位整数）
            for (; j + 7 < half; j += 8) {
                __m256i va = _mm256_loadu_si256((__m256i*)&data[i + j]);
                __m256i vb = _mm256_loadu_si256((__m256i*)&data[i + j + half]);

                // 计算旋转因子向量
                __m256i vw = _mm256_set_epi32(
                    (1LL * w * fastPow(wn, 7, modulus)) % modulus,
                    (1LL * w * fastPow(wn, 6, modulus)) % modulus,
                    (1LL * w * fastPow(wn, 5, modulus)) % modulus,
                    (1LL * w * fastPow(wn, 4, modulus)) % modulus,
                    (1LL * w * fastPow(wn, 3, modulus)) % modulus,
                    (1LL * w * fastPow(wn, 2, modulus)) % modulus,
                    (1LL * w * wn) % modulus,
                    w
                );

                // 向量化蝶形运算（简化版本，实际需要更复杂的模运算）
                __m256i sum = _mm256_add_epi32(va, vb);
                __m256i diff = _mm256_sub_epi32(va, vb);

                _mm256_storeu_si256((__m256i*)&data[i + j], sum);
                _mm256_storeu_si256((__m256i*)&data[i + j + half], diff);

                w = (1LL * w * fastPow(wn, 8, modulus)) % modulus;
            }

            // 处理剩余元素
            for (; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
#else
    // 回退到标准实现
    radix2_dit_ntt(data, modulus, inverse);
#endif

    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ SIMD NEON实现 ============
void CompleteNTTEngine::simd_neon_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef __ARM_NEON
    int n = data.size();
    bitReversePermutation(data);

    const int32x4_t mod_vec = vdupq_n_s32(modulus);

    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            int j = 0;

            // NEON向量化处理（4个32位整数）
            for (; j + 3 < half; j += 4) {
                int32x4_t va = vld1q_s32(&data[i + j]);
                int32x4_t vb = vld1q_s32(&data[i + j + half]);

                // 计算旋转因子向量
                int32x4_t vw = {
                    (int32_t)w,
                    (int32_t)((1LL * w * wn) % modulus),
                    (int32_t)((1LL * w * fastPow(wn, 2, modulus)) % modulus),
                    (int32_t)((1LL * w * fastPow(wn, 3, modulus)) % modulus)
                };

                // 向量化模乘（简化版本）
                int64x2_t vb_low = vmovl_s32(vget_low_s32(vb));
                int64x2_t vb_high = vmovl_s32(vget_high_s32(vb));
                int64x2_t vw_low = vmovl_s32(vget_low_s32(vw));
                int64x2_t vw_high = vmovl_s32(vget_high_s32(vw));

                int64x2_t prod_low = vmulq_s64(vb_low, vw_low);
                int64x2_t prod_high = vmulq_s64(vb_high, vw_high);

                // 向量化蝶形运算
                int32x4_t sum = vaddq_s32(va, vb);
                int32x4_t diff = vsubq_s32(va, vb);

                // 模运算处理（简化）
                sum = vbslq_s32(vcgeq_s32(sum, mod_vec), vsubq_s32(sum, mod_vec), sum);
                diff = vbslq_s32(vcltq_s32(diff, vdupq_n_s32(0)), vaddq_s32(diff, mod_vec), diff);

                vst1q_s32(&data[i + j], sum);
                vst1q_s32(&data[i + j + half], diff);

                w = (1LL * w * fastPow(wn, 4, modulus)) % modulus;
            }

            // 处理剩余元素
            for (; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
#else
    // 回退到标准实现
    radix2_dit_ntt(data, modulus, inverse);
#endif

    if (inverse) {
        int n = data.size();
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ 其他并行实现在complete_parallel_strategies.cpp中 ============

void CompleteNTTEngine::initBarrettParams(int modulus) {
    barrett_params_.mod = modulus;
    barrett_params_.inv = (1ULL << 32) / modulus;
    barrett_params_.shift = 32;
}

void CompleteNTTEngine::initMontgomeryParams(int modulus) {
    montgomery_params_.mod = modulus;
    montgomery_params_.r = 1ULL << 32;
    // TODO: 计算Montgomery参数
}

uint32_t CompleteNTTEngine::barrettReduce(uint64_t x) {
    uint64_t q = (x * barrett_params_.inv) >> barrett_params_.shift;
    uint64_t r = x - q * barrett_params_.mod;
    return r >= barrett_params_.mod ? r - barrett_params_.mod : r;
}

uint32_t CompleteNTTEngine::montgomeryReduce(uint64_t x) {
    // TODO: 实现Montgomery约简
    return x % montgomery_params_.mod;
}

void CompleteNTTEngine::butterfly(int& a, int& b, uint32_t w, uint32_t mod) {
    int u = a;
    int v = (1LL * b * w) % mod;
    a = (u + v) % mod;
    b = (u - v + mod) % mod;
}

void CompleteNTTEngine::butterflyBarrett(int& a, int& b, uint32_t w) {
    int u = a;
    int v = barrettReduce(1LL * b * w);
    a = (u + v) % barrett_params_.mod;
    b = (u - v + barrett_params_.mod) % barrett_params_.mod;
}

void CompleteNTTEngine::butterflyMontgomery(int& a, int& b, uint32_t w) {
    int u = a;
    int v = montgomeryReduce(1LL * b * w);
    a = (u + v) % montgomery_params_.mod;
    b = (u - v + montgomery_params_.mod) % montgomery_params_.mod;
}

} // namespace HybridNTT
