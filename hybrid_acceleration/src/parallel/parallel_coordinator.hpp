/**
 * @file parallel_coordinator.hpp
 * @brief 高级并行协调器，实现多层次混合并行调度
 * <AUTHOR> Acceleration Framework
 */

#ifndef PARALLEL_COORDINATOR_HPP
#define PARALLEL_COORDINATOR_HPP

#include "../core/hardware_detector.hpp"
#include "../core/strategy_selector.hpp"
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <queue>
#include <functional>

#ifdef _OPENMP
#include <omp.h>
#endif

#ifdef MPI_VERSION
#include <mpi.h>
#endif

#ifdef __CUDA_ARCH__
#include <cuda_runtime.h>
#endif

namespace HybridNTT {

/**
 * @brief 计算任务类型枚举
 */
enum class TaskType {
    NTT_FORWARD,
    NTT_INVERSE,
    POINTWISE_MULTIPLY,
    DATA_TRANSFER,
    SYNCHRONIZATION
};

/**
 * @brief 并行计算任务
 */
struct ParallelTask {
    TaskType type;
    std::vector<int>* data;
    int start_index;
    int length;
    int level;
    uint32_t modulus;
    std::function<void()> callback;
    
    ParallelTask(TaskType t, std::vector<int>* d, int start, int len, int lv, uint32_t mod)
        : type(t), data(d), start_index(start), length(len), level(lv), modulus(mod) {}
};

/**
 * @brief 线程安全的任务队列
 */
class ThreadSafeTaskQueue {
private:
    std::queue<ParallelTask> tasks_;
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    
public:
    void push(ParallelTask&& task) {
        std::lock_guard<std::mutex> lock(mutex_);
        tasks_.push(std::move(task));
        condition_.notify_one();
    }
    
    bool pop(ParallelTask& task, std::chrono::milliseconds timeout = std::chrono::milliseconds(100)) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (condition_.wait_for(lock, timeout, [this] { return !tasks_.empty(); })) {
            task = std::move(tasks_.front());
            tasks_.pop();
            return true;
        }
        return false;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return tasks_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return tasks_.empty();
    }
};

/**
 * @brief 并行协调器类
 */
class ParallelCoordinator {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    OptimizationStrategy strategy_;
    
    // 线程池管理
    std::vector<std::thread> workerThreads_;
    ThreadSafeTaskQueue taskQueue_;
    std::atomic<bool> shouldStop_{false};
    
    // MPI相关
    int mpiRank_ = 0;
    int mpiSize_ = 1;
    bool mpiInitialized_ = false;
    
    // CUDA相关
    bool cudaAvailable_ = false;
    int cudaDeviceCount_ = 0;
    
    // 性能监控
    mutable std::mutex perfMutex_;
    std::unordered_map<std::string, double> performanceCounters_;
    
    /**
     * @brief 工作线程函数
     */
    void workerThreadFunction(int threadId) {
        ParallelTask task(TaskType::NTT_FORWARD, nullptr, 0, 0, 0, 0);
        
        while (!shouldStop_.load()) {
            if (taskQueue_.pop(task)) {
                executeTask(task, threadId);
            }
        }
    }
    
    /**
     * @brief 执行单个任务
     */
    void executeTask(const ParallelTask& task, int threadId) {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        switch (task.type) {
            case TaskType::NTT_FORWARD:
                executeNTTSegment(task, false);
                break;
            case TaskType::NTT_INVERSE:
                executeNTTSegment(task, true);
                break;
            case TaskType::POINTWISE_MULTIPLY:
                executePointwiseMultiply(task);
                break;
            case TaskType::DATA_TRANSFER:
                executeDataTransfer(task);
                break;
            case TaskType::SYNCHRONIZATION:
                executeSynchronization(task);
                break;
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        double execTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();
        
        // 更新性能计数器
        std::lock_guard<std::mutex> lock(perfMutex_);
        performanceCounters_["task_execution_time"] += execTime;
        performanceCounters_["task_count"]++;
        
        if (task.callback) {
            task.callback();
        }
    }
    
    /**
     * @brief 执行NTT段计算
     */
    void executeNTTSegment(const ParallelTask& task, bool is_inverse) {
        if (!task.data || task.length <= 0) return;
        
        // 根据任务大小选择最优执行策略
        if (task.length <= 64) {
            executeSerialNTTSegment(*task.data, task.start_index, task.length, is_inverse, task.modulus);
        } else if (task.length <= 1024 && strategy_.vectorWidth >= 8) {
            executeSIMDNTTSegment(*task.data, task.start_index, task.length, is_inverse, task.modulus);
        } else if (cudaAvailable_ && task.length >= 4096) {
            executeCUDANTTSegment(*task.data, task.start_index, task.length, is_inverse, task.modulus);
        } else {
            executeOpenMPNTTSegment(*task.data, task.start_index, task.length, is_inverse, task.modulus);
        }
    }
    
    /**
     * @brief 串行NTT段执行
     */
    void executeSerialNTTSegment(std::vector<int>& data, int start, int length, bool is_inverse, uint32_t modulus);
    
    /**
     * @brief SIMD NTT段执行
     */
    void executeSIMDNTTSegment(std::vector<int>& data, int start, int length, bool is_inverse, uint32_t modulus);
    
    /**
     * @brief CUDA NTT段执行
     */
    void executeCUDANTTSegment(std::vector<int>& data, int start, int length, bool is_inverse, uint32_t modulus);
    
    /**
     * @brief OpenMP NTT段执行
     */
    void executeOpenMPNTTSegment(std::vector<int>& data, int start, int length, bool is_inverse, uint32_t modulus);
    
    /**
     * @brief 执行点乘操作
     */
    void executePointwiseMultiply(const ParallelTask& task);
    
    /**
     * @brief 执行数据传输
     */
    void executeDataTransfer(const ParallelTask& task);
    
    /**
     * @brief 执行同步操作
     */
    void executeSynchronization(const ParallelTask& task);

public:
    /**
     * @brief 构造函数
     */
    explicit ParallelCoordinator(std::shared_ptr<HardwareDetector> detector)
        : hardwareDetector_(detector) {
        initializeEnvironment();
    }
    
    /**
     * @brief 析构函数
     */
    ~ParallelCoordinator() {
        shutdown();
    }
    
    /**
     * @brief 初始化并行环境
     */
    void initializeEnvironment();
    
    /**
     * @brief 设置优化策略
     */
    void setStrategy(const OptimizationStrategy& strategy) {
        strategy_ = strategy;
        adjustThreadPool();
    }
    
    /**
     * @brief 调整线程池大小
     */
    void adjustThreadPool();
    
    /**
     * @brief 提交并行NTT任务
     */
    void submitNTTTask(std::vector<int>& data, int start, int length, bool is_inverse, uint32_t modulus);
    
    /**
     * @brief 等待所有任务完成
     */
    void waitForCompletion();
    
    /**
     * @brief 关闭协调器
     */
    void shutdown();
    
    /**
     * @brief 获取性能统计
     */
    std::unordered_map<std::string, double> getPerformanceStats() const {
        std::lock_guard<std::mutex> lock(perfMutex_);
        return performanceCounters_;
    }
};

} // namespace HybridNTT

#endif // PARALLEL_COORDINATOR_HPP
