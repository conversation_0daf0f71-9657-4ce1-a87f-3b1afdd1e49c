Found 30 test cases in nttdata/
Loaded test case 0: n=4, p=998244353
Loaded test case 1: n=8, p=1004535809
Loaded test case 2: n=16, p=469762049
Loaded test case 3: n=32, p=985661441
Loaded test case 4: n=64, p=998244353
Loaded test case 5: n=128, p=1004535809
Loaded test case 6: n=256, p=469762049
Loaded test case 7: n=512, p=998244353
Loaded test case 8: n=1024, p=1004535809
Loaded test case 9: n=2048, p=998244353
Loaded test case 10: n=4096, p=998244353
Loaded test case 11: n=8192, p=1004535809
Loaded test case 12: n=16384, p=998244353
Loaded test case 13: n=100, p=998244353
Loaded test case 14: n=1000, p=1004535809
Loaded test case 15: n=1, p=998244353
Loaded test case 16: n=2, p=1004535809
Loaded test case 17: n=3, p=469762049
Loaded test case 18: n=32768, p=998244353
Loaded test case 19: n=65536, p=998244353
Loaded test case 20: n=256, p=754974721
Loaded test case 21: n=1024, p=5767169
Loaded test case 22: n=256, p=998244353
Loaded test case 23: n=128, p=7340033
Loaded test case 24: n=64, p=5767169
Loaded test case 25: n=64, p=469762049
Loaded test case 26: n=64, p=985661441
Loaded test case 27: n=128, p=1004535809
Loaded test case 28: n=64, p=985661441
Loaded test case 29: n=64, p=1004535809

============================================================
           CORRECTNESS TESTING
============================================================

=== Testing Serial Radix-2 DIT ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=11μs)
Test case 2: ✓ PASS (n=16, time=16μs)
Test case 3: ✓ PASS (n=32, time=27μs)
Test case 4: ✓ PASS (n=64, time=50μs)
Test case 5: ✓ PASS (n=128, time=108μs)
Test case 6: ✓ PASS (n=256, time=207μs)
Test case 7: ✗ FAIL (n=512, time=289μs)
Test case 8: ✗ FAIL (n=1024, time=542μs)
Test case 9: ✓ PASS (n=2048, time=2423μs)
Test case 10: ✓ PASS (n=4096, time=3606μs)
Test case 11: ✓ PASS (n=8192, time=7430μs)
Test case 12: ✓ PASS (n=16384, time=12265μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=23277μs)
Test case 19: ✓ PASS (n=65536, time=36030μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=619μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Radix-2 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=332μs)
Test case 8: ✗ FAIL (n=1024, time=547μs)
Test case 9: ✓ PASS (n=2048, time=1155μs)
Test case 10: ✓ PASS (n=4096, time=2204μs)
Test case 11: ✓ PASS (n=8192, time=4725μs)
Test case 12: ✓ PASS (n=16384, time=8866μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18275μs)
Test case 19: ✓ PASS (n=65536, time=35456μs)
Test case 20: ✗ FAIL (n=256, time=246μs)
Test case 21: ✗ FAIL (n=1024, time=622μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=111μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Radix-4 DIT ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=247μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=627μs)
Test case 9: ✓ PASS (n=2048, time=60259μs)
Test case 10: ✓ PASS (n=4096, time=2220μs)
Test case 11: ✓ PASS (n=8192, time=4401μs)
Test case 12: ✓ PASS (n=16384, time=8829μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=626μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17812μs)
Test case 19: ✓ PASS (n=65536, time=35251μs)
Test case 20: ✗ FAIL (n=256, time=243μs)
Test case 21: ✗ FAIL (n=1024, time=619μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Radix-4 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=17μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=247μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=573μs)
Test case 9: ✓ PASS (n=2048, time=1230μs)
Test case 10: ✓ PASS (n=4096, time=2289μs)
Test case 11: ✓ PASS (n=8192, time=4476μs)
Test case 12: ✓ PASS (n=16384, time=8922μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18337μs)
Test case 19: ✓ PASS (n=65536, time=36744μs)
Test case 20: ✗ FAIL (n=256, time=243μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=114μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Split-Radix ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=13μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=588μs)
Test case 9: ✓ PASS (n=2048, time=1153μs)
Test case 10: ✓ PASS (n=4096, time=2235μs)
Test case 11: ✓ PASS (n=8192, time=4393μs)
Test case 12: ✓ PASS (n=16384, time=8827μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17716μs)
Test case 19: ✓ PASS (n=65536, time=35129μs)
Test case 20: ✗ FAIL (n=256, time=246μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=115μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Cache-Oblivious ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=114μs)
Test case 6: ✓ PASS (n=256, time=245μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=626μs)
Test case 9: ✓ PASS (n=2048, time=1171μs)
Test case 10: ✓ PASS (n=4096, time=2224μs)
Test case 11: ✓ PASS (n=8192, time=4449μs)
Test case 12: ✓ PASS (n=16384, time=8859μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=630μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17954μs)
Test case 19: ✓ PASS (n=65536, time=35318μs)
Test case 20: ✗ FAIL (n=256, time=246μs)
Test case 21: ✗ FAIL (n=1024, time=622μs)
Test case 22: ✓ PASS (n=256, time=248μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=51μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing OpenMP Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=111μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=626μs)
Test case 9: ✓ PASS (n=2048, time=1178μs)
Test case 10: ✓ PASS (n=4096, time=2268μs)
Test case 11: ✓ PASS (n=8192, time=4420μs)
Test case 12: ✓ PASS (n=16384, time=8933μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17900μs)
Test case 19: ✓ PASS (n=65536, time=38758μs)
Test case 20: ✗ FAIL (n=256, time=253μs)
Test case 21: ✗ FAIL (n=1024, time=630μs)
Test case 22: ✓ PASS (n=256, time=250μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=111μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing OpenMP Task Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=116μs)
Test case 6: ✓ PASS (n=256, time=250μs)
Test case 7: ✗ FAIL (n=512, time=320μs)
Test case 8: ✗ FAIL (n=1024, time=631μs)
Test case 9: ✓ PASS (n=2048, time=1202μs)
Test case 10: ✓ PASS (n=4096, time=2275μs)
Test case 11: ✓ PASS (n=8192, time=4582μs)
Test case 12: ✓ PASS (n=16384, time=8988μs)
Test case 13: ✓ PASS (n=100, time=125μs)
Test case 14: ✗ FAIL (n=1000, time=634μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=47161μs)
Test case 19: ✓ PASS (n=65536, time=35588μs)
Test case 20: ✗ FAIL (n=256, time=247μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing OpenMP Split-Radix ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=51μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=625μs)
Test case 9: ✓ PASS (n=2048, time=1173μs)
Test case 10: ✓ PASS (n=4096, time=2249μs)
Test case 11: ✓ PASS (n=8192, time=4403μs)
Test case 12: ✓ PASS (n=16384, time=8857μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=626μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=24227μs)
Test case 19: ✓ PASS (n=65536, time=38209μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=624μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing SIMD AVX ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=625μs)
Test case 9: ✓ PASS (n=2048, time=1212μs)
Test case 10: ✓ PASS (n=4096, time=2209μs)
Test case 11: ✓ PASS (n=8192, time=4382μs)
Test case 12: ✓ PASS (n=16384, time=11881μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=629μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17745μs)
Test case 19: ✓ PASS (n=65536, time=35079μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=246μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=111μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing SIMD NEON ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=111μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=625μs)
Test case 9: ✓ PASS (n=2048, time=32651μs)
Test case 10: ✓ PASS (n=4096, time=2238μs)
Test case 11: ✓ PASS (n=8192, time=4452μs)
Test case 12: ✓ PASS (n=16384, time=8884μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=632μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18710μs)
Test case 19: ✓ PASS (n=65536, time=46783μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=116μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing pthread Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=543μs)
Test case 9: ✓ PASS (n=2048, time=1845μs)
Test case 10: ✓ PASS (n=4096, time=2219μs)
Test case 11: ✓ PASS (n=8192, time=4389μs)
Test case 12: ✓ PASS (n=16384, time=8792μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=626μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=9μs)
Test case 18: ✓ PASS (n=32768, time=21701μs)
Test case 19: ✓ PASS (n=65536, time=38308μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing pthread Work-Stealing ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=51μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=625μs)
Test case 9: ✓ PASS (n=2048, time=1281μs)
Test case 10: ✓ PASS (n=4096, time=2185μs)
Test case 11: ✓ PASS (n=8192, time=4402μs)
Test case 12: ✓ PASS (n=16384, time=9007μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=20767μs)
Test case 19: ✓ PASS (n=65536, time=35290μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing MPI Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=51μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=320μs)
Test case 8: ✗ FAIL (n=1024, time=623μs)
Test case 9: ✓ PASS (n=2048, time=1168μs)
Test case 10: ✓ PASS (n=4096, time=2242μs)
Test case 11: ✓ PASS (n=8192, time=4385μs)
Test case 12: ✓ PASS (n=16384, time=8783μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=21064μs)
Test case 19: ✓ PASS (n=65536, time=56491μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=622μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=113μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Hybrid All ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=51μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=624μs)
Test case 9: ✓ PASS (n=2048, time=47621μs)
Test case 10: ✓ PASS (n=4096, time=2190μs)
Test case 11: ✓ PASS (n=8192, time=4381μs)
Test case 12: ✓ PASS (n=16384, time=8782μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=628μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17814μs)
Test case 19: ✓ PASS (n=65536, time=34879μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing CRT Multi-Modulus ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=17μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=111μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=316μs)
Test case 8: ✗ FAIL (n=1024, time=625μs)
Test case 9: ✓ PASS (n=2048, time=1206μs)
Test case 10: ✓ PASS (n=4096, time=2277μs)
Test case 11: ✓ PASS (n=8192, time=5261μs)
Test case 12: ✓ PASS (n=16384, time=8807μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17623μs)
Test case 19: ✓ PASS (n=65536, time=34715μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=111μs)
Test case 28: ✓ PASS (n=64, time=51μs)
Test case 29: ✓ PASS (n=64, time=51μs)

============================================================
CORRECTNESS TEST SUMMARY: 0/16 strategies passed
============================================================

============================================================
           PERFORMANCE BENCHMARK
============================================================
    Size    Strategy   Time(μs)     SpeedupThroughput(MB/s)
------------------------------------------------------------
     256      <USER>       <GROUP>.8        1.00            8.3
     256      OpenMP       211.6        1.11            9.2
     256    SIMD AVX       190.2        1.23           10.3
     256     pthread       191.2        1.23           10.2
     256      Hybrid       190.8        1.23           10.2
------------------------------------------------------------
     512      <USER>       <GROUP>.4        1.00           14.7
     512      OpenMP       264.6        1.01           14.8
     512    SIMD AVX       264.6        1.01           14.8
     512     pthread       264.2        1.01           14.8
     512      Hybrid       264.4        1.01           14.8
------------------------------------------------------------
    1024      <USER>       <GROUP>.6        1.00           14.6
    1024      OpenMP       533.8        1.00           14.6
    1024    SIMD AVX       532.8        1.00           14.7
    1024     pthread       533.6        1.00           14.6
    1024      Hybrid       533.6        1.00           14.6
------------------------------------------------------------
    2048      <USER>      <GROUP>.4        1.00            9.8
    2048      OpenMP      1380.0        1.16           11.3
    2048    SIMD AVX      1325.0        1.20           11.8
    2048     pthread      1171.2        1.36           13.3
    2048      Hybrid      1159.8        1.38           13.5
------------------------------------------------------------
    4096      <USER>      <GROUP>.6        1.00           14.0
    4096      OpenMP      2298.0        0.97           13.6
    4096    SIMD AVX      2227.6        1.01           14.0
    4096     pthread      2241.8        1.00           13.9
    4096      Hybrid      2239.8        1.00           14.0
------------------------------------------------------------
    8192      <USER>      <GROUP>.2        1.00           14.1
    8192      OpenMP      4388.6        1.01           14.2
    8192    SIMD AVX      4437.8        1.00           14.1
    8192     pthread      4499.6        0.98           13.9
    8192      Hybrid      4399.4        1.01           14.2
------------------------------------------------------------
   16384      <USER>      <GROUP>.6        1.00           13.8
   16384      OpenMP      9942.2        0.91           12.6
   16384    SIMD AVX      8873.4        1.02           14.1
   16384     pthread     10707.4        0.85           11.7
   16384      Hybrid      9585.6        0.95           13.0
------------------------------------------------------------
    1000      <USER>       <GROUP>.2        1.00           12.2
    1000      OpenMP       550.8        1.13           13.9
    1000    SIMD AVX       532.6        1.17           14.3
    1000     pthread       533.2        1.17           14.3
    1000      Hybrid       532.2        1.17           14.3
------------------------------------------------------------
   32768      <USER>     <GROUP>.4        1.00           11.1
   32768      OpenMP     20600.0        1.10           12.1
   32768    SIMD AVX     27469.8        0.82            9.1
   32768     pthread     19466.4        1.16           12.8
   32768      Hybrid     20070.2        1.12           12.5
------------------------------------------------------------
   65536      <USER>     <GROUP>.8        1.00           14.0
   65536      OpenMP     35395.2        1.01           14.1
   65536    SIMD AVX     45221.0        0.79           11.1
   65536     pthread     46123.4        0.77           10.8
   65536      Hybrid     35643.6        1.00           14.0
------------------------------------------------------------
     256      <USER>       <GROUP>.2        1.00            8.3
     256      OpenMP       228.8        1.02            8.5
     256    SIMD AVX       227.2        1.03            8.6
     256     pthread       196.8        1.19            9.9
     256      Hybrid       191.4        1.22           10.2
------------------------------------------------------------
    1024      <USER>       <GROUP>.0        1.00           14.7
    1024      OpenMP       528.4        1.00           14.8
    1024    SIMD AVX       527.4        1.01           14.8
    1024     pthread       528.0        1.01           14.8
    1024      Hybrid       528.6        1.00           14.8
------------------------------------------------------------
     256      <USER>       <GROUP>.6        1.00            9.9
     256      OpenMP       192.0        1.02           10.2
     256    SIMD AVX       192.4        1.02           10.2
     256     pthread       191.4        1.03           10.2
     256      Hybrid       191.4        1.03           10.2
------------------------------------------------------------

All tests completed successfully!
