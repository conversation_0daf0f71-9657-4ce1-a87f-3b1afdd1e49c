Found 30 test cases in nttdata/
Loaded test case 0: n=4, p=998244353
Loaded test case 1: n=8, p=1004535809
Loaded test case 2: n=16, p=469762049
Loaded test case 3: n=32, p=985661441
Loaded test case 4: n=64, p=998244353
Loaded test case 5: n=128, p=1004535809
Loaded test case 6: n=256, p=469762049
Loaded test case 7: n=512, p=998244353
Loaded test case 8: n=1024, p=1004535809
Loaded test case 9: n=2048, p=998244353
Loaded test case 10: n=4096, p=998244353
Loaded test case 11: n=8192, p=1004535809
Loaded test case 12: n=16384, p=998244353
Loaded test case 13: n=100, p=998244353
Loaded test case 14: n=1000, p=1004535809
Loaded test case 15: n=1, p=998244353
Loaded test case 16: n=2, p=1004535809
Loaded test case 17: n=3, p=469762049
Loaded test case 18: n=32768, p=998244353
Loaded test case 19: n=65536, p=998244353
Loaded test case 20: n=256, p=754974721
Loaded test case 21: n=1024, p=5767169
Loaded test case 22: n=256, p=998244353
Loaded test case 23: n=128, p=7340033
Loaded test case 24: n=64, p=5767169
Loaded test case 25: n=64, p=469762049
Loaded test case 26: n=64, p=985661441
Loaded test case 27: n=128, p=1004535809
Loaded test case 28: n=64, p=985661441
Loaded test case 29: n=64, p=1004535809

============================================================
           CORRECTNESS TESTING
============================================================

=== Testing Serial Radix-2 DIT ===
Test case 0: ✓ PASS (n=4, time=8μs)
Test case 1: ✓ PASS (n=8, time=11μs)
Test case 2: ✓ PASS (n=16, time=16μs)
Test case 3: ✓ PASS (n=32, time=27μs)
Test case 4: ✓ PASS (n=64, time=49μs)
Test case 5: ✓ PASS (n=128, time=99μs)
Test case 6: ✓ PASS (n=256, time=203μs)
Test case 7: ✗ FAIL (n=512, time=272μs)
Test case 8: ✗ FAIL (n=1024, time=540μs)
Test case 9: ✓ PASS (n=2048, time=2406μs)
Test case 10: ✓ PASS (n=4096, time=4712μs)
Test case 11: ✓ PASS (n=8192, time=6455μs)
Test case 12: ✓ PASS (n=16384, time=11359μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=619μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=19278μs)
Test case 19: ✓ PASS (n=65536, time=35446μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=619μs)
Test case 22: ✓ PASS (n=256, time=247μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=53μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=113μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing Serial Radix-2 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=312μs)
Test case 8: ✗ FAIL (n=1024, time=550μs)
Test case 9: ✓ PASS (n=2048, time=1772μs)
Test case 10: ✓ PASS (n=4096, time=2387μs)
Test case 11: ✓ PASS (n=8192, time=4828μs)
Test case 12: ✓ PASS (n=16384, time=8911μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18038μs)
Test case 19: ✓ PASS (n=65536, time=35048μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=617μs)
Test case 22: ✓ PASS (n=256, time=248μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=111μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Radix-4 DIT ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=364μs)
Test case 8: ✗ FAIL (n=1024, time=621μs)
Test case 9: ✓ PASS (n=2048, time=1173μs)
Test case 10: ✓ PASS (n=4096, time=2370μs)
Test case 11: ✓ PASS (n=8192, time=4381μs)
Test case 12: ✓ PASS (n=16384, time=8804μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17792μs)
Test case 19: ✓ PASS (n=65536, time=34988μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Serial Radix-4 DIF ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=630μs)
Test case 9: ✓ PASS (n=2048, time=1368μs)
Test case 10: ✓ PASS (n=4096, time=2222μs)
Test case 11: ✓ PASS (n=8192, time=4395μs)
Test case 12: ✓ PASS (n=16384, time=8830μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=622μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17617μs)
Test case 19: ✓ PASS (n=65536, time=35264μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=623μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=113μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing OpenMP Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=114μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=565μs)
Test case 9: ✓ PASS (n=2048, time=1553μs)
Test case 10: ✓ PASS (n=4096, time=2244μs)
Test case 11: ✓ PASS (n=8192, time=4760μs)
Test case 12: ✓ PASS (n=16384, time=8909μs)
Test case 13: ✓ PASS (n=100, time=118μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18095μs)
Test case 19: ✓ PASS (n=65536, time=34884μs)
Test case 20: ✗ FAIL (n=256, time=246μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=246μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=54μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing OpenMP Task Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=112μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=314μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1174μs)
Test case 10: ✓ PASS (n=4096, time=2340μs)
Test case 11: ✓ PASS (n=8192, time=4382μs)
Test case 12: ✓ PASS (n=16384, time=8828μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18284μs)
Test case 19: ✓ PASS (n=65536, time=34796μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=244μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing SIMD AVX ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1630μs)
Test case 10: ✓ PASS (n=4096, time=2326μs)
Test case 11: ✓ PASS (n=8192, time=4391μs)
Test case 12: ✓ PASS (n=16384, time=8929μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=623μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=18129μs)
Test case 19: ✓ PASS (n=65536, time=35121μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=624μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=113μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=51μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=113μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing SIMD NEON ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=244μs)
Test case 7: ✗ FAIL (n=512, time=311μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1234μs)
Test case 10: ✓ PASS (n=4096, time=7097μs)
Test case 11: ✓ PASS (n=8192, time=4730μs)
Test case 12: ✓ PASS (n=16384, time=9070μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=639μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17888μs)
Test case 19: ✓ PASS (n=65536, time=34647μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=57μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=113μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing pthread Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=315μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1326μs)
Test case 10: ✓ PASS (n=4096, time=2266μs)
Test case 11: ✓ PASS (n=8192, time=4482μs)
Test case 12: ✓ PASS (n=16384, time=8895μs)
Test case 13: ✓ PASS (n=100, time=117μs)
Test case 14: ✗ FAIL (n=1000, time=625μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17741μs)
Test case 19: ✓ PASS (n=65536, time=34927μs)
Test case 20: ✗ FAIL (n=256, time=244μs)
Test case 21: ✗ FAIL (n=1024, time=620μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=114μs)
Test case 28: ✓ PASS (n=64, time=53μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing MPI Data Parallel ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=13μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=31μs)
Test case 4: ✓ PASS (n=64, time=53μs)
Test case 5: ✓ PASS (n=128, time=115μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=323μs)
Test case 8: ✗ FAIL (n=1024, time=626μs)
Test case 9: ✓ PASS (n=2048, time=1343μs)
Test case 10: ✓ PASS (n=4096, time=2275μs)
Test case 11: ✓ PASS (n=8192, time=4470μs)
Test case 12: ✓ PASS (n=16384, time=8862μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=624μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17819μs)
Test case 19: ✓ PASS (n=65536, time=35397μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=621μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

=== Testing Hybrid All ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✓ PASS (n=8, time=12μs)
Test case 2: ✓ PASS (n=16, time=18μs)
Test case 3: ✓ PASS (n=32, time=30μs)
Test case 4: ✓ PASS (n=64, time=52μs)
Test case 5: ✓ PASS (n=128, time=113μs)
Test case 6: ✓ PASS (n=256, time=243μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=622μs)
Test case 9: ✓ PASS (n=2048, time=1181μs)
Test case 10: ✓ PASS (n=4096, time=2361μs)
Test case 11: ✓ PASS (n=8192, time=4415μs)
Test case 12: ✓ PASS (n=16384, time=8848μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✗ FAIL (n=1000, time=627μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=8μs)
Test case 18: ✓ PASS (n=32768, time=17781μs)
Test case 19: ✓ PASS (n=65536, time=34738μs)
Test case 20: ✗ FAIL (n=256, time=245μs)
Test case 21: ✗ FAIL (n=1024, time=618μs)
Test case 22: ✓ PASS (n=256, time=245μs)
Test case 23: ✓ PASS (n=128, time=114μs)
Test case 24: ✓ PASS (n=64, time=54μs)
Test case 25: ✓ PASS (n=64, time=52μs)
Test case 26: ✓ PASS (n=64, time=51μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=52μs)
Test case 29: ✓ PASS (n=64, time=51μs)

============================================================
CORRECTNESS TEST SUMMARY: 0/11 strategies passed
============================================================

============================================================
           PERFORMANCE BENCHMARK
============================================================
    Size    Strategy   Time(μs)     SpeedupThroughput(MB/s)
------------------------------------------------------------
     256      <USER>       <GROUP>.8        1.00            8.3
     256      OpenMP       197.6        1.19            9.9
     256    SIMD AVX       189.6        1.24           10.3
     256     pthread       190.2        1.23           10.3
     256      Hybrid       190.6        1.23           10.2
------------------------------------------------------------
     512      <USER>       <GROUP>.0        1.00           14.5
     512      OpenMP       263.6        1.02           14.8
     512    SIMD AVX       264.8        1.02           14.8
     512     pthread       265.6        1.02           14.7
     512      Hybrid       265.0        1.02           14.7
------------------------------------------------------------
    1024      <USER>       <GROUP>.4        1.00           14.6
    1024      OpenMP       533.4        1.00           14.6
    1024    SIMD AVX       533.4        1.00           14.6
    1024     pthread       535.8        1.00           14.6
    1024      Hybrid       532.8        1.00           14.7
------------------------------------------------------------
    2048      <USER>      <GROUP>.0        1.00           11.4
    2048      OpenMP      1435.6        0.96           10.9
    2048    SIMD AVX      1417.2        0.97           11.0
    2048     pthread      1346.6        1.02           11.6
    2048      Hybrid      1157.0        1.19           13.5
------------------------------------------------------------
    4096      <USER>      <GROUP>.4        1.00           14.1
    4096      OpenMP      2225.6        1.00           14.0
    4096    SIMD AVX      2615.6        0.85           11.9
    4096     pthread      2254.2        0.99           13.9
    4096      Hybrid      2220.8        1.00           14.1
------------------------------------------------------------
    8192      <USER>      <GROUP>.2        1.00           14.2
    8192      OpenMP      4544.6        0.97           13.8
    8192    SIMD AVX      4423.6        1.00           14.1
    8192     pthread      4387.6        1.01           14.2
    8192      Hybrid      4454.2        0.99           14.0
------------------------------------------------------------
   16384      <USER>      <GROUP>.4        1.00           14.2
   16384      OpenMP      8803.2        1.00           14.2
   16384    SIMD AVX      8783.2        1.00           14.2
   16384     pthread      8783.0        1.00           14.2
   16384      Hybrid      8786.4        1.00           14.2
------------------------------------------------------------
    1000      <USER>       <GROUP>.8        1.00           12.3
    1000      OpenMP       548.4        1.13           13.9
    1000    SIMD AVX       533.2        1.17           14.3
    1000     pthread       533.6        1.17           14.3
    1000      Hybrid       533.0        1.17           14.3
------------------------------------------------------------
   32768      <USER>     <GROUP>.6        1.00           13.3
   32768      OpenMP     21573.8        0.87           11.6
   32768    SIMD AVX     19464.6        0.96           12.8
   32768     pthread     18780.2        1.00           13.3
   32768      Hybrid     20854.8        0.90           12.0
------------------------------------------------------------
   65536      <USER>     <GROUP>.8        1.00           14.0
   65536      OpenMP     35356.6        1.01           14.1
   65536    SIMD AVX     37755.6        0.95           13.2
   65536     pthread     35687.4        1.00           14.0
   65536      Hybrid     37606.6        0.95           13.3
------------------------------------------------------------
     256      <USER>       <GROUP>.4        1.00            8.3
     256      OpenMP       228.8        1.02            8.5
     256    SIMD AVX       229.0        1.02            8.5
     256     pthread       194.8        1.20           10.0
     256      Hybrid       190.2        1.23           10.3
------------------------------------------------------------
    1024      <USER>       <GROUP>.4        1.00           14.7
    1024      OpenMP       527.4        1.01           14.8
    1024    SIMD AVX       527.8        1.00           14.8
    1024     pthread       529.0        1.00           14.8
    1024      Hybrid       527.8        1.00           14.8
------------------------------------------------------------
     256      <USER>       <GROUP>.6        1.00           10.0
     256      OpenMP       190.4        1.03           10.3
     256    SIMD AVX       190.8        1.03           10.2
     256     pthread       190.8        1.03           10.2
     256      Hybrid       190.8        1.03           10.2
------------------------------------------------------------

All tests completed successfully!
