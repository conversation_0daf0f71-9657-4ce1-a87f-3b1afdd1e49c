/**
 * @file parallel_coordinator.cpp
 * @brief 并行协调器实现
 * <AUTHOR> Acceleration Framework
 */

#include "parallel_coordinator.hpp"
#include <algorithm>
#include <cmath>
#include <immintrin.h>

namespace HybridNTT {

void ParallelCoordinator::initializeEnvironment() {
    // 检查MPI环境
#ifdef MPI_VERSION
    int provided;
    MPI_Query_thread(&provided);
    if (provided >= MPI_THREAD_MULTIPLE) {
        MPI_Comm_rank(MPI_COMM_WORLD, &mpiRank_);
        MPI_Comm_size(MPI_COMM_WORLD, &mpiSize_);
        mpiInitialized_ = true;
    }
#endif

    // 检查CUDA环境
#ifdef __CUDA_ARCH__
    cudaError_t error = cudaGetDeviceCount(&cudaDeviceCount_);
    if (error == cudaSuccess && cudaDeviceCount_ > 0) {
        cudaAvailable_ = true;
        cudaSetDevice(0); // 使用第一个GPU
    }
#endif

    // 初始化性能计数器
    performanceCounters_["task_execution_time"] = 0.0;
    performanceCounters_["task_count"] = 0.0;
    performanceCounters_["mpi_communication_time"] = 0.0;
    performanceCounters_["cuda_kernel_time"] = 0.0;
}

void ParallelCoordinator::adjustThreadPool() {
    // 停止现有线程
    shouldStop_.store(true);
    for (auto& thread : workerThreads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    workerThreads_.clear();
    
    // 创建新的线程池
    shouldStop_.store(false);
    int numThreads = strategy_.numThreads;
    workerThreads_.reserve(numThreads);
    
    for (int i = 0; i < numThreads; ++i) {
        workerThreads_.emplace_back([this, i]() {
            workerThreadFunction(i);
        });
    }
}

void ParallelCoordinator::executeSerialNTTSegment(std::vector<int>& data, int start, int length, 
                                                bool is_inverse, uint32_t modulus) {
    // 快速幂运算
    auto fastPow = [](uint32_t x, uint32_t y, uint32_t p) -> uint32_t {
        uint32_t res = 1;
        x %= p;
        while (y) {
            if (y & 1) res = (static_cast<uint64_t>(res) * x) % p;
            x = (static_cast<uint64_t>(x) * x) % p;
            y >>= 1;
        }
        return res;
    };
    
    // 执行蝶形运算
    for (int len = 2; len <= length; len <<= 1) {
        int m = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (is_inverse) wn = fastPow(wn, modulus - 2, modulus);
        
        for (int i = start; i < start + length; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < m; ++j) {
                if (i + j + m < data.size()) {
                    uint32_t u = data[i + j];
                    uint32_t v = (static_cast<uint64_t>(data[i + j + m]) * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + m] = (u - v + modulus) % modulus;
                    w = (static_cast<uint64_t>(w) * wn) % modulus;
                }
            }
        }
    }
}

void ParallelCoordinator::executeSIMDNTTSegment(std::vector<int>& data, int start, int length, 
                                              bool is_inverse, uint32_t modulus) {
    // 快速幂运算
    auto fastPow = [](uint32_t x, uint32_t y, uint32_t p) -> uint32_t {
        uint32_t res = 1;
        x %= p;
        while (y) {
            if (y & 1) res = (static_cast<uint64_t>(res) * x) % p;
            x = (static_cast<uint64_t>(x) * x) % p;
            y >>= 1;
        }
        return res;
    };
    
    // SIMD优化的蝶形运算
    for (int len = 2; len <= length; len <<= 1) {
        int m = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (is_inverse) wn = fastPow(wn, modulus - 2, modulus);
        
        for (int i = start; i < start + length; i += len) {
            uint32_t w = 1;
            int j = 0;
            
            // 向量化处理（8个元素一组）
            if (strategy_.vectorWidth >= 8 && m >= 8) {
                for (; j <= m - 8; j += 8) {
                    if (i + j + m + 7 < data.size()) {
                        // 加载数据
                        __m256i u_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&data[i + j]));
                        __m256i v_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&data[i + j + m]));
                        
                        // 简化的向量运算
                        __m256i add_vec = _mm256_add_epi32(u_vec, v_vec);
                        __m256i sub_vec = _mm256_sub_epi32(u_vec, v_vec);
                        
                        // 存储结果
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i + j]), add_vec);
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i + j + m]), sub_vec);
                        
                        w = (static_cast<uint64_t>(w) * fastPow(wn, 8, modulus)) % modulus;
                    }
                }
            }
            
            // 处理剩余元素
            for (; j < m; ++j) {
                if (i + j + m < data.size()) {
                    uint32_t u = data[i + j];
                    uint32_t v = (static_cast<uint64_t>(data[i + j + m]) * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + m] = (u - v + modulus) % modulus;
                    w = (static_cast<uint64_t>(w) * wn) % modulus;
                }
            }
        }
    }
}

void ParallelCoordinator::executeCUDANTTSegment(std::vector<int>& data, int start, int length, 
                                              bool is_inverse, uint32_t modulus) {
#ifdef __CUDA_ARCH__
    if (!cudaAvailable_) {
        // 回退到OpenMP实现
        executeOpenMPNTTSegment(data, start, length, is_inverse, modulus);
        return;
    }
    
    // CUDA实现（简化版本）
    // 实际实现需要CUDA kernel和内存管理
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 这里应该是CUDA kernel调用
    // 为了简化，暂时使用OpenMP实现
    executeOpenMPNTTSegment(data, start, length, is_inverse, modulus);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    double kernelTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();
    
    std::lock_guard<std::mutex> lock(perfMutex_);
    performanceCounters_["cuda_kernel_time"] += kernelTime;
#else
    executeOpenMPNTTSegment(data, start, length, is_inverse, modulus);
#endif
}

void ParallelCoordinator::executeOpenMPNTTSegment(std::vector<int>& data, int start, int length, 
                                                bool is_inverse, uint32_t modulus) {
    // 快速幂运算
    auto fastPow = [](uint32_t x, uint32_t y, uint32_t p) -> uint32_t {
        uint32_t res = 1;
        x %= p;
        while (y) {
            if (y & 1) res = (static_cast<uint64_t>(res) * x) % p;
            x = (static_cast<uint64_t>(x) * x) % p;
            y >>= 1;
        }
        return res;
    };
    
#ifdef _OPENMP
    // OpenMP并行蝶形运算
    for (int len = 2; len <= length; len <<= 1) {
        int m = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (is_inverse) wn = fastPow(wn, modulus - 2, modulus);
        
        #pragma omp parallel for schedule(static)
        for (int i = start; i < start + length; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < m; ++j) {
                if (i + j + m < data.size()) {
                    uint32_t u = data[i + j];
                    uint32_t v = (static_cast<uint64_t>(data[i + j + m]) * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + m] = (u - v + modulus) % modulus;
                    w = (static_cast<uint64_t>(w) * wn) % modulus;
                }
            }
        }
    }
#else
    executeSerialNTTSegment(data, start, length, is_inverse, modulus);
#endif
}

void ParallelCoordinator::executePointwiseMultiply(const ParallelTask& task) {
    if (!task.data || task.length <= 0) return;
    
    // 简化的点乘实现
    for (int i = task.start_index; i < task.start_index + task.length && i < task.data->size(); ++i) {
        // 这里应该是与另一个数组的点乘
        // 为了简化，暂时保持不变
    }
}

void ParallelCoordinator::executeDataTransfer(const ParallelTask& task) {
#ifdef MPI_VERSION
    if (mpiInitialized_) {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // MPI数据传输实现
        // 这里应该是具体的MPI通信代码
        
        auto endTime = std::chrono::high_resolution_clock::now();
        double commTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();
        
        std::lock_guard<std::mutex> lock(perfMutex_);
        performanceCounters_["mpi_communication_time"] += commTime;
    }
#endif
}

void ParallelCoordinator::executeSynchronization(const ParallelTask& task) {
#ifdef MPI_VERSION
    if (mpiInitialized_) {
        MPI_Barrier(MPI_COMM_WORLD);
    }
#endif
    
#ifdef _OPENMP
    #pragma omp barrier
#endif
}

void ParallelCoordinator::submitNTTTask(std::vector<int>& data, int start, int length, 
                                      bool is_inverse, uint32_t modulus) {
    TaskType type = is_inverse ? TaskType::NTT_INVERSE : TaskType::NTT_FORWARD;
    ParallelTask task(type, &data, start, length, 0, modulus);
    taskQueue_.push(std::move(task));
}

void ParallelCoordinator::waitForCompletion() {
    while (!taskQueue_.empty()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    // 额外等待确保所有任务完成
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

void ParallelCoordinator::shutdown() {
    shouldStop_.store(true);
    
    for (auto& thread : workerThreads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    workerThreads_.clear();
}

} // namespace HybridNTT
