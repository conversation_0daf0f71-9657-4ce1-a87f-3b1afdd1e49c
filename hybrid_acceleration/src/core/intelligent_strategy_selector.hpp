/**
 * @file intelligent_strategy_selector.hpp
 * @brief 智能策略选择器 - 硬件感知优化和自适应负载均衡
 */

#pragma once

#include "simple_hardware_detector.hpp"
#include <memory>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <iostream>
#include <ostream>
#include <string>
#include <atomic>

namespace HybridNTT {

/**
 * @brief 扩展的并行策略枚举
 */
enum class IntelligentParallelStrategy {
    SERIAL,
    OPENMP_PARALLEL,
    PTHREAD_PARALLEL,
    SIMD_PARALLEL,
    CUDA_PARALLEL,
    MPI_PARALLEL,
    OPENMP_SIMD,
    PTHREAD_SIMD,
    CUDA_SIMD,
    MPI_OPENMP,
    MPI_CUDA,
    HYBRID_ALL
};

/**
 * @brief 扩展的算法变体枚举
 */
enum class IntelligentNTTVariant {
    RADIX_2_DIT,
    RADIX_2_DIF,
    RADIX_4_DIT,
    RADIX_4_DIF,
    MIXED_RADIX,
    SPLIT_RADIX,
    CACHE_OBLIVIOUS,
    WORK_STEALING
};

/**
 * @brief 扩展的约简类型枚举
 */
enum class IntelligentReductionType {
    STANDARD,
    BARRETT,
    MONTGOMERY,
    CRT_MULTI_MODULUS
};

/**
 * @brief 智能优化策略结构
 */
struct IntelligentOptimizationStrategy {
    IntelligentParallelStrategy parallelStrategy;
    IntelligentNTTVariant nttVariant;
    IntelligentReductionType reductionType;
    
    int numThreads = 1;
    int vectorWidth = 1;
    int blockSize = 256;
    bool useNUMAAware = false;
    bool useMemoryPool = false;
    
    double estimatedPerformance = 0.0;
    double confidence = 0.0;
    
    // 硬件特定优化参数
    struct HardwareParams {
        int l1CacheSize = 32;    // KB
        int l2CacheSize = 256;   // KB
        int l3CacheSize = 8192;  // KB
        int memoryBandwidth = 25; // GB/s
        int gpuMemory = 0;       // MB
        int gpuCores = 0;
        int networkBandwidth = 1; // GB/s
    } hardwareParams;
};

/**
 * @brief 智能策略选择器 - 硬件感知优化
 */
class IntelligentStrategySelector {
public:
    std::shared_ptr<HardwareDetector> hardwareDetector_;

private:
    
    // 性能历史记录
    struct PerformanceRecord {
        int problemSize;
        int modulus;
        IntelligentParallelStrategy strategy;
        IntelligentNTTVariant variant;
        IntelligentReductionType reduction;
        double executionTime;
        double throughput;
        std::chrono::time_point<std::chrono::high_resolution_clock> timestamp;
    };
    
    std::vector<PerformanceRecord> performanceHistory_;
    std::mutex historyMutex_;
    
    // 硬件特性权重
    struct HardwareWeights {
        double cpuCoreWeight = 1.0;
        double simdWeight = 1.5;
        double gpuWeight = 3.0;
        double memoryWeight = 0.8;
        double networkWeight = 2.0;
        double cacheWeight = 1.2;
    };
    
    HardwareWeights weights_;
    
    // 自适应学习参数
    double learningRate_ = 0.1;
    int minSamplesForLearning_ = 5;

public:
    explicit IntelligentStrategySelector(std::shared_ptr<HardwareDetector> detector)
        : hardwareDetector_(detector) {
        if (detector) {
            detector->detectAll();
        }
    }
    
    /**
     * @brief 智能选择最优策略 - 基于硬件特性和历史性能
     */
    IntelligentOptimizationStrategy selectOptimalStrategy(int problemSize, int modulus) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();
        
        // 生成候选策略
        std::vector<IntelligentOptimizationStrategy> candidates = generateCandidateStrategies(problemSize, modulus);
        
        // 评估每个候选策略
        for (auto& candidate : candidates) {
            candidate.estimatedPerformance = evaluateStrategy(candidate, problemSize, modulus);
            candidate.confidence = calculateConfidence(candidate, problemSize);
            
            // 应用历史学习
            applyHistoricalLearning(candidate, problemSize, modulus);
        }
        
        // 选择最优策略
        auto bestStrategy = std::max_element(candidates.begin(), candidates.end(),
            [](const IntelligentOptimizationStrategy& a, const IntelligentOptimizationStrategy& b) {
                return (a.estimatedPerformance * a.confidence) < (b.estimatedPerformance * b.confidence);
            });
        
        if (bestStrategy != candidates.end()) {
            return *bestStrategy;
        }
        
        // 默认策略
        return createDefaultStrategy(problemSize, modulus);
    }
    
    /**
     * @brief 记录性能数据用于自适应学习
     */
    void recordPerformance(int problemSize, int modulus, 
                          const IntelligentOptimizationStrategy& strategy,
                          double executionTime, double throughput) {
        std::lock_guard<std::mutex> lock(historyMutex_);
        
        PerformanceRecord record;
        record.problemSize = problemSize;
        record.modulus = modulus;
        record.strategy = strategy.parallelStrategy;
        record.variant = strategy.nttVariant;
        record.reduction = strategy.reductionType;
        record.executionTime = executionTime;
        record.throughput = throughput;
        record.timestamp = std::chrono::high_resolution_clock::now();
        
        performanceHistory_.push_back(record);
        
        // 限制历史记录数量，保持最新的1000条记录
        if (performanceHistory_.size() > 1000) {
            performanceHistory_.erase(performanceHistory_.begin());
        }
        
        // 自适应调整权重
        adaptWeights();
    }
    
    /**
     * @brief 获取性能统计信息
     */
    void printPerformanceStatistics() {
        std::lock_guard<std::mutex> lock(historyMutex_);
        
        if (performanceHistory_.empty()) {
            std::cout << "暂无性能历史数据" << std::endl;
            return;
        }
        
        std::cout << "\n=== 智能策略选择器性能统计 ===" << std::endl;
        std::cout << "总记录数: " << performanceHistory_.size() << std::endl;
        
        // 按策略分组统计
        std::map<IntelligentParallelStrategy, std::vector<double>> strategyPerformance;
        for (const auto& record : performanceHistory_) {
            strategyPerformance[record.strategy].push_back(record.throughput);
        }
        
        for (const auto& [strategy, throughputs] : strategyPerformance) {
            double avgThroughput = std::accumulate(throughputs.begin(), throughputs.end(), 0.0) / throughputs.size();
            double maxThroughput = *std::max_element(throughputs.begin(), throughputs.end());
            std::cout << "策略 " << static_cast<int>(strategy) 
                      << " - 平均: " << avgThroughput << " MB/s"
                      << " - 最大: " << maxThroughput << " MB/s"
                      << " - 样本数: " << throughputs.size() << std::endl;
        }
        
        // 显示当前权重
        std::cout << "\n当前硬件权重:" << std::endl;
        std::cout << "CPU核心权重: " << weights_.cpuCoreWeight << std::endl;
        std::cout << "SIMD权重: " << weights_.simdWeight << std::endl;
        std::cout << "GPU权重: " << weights_.gpuWeight << std::endl;
        std::cout << "内存权重: " << weights_.memoryWeight << std::endl;
        std::cout << "网络权重: " << weights_.networkWeight << std::endl;
        std::cout << "缓存权重: " << weights_.cacheWeight << std::endl;
    }

private:
    /**
     * @brief 生成候选策略列表
     */
    std::vector<IntelligentOptimizationStrategy> generateCandidateStrategies(int problemSize, int modulus) {
        std::vector<IntelligentOptimizationStrategy> candidates;

        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();

        // 基础并行策略候选
        std::vector<IntelligentParallelStrategy> parallelStrategies = {
            IntelligentParallelStrategy::SERIAL,
            IntelligentParallelStrategy::OPENMP_PARALLEL,
            IntelligentParallelStrategy::PTHREAD_PARALLEL
        };

        // 根据硬件能力添加策略
        if (cpuInfo.avx2_support || cpuInfo.neon_support || cpuInfo.sse_support) {
            parallelStrategies.push_back(IntelligentParallelStrategy::SIMD_PARALLEL);
            parallelStrategies.push_back(IntelligentParallelStrategy::OPENMP_SIMD);
            parallelStrategies.push_back(IntelligentParallelStrategy::PTHREAD_SIMD);
        }

        if (gpuInfo.available) {
            parallelStrategies.push_back(IntelligentParallelStrategy::CUDA_PARALLEL);
            if (cpuInfo.avx2_support || cpuInfo.neon_support) {
                parallelStrategies.push_back(IntelligentParallelStrategy::CUDA_SIMD);
            }
        }

        if (networkInfo.hasMPI && networkInfo.mpi_size > 1) {
            parallelStrategies.push_back(IntelligentParallelStrategy::MPI_PARALLEL);
            parallelStrategies.push_back(IntelligentParallelStrategy::MPI_OPENMP);
            if (gpuInfo.available) {
                parallelStrategies.push_back(IntelligentParallelStrategy::MPI_CUDA);
            }
        }

        // 大规模问题考虑混合策略
        if (problemSize >= 8192) {
            parallelStrategies.push_back(IntelligentParallelStrategy::HYBRID_ALL);
        }

        // 算法变体候选
        std::vector<IntelligentNTTVariant> nttVariants = {
            IntelligentNTTVariant::RADIX_2_DIT,
            IntelligentNTTVariant::RADIX_4_DIT
        };

        // 根据问题规模添加高级算法
        if (problemSize >= 1024) {
            nttVariants.push_back(IntelligentNTTVariant::SPLIT_RADIX);
            nttVariants.push_back(IntelligentNTTVariant::CACHE_OBLIVIOUS);
        }

        if (problemSize >= 4096) {
            nttVariants.push_back(IntelligentNTTVariant::WORK_STEALING);
        }

        // 约简类型候选
        std::vector<IntelligentReductionType> reductionTypes = {
            IntelligentReductionType::STANDARD,
            IntelligentReductionType::BARRETT,
            IntelligentReductionType::MONTGOMERY
        };

        // 大模数考虑CRT
        if (modulus > 1000000000) {
            reductionTypes.push_back(IntelligentReductionType::CRT_MULTI_MODULUS);
        }

        // 生成所有组合
        for (auto parallelStrategy : parallelStrategies) {
            for (auto nttVariant : nttVariants) {
                for (auto reductionType : reductionTypes) {
                    IntelligentOptimizationStrategy strategy;
                    strategy.parallelStrategy = parallelStrategy;
                    strategy.nttVariant = nttVariant;
                    strategy.reductionType = reductionType;

                    // 配置策略参数
                    configureStrategyParameters(strategy, problemSize, modulus);

                    candidates.push_back(strategy);
                }
            }
        }

        return candidates;
    }

public:
    /**
     * @brief 配置策略参数
     */
    void configureStrategyParameters(IntelligentOptimizationStrategy& strategy, int problemSize, int modulus) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();

        // 设置线程数
        switch (strategy.parallelStrategy) {
            case IntelligentParallelStrategy::SERIAL:
                strategy.numThreads = 1;
                break;
            case IntelligentParallelStrategy::OPENMP_PARALLEL:
            case IntelligentParallelStrategy::OPENMP_SIMD:
                strategy.numThreads = std::min(cpuInfo.cores, std::max(1, problemSize / 512));
                break;
            case IntelligentParallelStrategy::PTHREAD_PARALLEL:
            case IntelligentParallelStrategy::PTHREAD_SIMD:
                strategy.numThreads = std::min(cpuInfo.cores, std::max(1, problemSize / 1024));
                break;
            case IntelligentParallelStrategy::CUDA_PARALLEL:
            case IntelligentParallelStrategy::CUDA_SIMD:
                strategy.numThreads = gpuInfo.available ? 256 : 1; // GPU block size
                break;
            case IntelligentParallelStrategy::MPI_PARALLEL:
            case IntelligentParallelStrategy::MPI_OPENMP:
            case IntelligentParallelStrategy::MPI_CUDA:
                strategy.numThreads = cpuInfo.cores;
                break;
            case IntelligentParallelStrategy::HYBRID_ALL:
                strategy.numThreads = cpuInfo.cores;
                break;
            default:
                strategy.numThreads = 1;
                break;
        }

        // 设置向量宽度
        if (cpuInfo.avx2_support) {
            strategy.vectorWidth = 8;
        } else if (cpuInfo.avx_support) {
            strategy.vectorWidth = 8;
        } else if (cpuInfo.neon_support) {
            strategy.vectorWidth = 4;
        } else if (cpuInfo.sse_support) {
            strategy.vectorWidth = 4;
        } else {
            strategy.vectorWidth = 1;
        }

        // 设置块大小
        strategy.blockSize = std::min(1024, std::max(64, problemSize / strategy.numThreads));

        // NUMA感知设置
        strategy.useNUMAAware = (cpuInfo.cores >= 8 && problemSize >= 8192);

        // 内存池设置
        strategy.useMemoryPool = (problemSize >= 4096);

        // 硬件参数设置
        strategy.hardwareParams.l1CacheSize = 32;   // 假设32KB L1
        strategy.hardwareParams.l2CacheSize = 256;  // 假设256KB L2
        strategy.hardwareParams.l3CacheSize = 8192; // 假设8MB L3
        strategy.hardwareParams.memoryBandwidth = 25; // 假设25GB/s DDR4

        if (gpuInfo.available) {
            strategy.hardwareParams.gpuMemory = static_cast<int>(gpuInfo.memory_size / (1024 * 1024)); // MB
            strategy.hardwareParams.gpuCores = gpuInfo.device_count * 1024; // 估算
        }
    }

    /**
     * @brief 评估策略性能
     */
    double evaluateStrategy(const IntelligentOptimizationStrategy& strategy, int problemSize, int modulus) {
        double baseComplexity = problemSize * std::log2(problemSize); // O(n log n)

        // 并行效率评估
        double parallelEfficiency = calculateParallelEfficiency(strategy, problemSize);

        // 算法效率评估
        double algorithmEfficiency = calculateAlgorithmEfficiency(strategy.nttVariant, problemSize);

        // 约简效率评估
        double reductionEfficiency = calculateReductionEfficiency(strategy.reductionType, modulus);

        // 硬件匹配度评估
        double hardwareMatch = calculateHardwareMatch(strategy);

        // 综合性能评分
        double performance = baseComplexity / (parallelEfficiency * algorithmEfficiency * reductionEfficiency * hardwareMatch);

        return 1.0 / performance; // 转换为性能分数（越高越好）
    }

    /**
     * @brief 计算并行效率
     */
    double calculateParallelEfficiency(const IntelligentOptimizationStrategy& strategy, int problemSize) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();

        double efficiency = 1.0;

        switch (strategy.parallelStrategy) {
            case IntelligentParallelStrategy::SERIAL:
                efficiency = 1.0;
                break;

            case IntelligentParallelStrategy::OPENMP_PARALLEL:
            case IntelligentParallelStrategy::PTHREAD_PARALLEL:
                // Amdahl定律近似
                efficiency = std::min(static_cast<double>(cpuInfo.cores),
                                    static_cast<double>(problemSize) / 1000.0);
                efficiency *= 0.85; // 并行开销
                break;

            case IntelligentParallelStrategy::SIMD_PARALLEL:
                efficiency = std::min(static_cast<double>(strategy.vectorWidth),
                                    static_cast<double>(problemSize) / 100.0);
                break;

            case IntelligentParallelStrategy::OPENMP_SIMD:
            case IntelligentParallelStrategy::PTHREAD_SIMD:
                efficiency = std::min(static_cast<double>(cpuInfo.cores * strategy.vectorWidth),
                                    static_cast<double>(problemSize) / 500.0);
                efficiency *= 0.8; // 混合并行开销
                break;

            case IntelligentParallelStrategy::CUDA_PARALLEL:
            case IntelligentParallelStrategy::CUDA_SIMD:
                if (gpuInfo.available) {
                    efficiency = std::min(static_cast<double>(gpuInfo.device_count * 1024),
                                        static_cast<double>(problemSize) / 10.0);
                    efficiency *= 0.7; // GPU传输开销
                }
                break;

            case IntelligentParallelStrategy::MPI_PARALLEL:
            case IntelligentParallelStrategy::MPI_OPENMP:
            case IntelligentParallelStrategy::MPI_CUDA:
                if (networkInfo.hasMPI) {
                    efficiency = std::min(static_cast<double>(networkInfo.mpi_size),
                                        static_cast<double>(problemSize) / 10000.0);
                    efficiency *= 0.6; // 网络通信开销
                }
                break;

            case IntelligentParallelStrategy::HYBRID_ALL:
                // 混合策略：考虑所有因素
                efficiency = calculateHybridEfficiency(strategy, problemSize);
                break;
        }

        return std::max(1.0, efficiency);
    }

    /**
     * @brief 计算混合策略效率
     */
    double calculateHybridEfficiency(const IntelligentOptimizationStrategy& strategy, int problemSize) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();

        double cpuEff = std::min(static_cast<double>(cpuInfo.cores),
                               static_cast<double>(problemSize) / 1000.0);
        double simdEff = std::min(static_cast<double>(strategy.vectorWidth),
                                static_cast<double>(problemSize) / 100.0);
        double gpuEff = gpuInfo.available ?
                       std::min(static_cast<double>(gpuInfo.device_count * 512),
                              static_cast<double>(problemSize) / 20.0) : 0.0;
        double mpiEff = networkInfo.hasMPI ?
                       std::min(static_cast<double>(networkInfo.mpi_size),
                              static_cast<double>(problemSize) / 5000.0) : 0.0;

        // 协同效应计算
        double synergy = 1.0;
        if (gpuEff > 0 && cpuEff > 0) synergy *= 1.2;
        if (mpiEff > 0 && (cpuEff > 0 || gpuEff > 0)) synergy *= 1.1;
        if (simdEff > 0 && cpuEff > 0) synergy *= 1.15;

        double totalEff = (cpuEff + simdEff + gpuEff + mpiEff) * synergy * 0.5; // 混合开销
        return std::max(1.0, totalEff);
    }

    /**
     * @brief 计算算法效率
     */
    double calculateAlgorithmEfficiency(IntelligentNTTVariant variant, int problemSize) {
        switch (variant) {
            case IntelligentNTTVariant::RADIX_2_DIT:
            case IntelligentNTTVariant::RADIX_2_DIF:
                return 1.0; // 基准

            case IntelligentNTTVariant::RADIX_4_DIT:
            case IntelligentNTTVariant::RADIX_4_DIF:
                return problemSize >= 1024 ? 1.2 : 0.9; // 大规模更优

            case IntelligentNTTVariant::MIXED_RADIX:
                return 1.1; // 适中

            case IntelligentNTTVariant::SPLIT_RADIX:
                return problemSize >= 2048 ? 1.4 : 1.0; // 理论最优

            case IntelligentNTTVariant::CACHE_OBLIVIOUS:
                return problemSize >= 4096 ? 1.3 : 1.1; // 缓存友好

            case IntelligentNTTVariant::WORK_STEALING:
                return problemSize >= 8192 ? 1.25 : 0.95; // 大规模负载均衡

            default:
                return 1.0;
        }
    }

    /**
     * @brief 计算约简效率
     */
    double calculateReductionEfficiency(IntelligentReductionType reduction, int modulus) {
        int modulusBits = static_cast<int>(std::log2(modulus)) + 1;

        switch (reduction) {
            case IntelligentReductionType::STANDARD:
                return modulusBits <= 20 ? 1.0 : 0.5; // 小模数高效

            case IntelligentReductionType::BARRETT:
                return modulusBits >= 20 ? 1.3 : 1.0; // 中大模数优化

            case IntelligentReductionType::MONTGOMERY:
                return modulusBits >= 25 ? 1.4 : 1.1; // 大模数最优

            case IntelligentReductionType::CRT_MULTI_MODULUS:
                return modulusBits >= 30 ? 1.5 : 0.8; // 超大模数专用

            default:
                return 1.0;
        }
    }

    /**
     * @brief 计算硬件匹配度
     */
    double calculateHardwareMatch(const IntelligentOptimizationStrategy& strategy) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();

        double match = 1.0;

        // CPU匹配度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::OPENMP_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::PTHREAD_PARALLEL) {
            match *= (cpuInfo.cores >= 4) ? 1.2 : 0.8;
        }

        // SIMD匹配度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::SIMD_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::OPENMP_SIMD ||
            strategy.parallelStrategy == IntelligentParallelStrategy::PTHREAD_SIMD) {
            if (cpuInfo.avx2_support) match *= 1.3;
            else if (cpuInfo.avx_support) match *= 1.2;
            else if (cpuInfo.neon_support) match *= 1.25;
            else if (cpuInfo.sse_support) match *= 1.1;
            else match *= 0.5;
        }

        // GPU匹配度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::CUDA_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::CUDA_SIMD ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_CUDA) {
            match *= gpuInfo.available ? 1.5 : 0.1;
        }

        // MPI匹配度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::MPI_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_OPENMP ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_CUDA) {
            match *= (networkInfo.hasMPI && networkInfo.mpi_size > 1) ? 1.4 : 0.1;
        }

        return match;
    }

    /**
     * @brief 计算策略置信度
     */
    double calculateConfidence(const IntelligentOptimizationStrategy& strategy, int problemSize) {
        double confidence = 0.8; // 基础置信度

        // 根据硬件匹配度调整
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();

        // GPU策略置信度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::CUDA_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::CUDA_SIMD ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_CUDA) {
            confidence = gpuInfo.available ? confidence : 0.0;
        }

        // MPI策略置信度
        if (strategy.parallelStrategy == IntelligentParallelStrategy::MPI_PARALLEL ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_OPENMP ||
            strategy.parallelStrategy == IntelligentParallelStrategy::MPI_CUDA) {
            confidence = (networkInfo.hasMPI && networkInfo.mpi_size > 1) ? confidence : 0.0;
        }

        // 根据问题规模调整
        if (problemSize < 512 && strategy.parallelStrategy != IntelligentParallelStrategy::SERIAL) {
            confidence *= 0.5; // 小规模问题不适合复杂并行
        }

        if (problemSize >= 8192) {
            confidence += 0.1; // 大规模问题提高置信度
        }

        // 根据算法变体调整
        if (strategy.nttVariant == IntelligentNTTVariant::SPLIT_RADIX && problemSize >= 2048) {
            confidence += 0.05; // Split-Radix在大规模问题上更可靠
        }

        if (strategy.nttVariant == IntelligentNTTVariant::WORK_STEALING && problemSize >= 8192) {
            confidence += 0.05; // Work-Stealing在超大规模问题上更可靠
        }

        return std::min(1.0, std::max(0.0, confidence));
    }

    /**
     * @brief 应用历史学习
     */
    void applyHistoricalLearning(IntelligentOptimizationStrategy& strategy, int problemSize, int modulus) {
        std::lock_guard<std::mutex> lock(historyMutex_);

        if (performanceHistory_.size() < minSamplesForLearning_) {
            return; // 样本不足，不进行学习
        }

        // 查找相似问题的历史记录
        std::vector<PerformanceRecord> similarRecords;
        for (const auto& record : performanceHistory_) {
            // 问题规模相似度（±20%）
            if (std::abs(record.problemSize - problemSize) <= problemSize * 0.2 &&
                std::abs(record.modulus - modulus) <= modulus * 0.2) {
                similarRecords.push_back(record);
            }
        }

        if (similarRecords.empty()) {
            return; // 没有相似记录
        }

        // 计算相同策略的平均性能
        double totalThroughput = 0.0;
        int matchingRecords = 0;

        for (const auto& record : similarRecords) {
            if (record.strategy == strategy.parallelStrategy &&
                record.variant == strategy.nttVariant &&
                record.reduction == strategy.reductionType) {
                totalThroughput += record.throughput;
                matchingRecords++;
            }
        }

        if (matchingRecords > 0) {
            double avgThroughput = totalThroughput / matchingRecords;

            // 根据历史性能调整估计性能
            double historicalBonus = avgThroughput / 1000.0; // 归一化
            strategy.estimatedPerformance *= (1.0 + historicalBonus * learningRate_);

            // 根据样本数量调整置信度
            double sampleBonus = std::min(0.2, matchingRecords * 0.02);
            strategy.confidence = std::min(1.0, strategy.confidence + sampleBonus);
        }
    }

    /**
     * @brief 自适应调整权重
     */
    void adaptWeights() {
        std::lock_guard<std::mutex> lock(historyMutex_);

        if (performanceHistory_.size() < minSamplesForLearning_ * 2) {
            return; // 样本不足
        }

        // 分析不同硬件特性对性能的影响
        std::map<IntelligentParallelStrategy, std::vector<double>> strategyPerformance;
        for (const auto& record : performanceHistory_) {
            strategyPerformance[record.strategy].push_back(record.throughput);
        }

        // 计算各策略的平均性能
        double maxAvgPerformance = 0.0;
        for (const auto& [strategy, throughputs] : strategyPerformance) {
            if (!throughputs.empty()) {
                double avgPerformance = std::accumulate(throughputs.begin(), throughputs.end(), 0.0) / throughputs.size();
                maxAvgPerformance = std::max(maxAvgPerformance, avgPerformance);
            }
        }

        // 根据性能表现调整权重
        for (const auto& [strategy, throughputs] : strategyPerformance) {
            if (!throughputs.empty()) {
                double avgPerformance = std::accumulate(throughputs.begin(), throughputs.end(), 0.0) / throughputs.size();
                double performanceRatio = avgPerformance / maxAvgPerformance;

                // 调整相应的硬件权重
                switch (strategy) {
                    case IntelligentParallelStrategy::OPENMP_PARALLEL:
                    case IntelligentParallelStrategy::PTHREAD_PARALLEL:
                        weights_.cpuCoreWeight = weights_.cpuCoreWeight * (1.0 + (performanceRatio - 0.5) * learningRate_);
                        break;
                    case IntelligentParallelStrategy::SIMD_PARALLEL:
                    case IntelligentParallelStrategy::OPENMP_SIMD:
                    case IntelligentParallelStrategy::PTHREAD_SIMD:
                        weights_.simdWeight = weights_.simdWeight * (1.0 + (performanceRatio - 0.5) * learningRate_);
                        break;
                    case IntelligentParallelStrategy::CUDA_PARALLEL:
                    case IntelligentParallelStrategy::CUDA_SIMD:
                        weights_.gpuWeight = weights_.gpuWeight * (1.0 + (performanceRatio - 0.5) * learningRate_);
                        break;
                    case IntelligentParallelStrategy::MPI_PARALLEL:
                    case IntelligentParallelStrategy::MPI_OPENMP:
                    case IntelligentParallelStrategy::MPI_CUDA:
                        weights_.networkWeight = weights_.networkWeight * (1.0 + (performanceRatio - 0.5) * learningRate_);
                        break;
                    default:
                        break;
                }
            }
        }

        // 限制权重范围
        weights_.cpuCoreWeight = std::max(0.1, std::min(5.0, weights_.cpuCoreWeight));
        weights_.simdWeight = std::max(0.1, std::min(5.0, weights_.simdWeight));
        weights_.gpuWeight = std::max(0.1, std::min(10.0, weights_.gpuWeight));
        weights_.memoryWeight = std::max(0.1, std::min(3.0, weights_.memoryWeight));
        weights_.networkWeight = std::max(0.1, std::min(5.0, weights_.networkWeight));
        weights_.cacheWeight = std::max(0.1, std::min(3.0, weights_.cacheWeight));
    }

    /**
     * @brief 创建默认策略
     */
    IntelligentOptimizationStrategy createDefaultStrategy(int problemSize, int modulus) {
        IntelligentOptimizationStrategy strategy;

        const auto& cpuInfo = hardwareDetector_->getCPUInfo();

        // 根据问题规模选择基本策略
        if (problemSize < 1024) {
            strategy.parallelStrategy = IntelligentParallelStrategy::SERIAL;
            strategy.nttVariant = IntelligentNTTVariant::RADIX_2_DIT;
        } else if (problemSize < 8192) {
            strategy.parallelStrategy = cpuInfo.cores >= 4 ?
                IntelligentParallelStrategy::OPENMP_PARALLEL : IntelligentParallelStrategy::SERIAL;
            strategy.nttVariant = IntelligentNTTVariant::RADIX_4_DIT;
        } else {
            strategy.parallelStrategy = IntelligentParallelStrategy::OPENMP_PARALLEL;
            strategy.nttVariant = IntelligentNTTVariant::SPLIT_RADIX;
        }

        // 根据模数选择约简类型
        int modulusBits = static_cast<int>(std::log2(modulus)) + 1;
        if (modulusBits <= 20) {
            strategy.reductionType = IntelligentReductionType::STANDARD;
        } else if (modulusBits <= 30) {
            strategy.reductionType = IntelligentReductionType::BARRETT;
        } else {
            strategy.reductionType = IntelligentReductionType::MONTGOMERY;
        }

        // 配置基本参数
        configureStrategyParameters(strategy, problemSize, modulus);

        strategy.estimatedPerformance = 1.0;
        strategy.confidence = 0.7;

        return strategy;
    }
};

} // namespace HybridNTT
