#include <cuda_runtime.h>
#include <iostream>
#include <vector>
#include <cstring>
#include <algorithm>
using namespace std;

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

typedef long long ll;

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

void calc_powg_cpu(int w[], int G, int P, int gen) {
    w[0] = 1; 
    long long f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (1LL * f * w[x - (1<<t)]) % P;
    }
}

// CUDA Kernel: DIF NTT蝶形运算
__global__ void dif_kernel(int *data, int len, const int *twiddles, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    int g = data[base + local_id];
    int h = data[base + local_id + half_len];
    
    // DIF: 对下半部分先乘旋转因子，然后加减
    long long w = twiddles[block_id];
    h = (1LL * h * w) % p;
    
    data[base + local_id] = (g + h) % p;
    data[base + local_id + half_len] = (g - h + p) % p;
}

// CUDA Kernel: DIT NTT蝶形运算
__global__ void dit_kernel(int *data, int len, const int *twiddles, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    int g = data[base + local_id];
    int h = data[base + local_id + half_len];
    
    // DIT: 先加减，对差值乘旋转因子
    data[base + local_id] = (g + h) % p;
    long long w = twiddles[block_id];
    data[base + local_id + half_len] = (1LL * (g - h + p) * w) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 数组反转（除了第一个元素）
__global__ void reverse_kernel(int *data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;

    // 反转data[1]到data[n-1]，总共n-1个元素
    // 需要交换的对数是(n-1)/2
    int total_pairs = (n - 1) / 2;

    if(idx < total_pairs) {
        int left = 1 + idx;  // 从data[1]开始
        int right = n - 1 - idx;  // 对应的右边位置

        // 调试信息
        if(idx == 0) {
            printf("reverse_kernel: idx=%d, left=%d, right=%d, data[left]=%d, data[right]=%d\n",
                   idx, left, right, data[left], data[right]);
        }

        int temp = data[left];
        data[left] = data[right];
        data[right] = temp;
    }
}

void cuda_poly_multiply_dif_dit(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 预计算旋转因子
    std::vector<int> twiddles(lim);
    calc_powg_cpu(twiddles.data(), lim, p, 3);
    
    cout << "lim = " << lim << ", 旋转因子: ";
    for(int i = 0; i < lim; i++) {
        cout << twiddles[i] << " ";
    }
    cout << endl;
    
    int *d_data_a, *d_data_b, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data_a, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_data_b, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, lim * sizeof(int)));
    
    CHECK_CUDA(cudaMemcpy(d_data_a, A.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_data_b, B.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    // DIF正变换
    cout << "DIF变换..." << endl;
    for(int len = lim; len > 1; len >>= 1) {
        cout << "  len = " << len << endl;
        int half_len = len >> 1;
        int num_blocks = lim / len;
        int total_butterflies = num_blocks * half_len;
        
        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;
        
        dif_kernel<<<blocks, threads>>>(d_data_a, len, d_twiddles, p, lim);
        dif_kernel<<<blocks, threads>>>(d_data_b, len, d_twiddles, p, lim);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 读取DIF结果
    CHECK_CUDA(cudaMemcpy(A.data(), d_data_a, lim * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaMemcpy(B.data(), d_data_b, lim * sizeof(int), cudaMemcpyDeviceToHost));
    
    cout << "DIF A结果: ";
    for(int i = 0; i < lim; i++) cout << A[i] << " ";
    cout << endl;
    
    cout << "DIF B结果: ";
    for(int i = 0; i < lim; i++) cout << B[i] << " ";
    cout << endl;
    
    // 点乘
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    cout << "点乘结果: ";
    for(int i = 0; i < lim; i++) cout << A[i] << " ";
    cout << endl;
    
    // 对旋转因子取逆
    for(int& w : twiddles) {
        w = qpow(w, p-2, p);
    }
    
    CHECK_CUDA(cudaMemcpy(d_data_a, A.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    // DIT逆变换
    cout << "DIT逆变换..." << endl;
    for(int len = 2; len <= lim; len <<= 1) {
        cout << "  len = " << len << endl;
        int half_len = len >> 1;
        int num_blocks = lim / len;
        int total_butterflies = num_blocks * half_len;
        
        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;
        
        dit_kernel<<<blocks, threads>>>(d_data_a, len, d_twiddles, p, lim);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 缩放
    int inv_n = qpow(lim, p-2, p);
    cout << "缩放因子: " << inv_n << endl;
    int threads = min(1024, lim);
    int blocks = (lim + threads - 1) / threads;
    scale_kernel<<<blocks, threads>>>(d_data_a, inv_n, lim, p);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 读取最终结果
    CHECK_CUDA(cudaMemcpy(A.data(), d_data_a, lim * sizeof(int), cudaMemcpyDeviceToHost));
    
    cout << "DIT+缩放结果: ";
    for(int i = 0; i < lim; i++) cout << A[i] << " ";
    cout << endl;

    // 手动验证reverse
    cout << "手动reverse验证: ";
    vector<int> manual_reverse = A;
    reverse(manual_reverse.begin() + 1, manual_reverse.end());
    for(int i = 0; i < lim; i++) cout << manual_reverse[i] << " ";
    cout << endl;

    cout << "最终结果: ";
    for(int i = 0; i < lim; i++) cout << A[i] << " ";
    cout << endl;
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
    
    CHECK_CUDA(cudaFree(d_data_a));
    CHECK_CUDA(cudaFree(d_data_b));
    CHECK_CUDA(cudaFree(d_twiddles));
}

int main() {
    // 测试用例0: n=4, p=7340033
    int a[] = {4, 1, 5, 2};
    int b[] = {1, 5, 5, 4};
    int ab[7];
    
    cout << "输入a: ";
    for(int i = 0; i < 4; i++) cout << a[i] << " ";
    cout << endl;
    
    cout << "输入b: ";
    for(int i = 0; i < 4; i++) cout << b[i] << " ";
    cout << endl;
    
    cuda_poly_multiply_dif_dit(a, b, ab, 4, 7340033);
    
    cout << "GPU结果: ";
    for(int i = 0; i < 7; i++) {
        cout << ab[i] << " ";
    }
    cout << endl;
    
    // 期望结果: 4 21 30 48 39 30 8
    int expected[] = {4, 21, 30, 48, 39, 30, 8};
    bool correct = true;
    for(int i = 0; i < 7; i++) {
        if(ab[i] != expected[i]) {
            correct = false;
            cout << "位置 " << i << ": 期望 " << expected[i] << ", 实际 " << ab[i] << endl;
        }
    }
    
    cout << (correct ? "正确" : "错误") << endl;
    
    return 0;
}
