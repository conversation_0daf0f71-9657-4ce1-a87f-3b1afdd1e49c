/*
 * ===========================================
 * 文件名: main_cuda_montgomery_fixed.cu
 * 描述: 修正版CUDA Montgomery规约优化的NTT实现
 * 特性: 真正的无预计算 vs 预计算对比
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_montgomery_fixed.cu -o ntt_cuda_montgomery_fixed
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    bool ok = true;
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            ok = false;
        }
    }
    if(ok) {
        std::cout << "结果正确" << std::endl;
    } else {
        std::cout << "结果错误" << std::endl;
    }
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

__host__ unsigned int mod_inverse_host(unsigned int a, unsigned int m) {
    long long m0 = m, t, q;
    long long x0 = 0, x1 = 1;
    long long a_ll = a, m_ll = m;
    if (m_ll == 1) return 0;
    while (a_ll > 1) {
        if (m_ll == 0) return 0;
        q = a_ll / m_ll;
        t = m_ll;
        m_ll = a_ll % m_ll;
        a_ll = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    if (a_ll != 1) return 0;
    if (x1 < 0) x1 += m0;
    return (unsigned int)x1;
}

struct MontgomeryParams {
    unsigned int mod;
    unsigned int mod_prime;
    unsigned int r2_mod;

    __host__ MontgomeryParams(unsigned int m = 0) : mod(m), mod_prime(0), r2_mod(0) {
        if (m > 0) {
            unsigned long long p_inv = m;
            for (int i = 0; i < 5; ++i) {
                p_inv = p_inv * (2 - m * p_inv);
            }
            mod_prime = (unsigned int)(-p_inv);

            unsigned long long r2 = 1ULL << 32;
            r2 %= m;
            r2 = (r2 * r2) % m;
            r2_mod = (unsigned int)r2;
        }
    }

    __host__ __device__ inline unsigned int reduce(unsigned long long x) const {
        unsigned int q = (unsigned int)x * mod_prime;
        unsigned long long t = x + (unsigned long long)q * mod;
        unsigned int res = (unsigned int)(t >> 32);
        return (res >= mod) ? res - mod : res;
    }

    __host__ __device__ inline unsigned int to_montgomery(unsigned int x) const {
        return reduce((unsigned long long)x * r2_mod);
    }

    __host__ __device__ inline unsigned int from_montgomery(unsigned int x) const {
        return reduce(x);
    }

    __host__ __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }

    __host__ __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? sum - mod : sum;
    }

    __host__ __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a - b + mod);
    }
};

__global__ void bit_reverse_kernel(int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        int target = rev[idx];
        if (idx < target) {
            int tmp = data[idx];
            data[idx] = data[target];
            data[target] = tmp;
        }
    }
}

__global__ void to_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.to_montgomery((unsigned int)data[idx]);
    }
}

__global__ void pointwise_mul_montgomery_kernel(int *A, const int *B, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        A[idx] = (int)mont.mul((unsigned int)A[idx], (unsigned int)B[idx]);
    }
}

__global__ void final_scaling_kernel(int *data, unsigned int factor_mont, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        unsigned int res_mont = mont.mul((unsigned int)data[idx], factor_mont);
        data[idx] = mont.from_montgomery(res_mont);
    }
}

// 真正的无预计算kernel：每次计算单个旋转因子
__global__ void ntt_naive_kernel(int *data, int len, unsigned int wn_mont, 
                                MontgomeryParams mont, int n) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    // 真正的无预计算：每次重新计算旋转因子
    unsigned int w = mont.to_montgomery(1);
    unsigned int wn_pow = wn_mont;
    int k_temp = k;

    // 这是低效的方式，但这才是真正的"无预计算"
    while(k_temp > 0) {
        if(k_temp & 1) {
            w = mont.mul(w, wn_pow);
        }
        wn_pow = mont.mul(wn_pow, wn_pow);
        k_temp >>= 1;
    }
    
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);
    
    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

// 优化的预计算kernel
__global__ void ntt_precomp_kernel(int *data, const int *twiddles, int len, 
                                  MontgomeryParams mont, int n, int offset) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    // 直接从预计算表中获取旋转因子
    unsigned int w = (unsigned int)twiddles[offset + k];
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);
    
    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

// 更高效的无预计算kernel：减少重复计算
__global__ void ntt_efficient_kernel(int *data, int len, unsigned int wn_mont,
                                    MontgomeryParams mont, int n) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    // 使用迭代而非快速幂来计算w = wn^k
    unsigned int w = mont.to_montgomery(1);
    for(int iter = 0; iter < k; iter++) {
        w = mont.mul(w, wn_mont);
    }
    
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);
    
    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

void precompute_twiddles_montgomery(std::vector<int>& twiddles, int n, int p, 
                                   bool inverse, const MontgomeryParams& mont) {
    twiddles.assign(n, 0);
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn_normal = qpow(3, (p - 1) / len, p);
        if (inverse) {
            wn_normal = qpow(wn_normal, p - 2, p);
        }
        
        long long w_normal = 1;
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = mont.to_montgomery((unsigned int)w_normal);
            w_normal = (w_normal * wn_normal) % p;
        }
        offset += len / 2;
    }
}

// 专门的性能测试函数：只测量核心NTT操作
void benchmark_ntt_performance(int* d_data, int n, bool inverse, int p, 
                               const MontgomeryParams& mont, int* d_rev, 
                               int* d_twiddles, bool use_precomp, bool use_efficient,
                               double& kernel_time_ms) {
    cudaEvent_t start, stop;
    CHECK_CUDA(cudaEventCreate(&start));
    CHECK_CUDA(cudaEventCreate(&stop));
    
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    
    // 位反转（预处理，不计入时间）
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 开始计时 - 只测量NTT核心循环
    CHECK_CUDA(cudaEventRecord(start));
    
    if (use_precomp && d_twiddles != nullptr) {
        // 预计算版本 - 移除中间同步
        int offset = 0;
        for (int len = 2; len <= n; len <<= 1) {
            int half_len = len >> 1;
            blocks = (n / len) * half_len / threads + 1;
            ntt_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, mont, n, offset);
            offset += half_len;
            // 移除中间同步，只在最后同步
        }
    } else {
        // 无预计算版本 - 移除中间同步
        for (int len = 2; len <= n; len <<= 1) {
            long long wn_normal = qpow(3, (p - 1) / len, p);
            if (inverse) wn_normal = qpow(wn_normal, p - 2, p);
            unsigned int wn_mont = mont.to_montgomery((unsigned int)wn_normal);

            int half_len = len >> 1;
            blocks = (n / len) * half_len / threads + 1;

            if (use_efficient) {
                ntt_efficient_kernel<<<blocks, threads>>>(d_data, len, wn_mont, mont, n);
            } else {
                ntt_naive_kernel<<<blocks, threads>>>(d_data, len, wn_mont, mont, n);
            }
            // 移除中间同步
        }
    }
    
    // 结束计时
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));
    
    float milliseconds = 0;
    CHECK_CUDA(cudaEventElapsedTime(&milliseconds, start, stop));
    kernel_time_ms = (double)milliseconds;
    
    CHECK_CUDA(cudaEventDestroy(start));
    CHECK_CUDA(cudaEventDestroy(stop));
}

// 改进的多项式乘法函数，分别测量各部分时间
void cuda_poly_multiply_montgomery_detailed(int* a, int* b, int n, int p, bool use_precomp, 
                                          bool use_efficient, double& fft_time, 
                                          double& ifft_time, double& total_time) {
    int lim = 1;
    while(lim < (2 * n - 1)) lim <<= 1;

    std::vector<int> rev(lim);
    for(int i = 0; i < lim; i++) {
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) ? (lim >> 1) : 0);
    }
    
    int *d_rev = nullptr;
    CHECK_CUDA(cudaMalloc(&d_rev, lim * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

    int *d_A = nullptr, *d_B = nullptr;
    CHECK_CUDA(cudaMalloc(&d_A, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_B, lim * sizeof(int)));
    
    std::vector<int> h_A(lim, 0), h_B(lim, 0);
    for(int i = 0; i < n; i++) h_A[i] = a[i];
    for(int i = 0; i < n; i++) h_B[i] = b[i];

    CHECK_CUDA(cudaMemcpy(d_A, h_A.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_B, h_B.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

    MontgomeryParams mont(p);
    int threads = 1024;
    int blocks = (lim + threads - 1) / threads;

    to_montgomery_kernel<<<blocks, threads>>>(d_A, mont, lim);
    to_montgomery_kernel<<<blocks, threads>>>(d_B, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());

    int *d_twiddles_fwd = nullptr, *d_twiddles_inv = nullptr;

    // 预计算旋转因子（如果需要）
    if (use_precomp) {
        std::vector<int> h_twiddles_fwd, h_twiddles_inv;
        precompute_twiddles_montgomery(h_twiddles_fwd, lim, p, false, mont);
        precompute_twiddles_montgomery(h_twiddles_inv, lim, p, true, mont);

        CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, lim * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&d_twiddles_inv, lim * sizeof(int)));
        CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    }

    auto total_start = std::chrono::high_resolution_clock::now();

    // 分别测量正向NTT时间
    double fft_a_time, fft_b_time;
    benchmark_ntt_performance(d_A, lim, false, p, mont, d_rev, d_twiddles_fwd, use_precomp, use_efficient, fft_a_time);
    benchmark_ntt_performance(d_B, lim, false, p, mont, d_rev, d_twiddles_fwd, use_precomp, use_efficient, fft_b_time);
    fft_time = fft_a_time + fft_b_time;

    // 点乘（时间很短，可以忽略）
    pointwise_mul_montgomery_kernel<<<blocks, threads>>>(d_A, d_B, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());

    // 测量逆向NTT时间
    benchmark_ntt_performance(d_A, lim, true, p, mont, d_rev, d_twiddles_inv, use_precomp, use_efficient, ifft_time);

    auto total_end = std::chrono::high_resolution_clock::now();
    total_time = std::chrono::duration<double, std::milli>(total_end - total_start).count();

    // 最终缩放
    unsigned int n_inv_p = mod_inverse_host((unsigned int)lim, (unsigned int)p);
    unsigned int factor_mont = mont.to_montgomery(n_inv_p);
    final_scaling_kernel<<<blocks, threads>>>(d_A, factor_mont, mont, 2 * n - 1);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    std::vector<int> h_res(2 * n - 1);
    CHECK_CUDA(cudaMemcpy(h_res.data(), d_A, (2 * n - 1) * sizeof(int), cudaMemcpyDeviceToHost));
    for(int i=0; i<2*n-1; ++i) a[i] = h_res[i];

    // 清理内存
    CHECK_CUDA(cudaFree(d_A));
    CHECK_CUDA(cudaFree(d_B));
    CHECK_CUDA(cudaFree(d_rev));
    if (d_twiddles_fwd != nullptr) {
        CHECK_CUDA(cudaFree(d_twiddles_fwd));
        CHECK_CUDA(cudaFree(d_twiddles_inv));
    }
}

int main() {
    printf("🔬 精确的Montgomery规约NTT性能对比\n");
    printf("========================================\n");

    for (int i = 0; i <= 3; ++i) {
        std::cout << "\n测试数据集 " << i << ":" << std::endl;
        
        int n, p;
        std::vector<int> a_vec(1 << 20), b_vec(1 << 20);
        int* a = a_vec.data();
        int* b = b_vec.data();
        fRead(a, b, &n, &p, i);
        
        printf("  n=%d, p=%d\n", n, p);
        printf("----------------------------------------\n");

        // 创建三个副本用于不同测试
        std::vector<int> a_naive(2 * n, 0), b_naive(2 * n, 0);
        std::vector<int> a_efficient(2 * n, 0), b_efficient(2 * n, 0);
        std::vector<int> a_precomp(2 * n, 0), b_precomp(2 * n, 0);

        for(int j = 0; j < n; ++j) {
            a_naive[j] = a_efficient[j] = a_precomp[j] = a[j];
            b_naive[j] = b_efficient[j] = b_precomp[j] = b[j];
        }

        double fft_time, ifft_time, total_time;

        // 1. 朴素无预计算版本
        printf("  朴素无预计算版本:\n");
        cuda_poly_multiply_montgomery_detailed(a_naive.data(), b_naive.data(), n, p, false, false, 
                                             fft_time, ifft_time, total_time);
        fCheck(a_naive.data(), n, i);
        printf("    FFT时间: %.3f ms, IFFT时间: %.3f ms, 总时间: %.3f ms\n", 
               fft_time, ifft_time, total_time);

        // 2. 高效无预计算版本
        printf("  高效无预计算版本:\n");
        cuda_poly_multiply_montgomery_detailed(a_efficient.data(), b_efficient.data(), n, p, false, true, 
                                             fft_time, ifft_time, total_time);
        fCheck(a_efficient.data(), n, i);
        printf("    FFT时间: %.3f ms, IFFT时间: %.3f ms, 总时间: %.3f ms\n", 
               fft_time, ifft_time, total_time);

        // 3. 预计算版本
        printf("  预计算版本:\n");
        double precomp_fft, precomp_ifft, precomp_total;
        cuda_poly_multiply_montgomery_detailed(a_precomp.data(), b_precomp.data(), n, p, true, false, 
                                             precomp_fft, precomp_ifft, precomp_total);
        fCheck(a_precomp.data(), n, i);
        printf("    FFT时间: %.3f ms, IFFT时间: %.3f ms, 总时间: %.3f ms\n", 
               precomp_fft, precomp_ifft, precomp_total);

        // 详细性能对比
        printf("\n  🔍 详细性能分析:\n");
        if(precomp_total > 0) {
            printf("    核心NTT加速比: %.2fx (预计算 vs 朴素)\n", 
                   (fft_time + ifft_time) / (precomp_fft + precomp_ifft));
            printf("    总体加速比: %.2fx\n", total_time / precomp_total);
        }
        
        printf("========================================\n");
    }
    
    printf("\n💡 说明:\n");
    printf("  - 使用CUDA事件精确测量核心NTT计算时间\n");
    printf("  - 移除了中间同步开销的干扰\n");
    printf("  - 分别显示FFT和IFFT的纯计算时间\n");
    
    return 0;
} 