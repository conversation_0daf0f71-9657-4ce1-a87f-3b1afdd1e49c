/**
 * @file complete_parallel_strategies.cpp
 * @brief 完整的并行策略实现
 * 
 * 基于现有项目中的所有并行实现，提供完整的并行策略支持
 */

#include "complete_ntt_engine.hpp"
#include <thread>
#include <future>
#include <queue>
#include <condition_variable>

#ifdef USE_MPI
#include <mpi.h>
#endif

#ifdef USE_CUDA
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

// ============ OpenMP任务并行实现 ============
void CompleteNTTEngine::openmp_task_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        // 使用OpenMP任务并行处理每个块
        #pragma omp parallel
        {
            #pragma omp single
            {
                for (int i = 0; i < n; i += len) {
                    #pragma omp task firstprivate(i, len, half, wn, modulus)
                    {
                        uint32_t w = 1;
                        for (int j = 0; j < half; j++) {
                            int u = data[i + j];
                            int v = (1LL * data[i + j + half] * w) % modulus;
                            data[i + j] = (u + v) % modulus;
                            data[i + j + half] = (u - v + modulus) % modulus;
                            w = (1LL * w * wn) % modulus;
                        }
                    }
                }
                #pragma omp taskwait
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        #pragma omp parallel for
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ pthread多线程实现 ============
struct ThreadData {
    std::vector<int>* data;
    int start;
    int len;
    int half;
    uint32_t wn;
    int modulus;
};

void* butterfly_worker(void* arg) {
    ThreadData* td = static_cast<ThreadData*>(arg);
    uint32_t w = 1;
    
    for (int j = 0; j < td->half; j++) {
        int u = (*td->data)[td->start + j];
        int v = (1LL * (*td->data)[td->start + j + td->half] * w) % td->modulus;
        (*td->data)[td->start + j] = (u + v) % td->modulus;
        (*td->data)[td->start + j + td->half] = (u - v + td->modulus) % td->modulus;
        w = (1LL * w * td->wn) % td->modulus;
    }
    
    return nullptr;
}

void CompleteNTTEngine::pthread_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    int num_threads = std::thread::hardware_concurrency();
    
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        std::vector<pthread_t> threads;
        std::vector<ThreadData> thread_data;
        
        int blocks_per_thread = std::max(1, (n / len) / num_threads);
        
        for (int t = 0; t < num_threads && t * blocks_per_thread * len < n; t++) {
            int start = t * blocks_per_thread * len;
            int end = std::min(start + blocks_per_thread * len, n);
            
            for (int i = start; i < end; i += len) {
                ThreadData td = {&data, i, len, half, wn, modulus};
                thread_data.push_back(td);
                
                pthread_t thread;
                pthread_create(&thread, nullptr, butterfly_worker, &thread_data.back());
                threads.push_back(thread);
            }
        }
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            pthread_join(thread, nullptr);
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ MPI分布式并行实现 ============
void CompleteNTTEngine::mpi_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_MPI
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    int n = data.size();
    int local_n = n / size;
    
    // 数据分发
    std::vector<int> local_data(local_n);
    MPI_Scatter(data.data(), local_n, MPI_INT, 
                local_data.data(), local_n, MPI_INT, 
                0, MPI_COMM_WORLD);
    
    // 本地NTT计算
    radix2_dit_ntt(local_data, modulus, inverse);
    
    // 全局蝶形运算（简化版本）
    for (int step = 1; step < size; step <<= 1) {
        int partner = rank ^ step;
        
        if (partner < size) {
            std::vector<int> recv_data(local_n);
            
            MPI_Sendrecv(local_data.data(), local_n, MPI_INT, partner, 0,
                        recv_data.data(), local_n, MPI_INT, partner, 0,
                        MPI_COMM_WORLD, MPI_STATUS_IGNORE);
            
            // 执行跨进程蝶形运算
            uint32_t w = fastPow(3, (modulus - 1) / (2 * step * local_n), modulus);
            if (inverse) w = modularInverse(w, modulus);
            
            for (int i = 0; i < local_n; i++) {
                if (rank < partner) {
                    int u = local_data[i];
                    int v = (1LL * recv_data[i] * w) % modulus;
                    local_data[i] = (u + v) % modulus;
                } else {
                    int u = recv_data[i];
                    int v = (1LL * local_data[i] * w) % modulus;
                    local_data[i] = (u - v + modulus) % modulus;
                }
            }
        }
    }
    
    // 数据收集
    MPI_Gather(local_data.data(), local_n, MPI_INT,
               data.data(), local_n, MPI_INT,
               0, MPI_COMM_WORLD);
#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

// ============ Radix-4 DIT实现 ============
void CompleteNTTEngine::radix4_dit_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 4; len <= n; len <<= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = (1LL * data[i + j + quarter] * w1) % modulus;
                int c = (1LL * data[i + j + 2*quarter] * w2) % modulus;
                int d = (1LL * data[i + j + 3*quarter] * w3) % modulus;
                
                // Radix-4蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (t2 + t4) % modulus;
                data[i + j + 2*quarter] = (t1 - t3 + modulus) % modulus;
                data[i + j + 3*quarter] = (t2 - t4 + modulus) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    // 处理剩余的Radix-2层
    for (int len = 2; len <= n && (len & (len-1)) == 0; len <<= 1) {
        if (len >= 4 && (len & 3) == 0) continue; // 已经被Radix-4处理
        
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ Radix-4 DIF实现 ============
void CompleteNTTEngine::radix4_dif_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // Radix-4 DIF蝶形运算
    for (int len = n; len >= 4; len >>= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = data[i + j + quarter];
                int c = data[i + j + 2*quarter];
                int d = data[i + j + 3*quarter];
                
                // Radix-4 DIF蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (1LL * (t2 + t4) * w1) % modulus;
                data[i + j + 2*quarter] = (1LL * (t1 - t3 + modulus) * w2) % modulus;
                data[i + j + 3*quarter] = (1LL * (t2 - t4 + modulus) * w3) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    bitReversePermutation(data);
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ CUDA GPU并行实现 ============
void CompleteNTTEngine::cuda_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_CUDA
    // TODO: 实现CUDA GPU并行
    // 目前回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

} // namespace HybridNTT
