#!/bin/bash

##############################################################################
# 智能混合并行NTT加速框架 - 自动构建和测试脚本
# Author: Hybrid Acceleration Framework
##############################################################################

set -e  # 出现错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未找到，请确保已安装"
        return 1
    fi
    return 0
}

# 检查编译环境
check_environment() {
    print_info "检查编译环境..."
    
    # 检查基础编译器
    if ! check_command g++; then
        return 1
    fi
    
    # 检查OpenMP支持
    if g++ -fopenmp -dM -E - </dev/null | grep -q "_OPENMP"; then
        print_success "OpenMP 支持已检测到"
        OPENMP_AVAILABLE=true
    else
        print_warning "OpenMP 支持未检测到"
        OPENMP_AVAILABLE=false
    fi
    
    # 检查MPI支持
    if check_command mpicxx; then
        print_success "MPI 支持已检测到"
        MPI_AVAILABLE=true
    else
        print_warning "MPI 支持未检测到"
        MPI_AVAILABLE=false
    fi
    
    # 检查CUDA支持
    if check_command nvcc; then
        print_success "CUDA 支持已检测到"
        CUDA_AVAILABLE=true
    else
        print_warning "CUDA 支持未检测到"
        CUDA_AVAILABLE=false
    fi
    
    print_success "环境检查完成"
    return 0
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    mkdir -p bin
    mkdir -p files
    mkdir -p logs
    mkdir -p results
    
    print_success "目录创建完成"
}

# 编译基础版本（仅CPU）
build_basic() {
    print_info "编译基础版本（CPU + OpenMP + SIMD）..."

    COMPILE_FLAGS="-std=c++17 -O3 -march=native -Wall -Wextra -I./src"
    SOURCE_FILES="main_hybrid_ntt.cpp src/parallel/parallel_coordinator.cpp"
    
    if [ "$OPENMP_AVAILABLE" = true ]; then
        COMPILE_FLAGS="$COMPILE_FLAGS -fopenmp"
    fi

    # 添加NUMA支持（如果可用）
    if pkg-config --exists libnuma 2>/dev/null; then
        COMPILE_FLAGS="$COMPILE_FLAGS -lnuma"
        print_info "检测到NUMA支持，已添加链接库"
    fi

    g++ $COMPILE_FLAGS $SOURCE_FILES -o bin/hybrid_ntt_basic

    if [ $? -eq 0 ]; then
        print_success "基础版本编译成功: bin/hybrid_ntt_basic"
    else
        print_error "基础版本编译失败"
        return 1
    fi
}

# 编译MPI版本
build_mpi() {
    if [ "$MPI_AVAILABLE" = false ]; then
        print_warning "跳过MPI版本编译（MPI不可用）"
        return 0
    fi

    print_info "编译MPI版本..."

    COMPILE_FLAGS="-std=c++17 -O3 -march=native -Wall -Wextra -DMPI_VERSION -I./src"
    SOURCE_FILES="main_hybrid_ntt.cpp src/parallel/parallel_coordinator.cpp"

    if [ "$OPENMP_AVAILABLE" = true ]; then
        COMPILE_FLAGS="$COMPILE_FLAGS -fopenmp"
    fi

    # 添加NUMA支持
    if pkg-config --exists libnuma 2>/dev/null; then
        COMPILE_FLAGS="$COMPILE_FLAGS -lnuma"
    fi

    mpicxx $COMPILE_FLAGS $SOURCE_FILES -o bin/hybrid_ntt_mpi

    if [ $? -eq 0 ]; then
        print_success "MPI版本编译成功: bin/hybrid_ntt_mpi"
    else
        print_error "MPI版本编译失败"
        return 1
    fi
}

# 编译CUDA版本
build_cuda() {
    if [ "$CUDA_AVAILABLE" = false ]; then
        print_warning "跳过CUDA版本编译（CUDA不可用）"
        return 0
    fi
    
    print_info "编译CUDA版本..."
    
    NVCC_FLAGS="-std=c++17 -O3 -arch=sm_75"
    COMPILER_FLAGS="-fopenmp"
    
    if [ "$OPENMP_AVAILABLE" = true ]; then
        nvcc $NVCC_FLAGS -Xcompiler "$COMPILER_FLAGS" main_hybrid_ntt.cpp -o bin/hybrid_ntt_cuda
    else
        nvcc $NVCC_FLAGS main_hybrid_ntt.cpp -o bin/hybrid_ntt_cuda
    fi
    
    if [ $? -eq 0 ]; then
        print_success "CUDA版本编译成功: bin/hybrid_ntt_cuda"
    else
        print_error "CUDA版本编译失败"
        return 1
    fi
}

# 运行基础测试
run_basic_test() {
    print_info "运行基础版本测试..."
    
    if [ ! -f "bin/hybrid_ntt_basic" ]; then
        print_error "基础版本可执行文件不存在"
        return 1
    fi
    
    ./bin/hybrid_ntt_basic > logs/basic_test.log 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "基础版本测试通过"
        grep "所有测试用例通过" logs/basic_test.log > /dev/null
        if [ $? -eq 0 ]; then
            print_success "所有测试用例验证通过"
        else
            print_warning "部分测试用例可能失败，请检查日志"
        fi
    else
        print_error "基础版本测试失败，请检查日志: logs/basic_test.log"
        return 1
    fi
}

# 运行MPI测试
run_mpi_test() {
    if [ "$MPI_AVAILABLE" = false ] || [ ! -f "bin/hybrid_ntt_mpi" ]; then
        print_warning "跳过MPI测试（MPI版本不可用）"
        return 0
    fi
    
    print_info "运行MPI版本测试（2进程）..."
    
    mpirun -np 2 ./bin/hybrid_ntt_mpi > logs/mpi_test.log 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "MPI版本测试通过"
    else
        print_error "MPI版本测试失败，请检查日志: logs/mpi_test.log"
        return 1
    fi
}

# 运行CUDA测试
run_cuda_test() {
    if [ "$CUDA_AVAILABLE" = false ] || [ ! -f "bin/hybrid_ntt_cuda" ]; then
        print_warning "跳过CUDA测试（CUDA版本不可用）"
        return 0
    fi
    
    print_info "运行CUDA版本测试..."
    
    ./bin/hybrid_ntt_cuda > logs/cuda_test.log 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "CUDA版本测试通过"
    else
        print_error "CUDA版本测试失败，请检查日志: logs/cuda_test.log"
        return 1
    fi
}

# 集成测试和性能验证
run_integration_tests() {
    print_info "运行集成测试和性能验证..."

    # 编译集成测试程序
    print_info "编译集成测试程序..."
    g++ -std=c++17 -O3 -march=native -Wall -Wextra -I./src -fopenmp \
        test_integration.cpp src/parallel/parallel_coordinator.cpp \
        -lnuma -o bin/test_integration

    if [ $? -eq 0 ]; then
        print_success "集成测试程序编译成功"
        print_info "运行集成测试..."
        ./bin/test_integration > logs/integration_test.log 2>&1

        if [ $? -eq 0 ]; then
            print_success "集成测试完成"
        else
            print_error "集成测试失败"
        fi
    else
        print_error "集成测试程序编译失败"
    fi
}

# 性能对比测试
run_performance_comparison() {
    print_info "运行性能对比测试..."

    # 编译性能对比程序
    print_info "编译性能对比程序..."
    g++ -std=c++17 -O3 -march=native -Wall -Wextra -I./src -fopenmp \
        performance_comparison.cpp src/parallel/parallel_coordinator.cpp \
        -lnuma -o bin/performance_comparison

    if [ $? -eq 0 ]; then
        print_success "性能对比程序编译成功"
        print_info "运行性能对比测试..."
        ./bin/performance_comparison > logs/performance_comparison.log 2>&1

        if [ $? -eq 0 ]; then
            print_success "性能对比测试完成"
        else
            print_error "性能对比测试失败"
        fi
    else
        print_error "性能对比程序编译失败"
    fi
}

# 性能基准测试
run_benchmark() {
    print_info "运行性能基准测试..."

    if [ -f "bin/hybrid_ntt_basic" ]; then
        print_info "基础版本性能测试..."
        time ./bin/hybrid_ntt_basic > logs/benchmark_basic.log 2>&1
    fi

    if [ "$MPI_AVAILABLE" = true ] && [ -f "bin/hybrid_ntt_mpi" ]; then
        print_info "MPI版本性能测试（4进程）..."
        time mpirun -np 4 ./bin/hybrid_ntt_mpi > logs/benchmark_mpi.log 2>&1
    fi

    if [ "$CUDA_AVAILABLE" = true ] && [ -f "bin/hybrid_ntt_cuda" ]; then
        print_info "CUDA版本性能测试..."
        time ./bin/hybrid_ntt_cuda > logs/benchmark_cuda.log 2>&1
    fi

    print_success "性能基准测试完成，结果保存在 logs/ 目录"
}

# 生成性能报告
generate_report() {
    print_info "生成性能分析报告..."
    
    cat > results/performance_summary.md << EOF
# 混合并行NTT性能测试报告

## 测试环境
- 编译时间: $(date)
- 系统信息: $(uname -a)
- CPU信息: $(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2 | xargs)
- 内存信息: $(free -h | grep "Mem:" | awk '{print $2}')

## 编译状态
- OpenMP: $([ "$OPENMP_AVAILABLE" = true ] && echo "✓ 可用" || echo "✗ 不可用")
- MPI: $([ "$MPI_AVAILABLE" = true ] && echo "✓ 可用" || echo "✗ 不可用")  
- CUDA: $([ "$CUDA_AVAILABLE" = true ] && echo "✓ 可用" || echo "✗ 不可用")

## 性能结果
详细的性能数据请查看 hybrid_results.csv 文件。

## 测试日志
- 基础版本: logs/basic_test.log
- MPI版本: logs/mpi_test.log
- CUDA版本: logs/cuda_test.log

## 框架特性
✓ 硬件自适应检测与配置
✓ 智能策略选择与优化  
✓ 多层次混合并行加速
✓ 自动算法变体选择
✓ 动态负载均衡调度
✓ 兼容原有项目接口
EOF

    print_success "性能报告已生成: results/performance_summary.md"
}

# 清理函数
cleanup() {
    print_info "清理编译文件..."
    rm -f *.o *.obj
    print_success "清理完成"
}

# 显示使用帮助
show_help() {
    echo "智能混合并行NTT加速框架 - 构建和测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --check          仅检查编译环境"
    echo "  --build          仅编译所有版本"
    echo "  --test           仅运行基础测试"
    echo "  --integration    运行集成测试和性能验证"
    echo "  --comparison     运行性能对比测试"
    echo "  --full-test      运行所有测试（基础+集成+对比）"
    echo "  --benchmark      仅运行性能基准测试"
    echo "  --clean          清理编译文件"
    echo "  --help           显示此帮助信息"
    echo "  (无参数)         执行完整的构建和测试流程"
    echo
}

# 主函数
main() {
    echo "====================================================="
    echo "  智能混合并行NTT加速框架 - 构建和测试脚本"
    echo "====================================================="
    
    case "${1:-all}" in
        --check)
            check_environment
            ;;
        --build)
            check_environment
            create_directories
            build_basic
            build_mpi
            build_cuda
            ;;
        --test)
            run_basic_test
            run_mpi_test
            run_cuda_test
            ;;
        --integration)
            run_integration_tests
            ;;
        --comparison)
            run_performance_comparison
            ;;
        --full-test)
            run_basic_test
            run_mpi_test
            run_cuda_test
            run_integration_tests
            run_performance_comparison
            ;;
        --benchmark)
            run_benchmark
            generate_report
            ;;
        --clean)
            cleanup
            ;;
        --help)
            show_help
            ;;
        all|*)
            # 完整流程
            check_environment
            create_directories
            
            # 编译阶段
            print_info "=== 编译阶段 ==="
            build_basic
            build_mpi
            build_cuda
            
            # 测试阶段
            print_info "=== 基础测试阶段 ==="
            run_basic_test
            run_mpi_test
            run_cuda_test

            # 集成测试阶段
            print_info "=== 集成测试阶段 ==="
            run_integration_tests

            # 性能对比测试阶段
            print_info "=== 性能对比测试阶段 ==="
            run_performance_comparison

            # 性能基准测试
            print_info "=== 性能基准测试 ==="
            run_benchmark
            
            # 生成报告
            generate_report
            
            print_success "所有任务完成！"
            print_info "查看结果:"
            print_info "  - 可执行文件: bin/"
            print_info "  - 测试日志: logs/"
            print_info "  - 性能结果: hybrid_results.csv"
            print_info "  - 分析报告: results/performance_summary.md"
            ;;
    esac
}

# 运行主函数
main "$@"