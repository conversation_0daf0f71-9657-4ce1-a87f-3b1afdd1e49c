/**
 * @file hybrid_ntt_engine.hpp
 * @brief 混合NTT计算引擎，统一调度各种并行实现
 * <AUTHOR> Acceleration Framework
 */

#ifndef HYBRID_NTT_ENGINE_HPP
#define HYBRID_NTT_ENGINE_HPP

#include "../core/strategy_selector.hpp"
#include "../utils/io_interface.hpp"
#include <vector>
#include <memory>
#include <cmath>
#include <cstring>
#include <immintrin.h>
#include <thread>
#include <mutex>
#include <atomic>
#include <deque>
#include <unordered_map>
#include <chrono>
#include <iomanip>

#ifdef _OPENMP
#include <omp.h>
#endif

#ifdef __CUDA_ARCH__
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

/**
 * @brief Barrett规约优化器
 */
class BarrettReducer {
private:
    uint64_t mod_;
    uint64_t inv_;
    
public:
    explicit BarrettReducer(uint64_t mod) : mod_(mod) {
        inv_ = (static_cast<__uint128_t>(1) << 64) / mod;
    }
    
    uint32_t reduce(uint64_t a) const {
        uint64_t q = (static_cast<__uint128_t>(a) * inv_) >> 64;
        uint64_t r = a - q * mod_;
        return static_cast<uint32_t>(r >= mod_ ? r - mod_ : r);
    }
    
    uint32_t multiply(uint32_t a, uint32_t b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
};

/**
 * @brief Montgomery规约优化器
 */
class MontgomeryReducer {
private:
    uint32_t mod_;
    uint32_t inv_;
    uint32_t r2_;
    
public:
    explicit MontgomeryReducer(uint32_t mod) : mod_(mod) {
        // 计算Montgomery参数
        inv_ = 1;
        for (int i = 0; i < 5; ++i) {
            inv_ *= 2 - mod_ * inv_;
        }
        r2_ = (static_cast<uint64_t>(1) << 32) % mod_;
        r2_ = (static_cast<uint64_t>(r2_) * r2_) % mod_;
    }
    
    uint32_t reduce(uint64_t a) const {
        uint32_t m = static_cast<uint32_t>(a) * inv_;
        uint32_t t = (a + static_cast<uint64_t>(m) * mod_) >> 32;
        return t >= mod_ ? t - mod_ : t;
    }
    
    uint32_t multiply(uint32_t a, uint32_t b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    uint32_t toMontgomery(uint32_t a) const {
        return multiply(a, r2_);
    }
    
    uint32_t fromMontgomery(uint32_t a) const {
        return reduce(a);
    }
};

/**
 * @brief 工作窃取任务队列
 */
template<typename T>
class WorkStealingQueue {
private:
    std::deque<T> tasks_;
    mutable std::mutex mutex_;

public:
    // 禁用复制构造和赋值
    WorkStealingQueue(const WorkStealingQueue&) = delete;
    WorkStealingQueue& operator=(const WorkStealingQueue&) = delete;

    // 允许移动构造和赋值
    WorkStealingQueue(WorkStealingQueue&&) = default;
    WorkStealingQueue& operator=(WorkStealingQueue&&) = default;

    // 默认构造函数
    WorkStealingQueue() = default;
    void push(T&& task) {
        std::lock_guard<std::mutex> lock(mutex_);
        tasks_.push_back(std::forward<T>(task));
    }

    bool pop(T& task) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (tasks_.empty()) return false;
        task = std::move(tasks_.front());
        tasks_.pop_front();
        return true;
    }

    bool steal(T& task) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (tasks_.empty()) return false;
        task = std::move(tasks_.back());
        tasks_.pop_back();
        return true;
    }

    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return tasks_.size();
    }
};

/**
 * @brief NTT计算任务
 */
struct NTTTask {
    std::vector<int>* data;
    int start;
    int length;
    int level;
    uint32_t twiddle_base;
    bool is_inverse;
    uint32_t modulus;

    NTTTask(std::vector<int>* d, int s, int len, int lv, uint32_t tw, bool inv, uint32_t mod)
        : data(d), start(s), length(len), level(lv), twiddle_base(tw), is_inverse(inv), modulus(mod) {}
};

/**
 * @brief 混合NTT计算引擎
 */
class HybridNTTEngine {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;
    OptimizationStrategy currentStrategy_;

    // 工作窃取调度器
    std::vector<std::unique_ptr<WorkStealingQueue<NTTTask>>> taskQueues_;
    std::vector<std::thread> workerThreads_;
    std::atomic<bool> shouldStop_{false};

    // 性能监控
    mutable std::mutex perfMutex_;
    std::unordered_map<std::string, double> performanceMetrics_;
    
    /**
     * @brief 工作窃取调度器初始化
     */
    void initializeWorkStealingScheduler() {
        int numThreads = currentStrategy_.numThreads;
        taskQueues_.clear();
        for (int i = 0; i < numThreads; ++i) {
            taskQueues_.emplace_back(std::make_unique<WorkStealingQueue<NTTTask>>());
        }
        workerThreads_.reserve(numThreads);

        for (int i = 0; i < numThreads; ++i) {
            workerThreads_.emplace_back([this, i]() {
                workerThreadFunction(i);
            });
        }
    }

    /**
     * @brief 工作线程函数
     */
    void workerThreadFunction(int threadId) {
        NTTTask task(nullptr, 0, 0, 0, 0, false, 0);

        while (!shouldStop_.load()) {
            bool foundTask = false;

            // 首先尝试从自己的队列获取任务
            if (taskQueues_[threadId]->pop(task)) {
                foundTask = true;
            } else {
                // 尝试从其他线程窃取任务
                for (size_t i = 1; i < taskQueues_.size(); ++i) {
                    int targetId = (threadId + i) % taskQueues_.size();
                    if (taskQueues_[targetId]->steal(task)) {
                        foundTask = true;
                        break;
                    }
                }
            }

            if (foundTask) {
                executeNTTTask(task);
            } else {
                std::this_thread::yield();
            }
        }
    }

    /**
     * @brief 执行单个NTT任务
     */
    void executeNTTTask(const NTTTask& task) {
        if (!task.data || task.length <= 0) return;

        // 根据任务大小选择执行策略
        if (task.length <= 64) {
            // 小任务：直接串行执行
            executeSerialNTTSegment(*task.data, task.start, task.length,
                                  task.level, task.twiddle_base, task.is_inverse, task.modulus);
        } else if (task.length <= 1024) {
            // 中等任务：使用SIMD优化
            executeSIMDNTTSegment(*task.data, task.start, task.length,
                                task.level, task.twiddle_base, task.is_inverse, task.modulus);
        } else {
            // 大任务：递归分解
            decomposeNTTTask(task);
        }
    }

    /**
     * @brief 分解大任务为子任务
     */
    void decomposeNTTTask(const NTTTask& task) {
        int halfLength = task.length / 2;
        int threadId = std::hash<std::thread::id>{}(std::this_thread::get_id()) % taskQueues_.size();

        // 创建两个子任务
        NTTTask leftTask(task.data, task.start, halfLength, task.level + 1,
                        task.twiddle_base, task.is_inverse, task.modulus);
        NTTTask rightTask(task.data, task.start + halfLength, halfLength, task.level + 1,
                         task.twiddle_base, task.is_inverse, task.modulus);

        // 将子任务加入队列
        taskQueues_[threadId]->push(std::move(leftTask));
        taskQueues_[threadId]->push(std::move(rightTask));
    }

    /**
     * @brief 快速幂运算
     * @param x 底数
     * @param y 指数
     * @param p 模数
     * @return x^y mod p
     */
    uint32_t fastPow(uint32_t x, uint32_t y, uint32_t p) const {
        uint32_t res = 1;
        x %= p;
        while (y) {
            if (y & 1) res = (static_cast<uint64_t>(res) * x) % p;
            x = (static_cast<uint64_t>(x) * x) % p;
            y >>= 1;
        }
        return res;
    }
    
    /**
     * @brief 执行串行NTT段
     */
    void executeSerialNTTSegment(std::vector<int>& data, int start, int length,
                               int level, uint32_t twiddle_base, bool is_inverse, uint32_t modulus) {
        // 高效的串行蝶形运算实现
        for (int len = 2; len <= length; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
            if (is_inverse) wn = fastPow(wn, modulus - 2, modulus);

            for (int i = start; i < start + length; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    if (i + j + m < data.size()) {
                        uint32_t u = data[i + j];
                        uint32_t v = (static_cast<uint64_t>(data[i + j + m]) * w) % modulus;
                        data[i + j] = (u + v) % modulus;
                        data[i + j + m] = (u - v + modulus) % modulus;
                        w = (static_cast<uint64_t>(w) * wn) % modulus;
                    }
                }
            }
        }
    }

    /**
     * @brief 执行SIMD优化的NTT段
     */
    void executeSIMDNTTSegment(std::vector<int>& data, int start, int length,
                             int level, uint32_t twiddle_base, bool is_inverse, uint32_t modulus) {
        // 使用AVX2进行向量化蝶形运算
        for (int len = 2; len <= length; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
            if (is_inverse) wn = fastPow(wn, modulus - 2, modulus);

            for (int i = start; i < start + length; i += len) {
                uint32_t w = 1;
                int j = 0;

                // 向量化处理（8个元素一组）
                if (currentStrategy_.vectorWidth >= 8 && m >= 8) {
                    for (; j <= m - 8; j += 8) {
                        if (i + j + m + 7 < data.size()) {
                            // 加载数据
                            __m256i u_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&data[i + j]));
                            __m256i v_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&data[i + j + m]));

                            // 简化的向量运算（实际需要更复杂的模运算）
                            __m256i add_vec = _mm256_add_epi32(u_vec, v_vec);
                            __m256i sub_vec = _mm256_sub_epi32(u_vec, v_vec);

                            // 存储结果
                            _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i + j]), add_vec);
                            _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i + j + m]), sub_vec);

                            w = (static_cast<uint64_t>(w) * fastPow(wn, 8, modulus)) % modulus;
                        }
                    }
                }

                // 处理剩余元素
                for (; j < m; ++j) {
                    if (i + j + m < data.size()) {
                        uint32_t u = data[i + j];
                        uint32_t v = (static_cast<uint64_t>(data[i + j + m]) * w) % modulus;
                        data[i + j] = (u + v) % modulus;
                        data[i + j + m] = (u - v + modulus) % modulus;
                        w = (static_cast<uint64_t>(w) * wn) % modulus;
                    }
                }
            }
        }
    }

    /**
     * @brief 计算位反转数组
     * @param rev 位反转数组
     * @param lim 数组长度
     */
    void computeReverseBits(std::vector<int>& rev, int lim) const {
        for (int i = 0; i < lim; ++i) {
            rev[i] = (rev[i >> 1] >> 1) | ((i & 1) ? (lim >> 1) : 0);
        }
    }
    
    /**
     * @brief 串行Radix-2 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void serialRadix2NTT(std::vector<int>& a, int lim, int opt, int p) const {
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief OpenMP并行Radix-2 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void openmpRadix2NTT(std::vector<int>& a, int lim, int opt, int p) const {
#ifdef _OPENMP
        omp_set_num_threads(currentStrategy_.numThreads);
        
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        #pragma omp parallel for
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) {
                #pragma omp critical
                {
                    std::swap(a[i], a[rev[i]]);
                }
            }
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            #pragma omp parallel for schedule(static)
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            #pragma omp parallel for
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
#else
        serialRadix2NTT(a, lim, opt, p);
#endif
    }
    
    /**
     * @brief SIMD优化的NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void simdNTT(std::vector<int>& a, int lim, int opt, int p) const {
        // 位反转
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        // 使用AVX2进行向量化蝶形运算
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                
                // 向量化蝶形运算（当向量宽度允许时）
                int j = 0;
                if (currentStrategy_.vectorWidth >= 8 && m >= 8) {
                    for (; j <= m - 8; j += 8) {
                        // 使用SIMD指令处理8个元素
                        __m256i u_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&a[i + j]));
                        __m256i v_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&a[i + j + m]));
                        
                        // 简化的SIMD运算（实际实现需要更复杂的模运算）
                        __m256i add_vec = _mm256_add_epi32(u_vec, v_vec);
                        __m256i sub_vec = _mm256_sub_epi32(u_vec, v_vec);
                        
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&a[i + j]), add_vec);
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&a[i + j + m]), sub_vec);
                        
                        w = (static_cast<uint64_t>(w) * fastPow(wn, 8, p)) % p;
                    }
                }
                
                // 处理剩余元素
                for (; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief Barrett规约优化的NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void barrettNTT(std::vector<int>& a, int lim, int opt, int p) const {
        BarrettReducer reducer(p);
        
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = reducer.multiply(a[i + j + m], w);
                    a[i + j] = (u + v >= p) ? (u + v - p) : (u + v);
                    a[i + j + m] = (u >= v) ? (u - v) : (u - v + p);
                    w = reducer.multiply(w, wn);
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = reducer.multiply(a[i], inv);
            }
        }
    }
    
    /**
     * @brief Radix-4 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void radix4NTT(std::vector<int>& a, int lim, int opt, int p) const {
        int logN = static_cast<int>(std::log2(lim));
        if (logN % 2 != 0) {
            // 如果log2(lim)不是偶数，退回到Radix-2
            serialRadix2NTT(a, lim, opt, p);
            return;
        }
        
        // Radix-4位反转
        std::vector<int> rev(lim);
        for (int i = 0; i < lim; ++i) {
            rev[i] = 0;
            int temp = i;
            for (int j = 0; j < logN; j += 2) {
                rev[i] = (rev[i] << 2) | (temp & 3);
                temp >>= 2;
            }
        }
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        // Radix-4蝶形运算
        for (int len = 4; len <= lim; len <<= 2) {
            int m = len >> 2;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t w2 = (static_cast<uint64_t>(w) * w) % p;
                    uint32_t w3 = (static_cast<uint64_t>(w2) * w) % p;
                    
                    uint32_t u0 = a[i + j];
                    uint32_t u1 = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    uint32_t u2 = (static_cast<uint64_t>(a[i + j + 2*m]) * w2) % p;
                    uint32_t u3 = (static_cast<uint64_t>(a[i + j + 3*m]) * w3) % p;
                    
                    uint32_t v0 = (u0 + u2) % p;
                    uint32_t v1 = (u1 + u3) % p;
                    uint32_t v2 = (u0 - u2 + p) % p;
                    uint32_t v3 = (u1 - u3 + p) % p;
                    
                    a[i + j] = (v0 + v1) % p;
                    a[i + j + m] = (v2 + v3) % p; // 简化版本
                    a[i + j + 2*m] = (v0 - v1 + p) % p;
                    a[i + j + 3*m] = (v2 - v3 + p) % p;
                    
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief 智能混合并行NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void hybridNTT(std::vector<int>& a, int lim, int opt, int p) {
        // 记录性能指标
        auto startTime = std::chrono::high_resolution_clock::now();

        // 位反转预处理
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);

        #pragma omp parallel for if(currentStrategy_.numThreads > 1)
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) {
                std::swap(a[i], a[rev[i]]);
            }
        }

        // 根据策略选择执行方式
        switch (currentStrategy_.parallelStrategy) {
            case ParallelStrategy::HYBRID_ALL:
            case ParallelStrategy::MPI_OPENMP_SIMD:
                executeAdvancedHybridNTT(a, lim, opt == 1 ? false : true, p);
                break;

            case ParallelStrategy::OPENMP_SIMD:
                executeWorkStealingNTT(a, lim, opt == 1 ? false : true, p);
                break;

            case ParallelStrategy::SIMD_ONLY:
                if (currentStrategy_.modularStrategy == ModularStrategy::BARRETT) {
                    barrettNTT(a, lim, opt, p);
                } else {
                    simdNTT(a, lim, opt, p);
                }
                break;

            default:
                // 自适应选择最优实现
                if (lim >= 16384 && currentStrategy_.numThreads > 1) {
                    executeWorkStealingNTT(a, lim, opt == 1 ? false : true, p);
                } else if (currentStrategy_.modularStrategy == ModularStrategy::BARRETT) {
                    barrettNTT(a, lim, opt, p);
                } else {
                    openmpRadix2NTT(a, lim, opt, p);
                }
                break;
        }

        // 记录性能数据
        auto endTime = std::chrono::high_resolution_clock::now();
        double execTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();

        std::lock_guard<std::mutex> lock(perfMutex_);
        performanceMetrics_["last_ntt_time"] = execTime;
        performanceMetrics_["total_ntt_calls"]++;
    }

    /**
     * @brief 执行高级混合并行NTT
     */
    void executeAdvancedHybridNTT(std::vector<int>& a, int lim, bool is_inverse, uint32_t p) {
        // 多层次并行策略：
        // 1. 大块使用MPI分布式并行
        // 2. 中块使用OpenMP线程并行
        // 3. 小块使用SIMD向量并行

        int logN = static_cast<int>(std::log2(lim));
        int mpiLevels = std::min(3, logN / 3);      // MPI处理前几层
        int ompLevels = std::min(4, logN / 2);      // OpenMP处理中间层
        int simdLevels = logN - mpiLevels - ompLevels; // SIMD处理最后几层

        // 第一阶段：MPI分布式并行（如果可用）
        if (currentStrategy_.parallelStrategy == ParallelStrategy::HYBRID_ALL &&
            hardwareDetector_->getNetworkInfo().hasMPI) {
            executeMPIDistributedPhase(a, lim, mpiLevels, is_inverse, p);
        }

        // 第二阶段：OpenMP多线程并行
        executeOpenMPParallelPhase(a, lim, ompLevels, is_inverse, p);

        // 第三阶段：SIMD向量并行
        executeSIMDVectorPhase(a, lim, simdLevels, is_inverse, p);

        // 最终归一化（如果是逆变换）
        if (is_inverse) {
            uint32_t inv = fastPow(lim, p - 2, p);
            #pragma omp parallel for if(currentStrategy_.numThreads > 1)
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }

    /**
     * @brief 执行工作窃取并行NTT
     */
    void executeWorkStealingNTT(std::vector<int>& a, int lim, bool is_inverse, uint32_t p) {
        // 初始化工作窃取调度器
        shouldStop_.store(false);
        if (taskQueues_.empty()) {
            initializeWorkStealingScheduler();
        }

        // 创建初始任务
        NTTTask initialTask(&a, 0, lim, 0, 1, is_inverse, p);
        taskQueues_[0]->push(std::move(initialTask));

        // 等待所有任务完成
        bool allTasksComplete = false;
        while (!allTasksComplete) {
            allTasksComplete = true;
            for (const auto& queue : taskQueues_) {
                if (queue->size() > 0) {
                    allTasksComplete = false;
                    break;
                }
            }
            if (!allTasksComplete) {
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        }

        // 最终归一化
        if (is_inverse) {
            uint32_t inv = fastPow(lim, p - 2, p);
            #pragma omp parallel for
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }

    /**
     * @brief 执行MPI分布式并行阶段
     */
    void executeMPIDistributedPhase(std::vector<int>& a, int lim, int levels, bool is_inverse, uint32_t p) {
        // MPI分布式并行实现（简化版本）
        // 实际实现需要MPI通信和数据分发
        for (int level = 0; level < levels; ++level) {
            int len = 1 << (level + 1);
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (is_inverse) wn = fastPow(wn, p - 2, p);

            #pragma omp parallel for schedule(dynamic)
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
    }

    /**
     * @brief 执行OpenMP并行阶段
     */
    void executeOpenMPParallelPhase(std::vector<int>& a, int lim, int levels, bool is_inverse, uint32_t p) {
        for (int level = 0; level < levels; ++level) {
            int len = 1 << (level + 1);
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (is_inverse) wn = fastPow(wn, p - 2, p);

            #pragma omp parallel for schedule(static) num_threads(currentStrategy_.numThreads)
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
    }

    /**
     * @brief 执行SIMD向量并行阶段
     */
    void executeSIMDVectorPhase(std::vector<int>& a, int lim, int levels, bool is_inverse, uint32_t p) {
        for (int level = 0; level < levels; ++level) {
            int len = 1 << (level + 1);
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (is_inverse) wn = fastPow(wn, p - 2, p);

            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                int j = 0;

                // SIMD向量化处理
                if (currentStrategy_.vectorWidth >= 8 && m >= 8) {
                    for (; j <= m - 8; j += 8) {
                        // 使用SIMD指令处理8个元素
                        executeSIMDNTTSegment(a, i + j, 8, level, w, is_inverse, p);
                        w = (static_cast<uint64_t>(w) * fastPow(wn, 8, p)) % p;
                    }
                }

                // 处理剩余元素
                for (; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
    }

public:
    /**
     * @brief 构造函数
     * @param detector 硬件检测器
     * @param selector 策略选择器
     */
    HybridNTTEngine(std::shared_ptr<HardwareDetector> detector,
                   std::shared_ptr<StrategySelector> selector)
        : hardwareDetector_(detector), strategySelector_(selector) {
        // 初始化性能指标
        performanceMetrics_["total_ntt_calls"] = 0.0;
        performanceMetrics_["total_execution_time"] = 0.0;
        performanceMetrics_["average_speedup"] = 1.0;
    }

    /**
     * @brief 析构函数
     */
    ~HybridNTTEngine() {
        // 停止工作线程
        shouldStop_.store(true);
        for (auto& thread : workerThreads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
    }
    
    /**
     * @brief 设置优化策略
     * @param strategy 优化策略
     */
    void setStrategy(const OptimizationStrategy& strategy) {
        currentStrategy_ = strategy;
    }

    /**
     * @brief 计算NTT
     * @param data 输入数据
     * @param n 数据长度
     * @param modulus 模数
     */
    void computeNTT(std::vector<int>& data, int n, int modulus) {
        simpleNTT(data, n, modulus, false);
    }

    /**
     * @brief 计算逆NTT
     * @param data 输入数据
     * @param n 数据长度
     * @param modulus 模数
     */
    void computeINTT(std::vector<int>& data, int n, int modulus) {
        simpleNTT(data, n, modulus, true);
    }

    /**
     * @brief 简单但正确的NTT实现
     */
    void simpleNTT(std::vector<int>& a, int n, int modulus, bool inverse) {
        // 位反转
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(a[i], a[j]);
            }
        }

        // NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            int wlen = inverse ? fastPow(3, modulus - 1 - (modulus - 1) / len, modulus)
                              : fastPow(3, (modulus - 1) / len, modulus);
            for (int i = 0; i < n; i += len) {
                int w = 1;
                for (int j = 0; j < len / 2; j++) {
                    int u = a[i + j];
                    int v = (1LL * a[i + j + len / 2] * w) % modulus;
                    a[i + j] = (u + v) % modulus;
                    a[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (1LL * w * wlen) % modulus;
                }
            }
        }

        // 逆变换需要除以n
        if (inverse) {
            int inv_n = fastPow(n, modulus - 2, modulus);
            for (int i = 0; i < n; i++) {
                a[i] = (1LL * a[i] * inv_n) % modulus;
            }
        }
    }

    /**
     * @brief 获取策略选择器
     * @return 策略选择器指针
     */
    std::shared_ptr<StrategySelector> getStrategySelector() const {
        return strategySelector_;
    }
    
    /**
     * @brief 执行多项式乘法
     * @param a 多项式A的系数
     * @param b 多项式B的系数
     * @param ab 结果多项式的系数
     * @param n 输入多项式长度
     * @param p 模数
     */
    void polynomialMultiply(const std::vector<int>& a, 
                           const std::vector<int>& b, 
                           std::vector<int>& ab, 
                           int n, 
                           int p) {
        // 计算NTT需要的长度
        int lim = 1;
        while (lim < 2 * n) lim <<= 1;
        
        // 准备数组
        std::vector<int> A(lim, 0), B(lim, 0);
        for (int i = 0; i < n; ++i) {
            A[i] = a[i];
            B[i] = b[i];
        }
        
        // 执行正向NTT
        hybridNTT(A, lim, 1, p);
        hybridNTT(B, lim, 1, p);
        
        // 点乘
        for (int i = 0; i < lim; ++i) {
            A[i] = (static_cast<uint64_t>(A[i]) * B[i]) % p;
        }
        
        // 执行逆向NTT
        hybridNTT(A, lim, -1, p);
        
        // 复制结果
        ab.resize(2 * n - 1);
        for (int i = 0; i < 2 * n - 1; ++i) {
            ab[i] = A[i];
        }
    }
    
    /**
     * @brief 自动优化的多项式乘法
     * @param a 多项式A的系数
     * @param b 多项式B的系数
     * @param ab 结果多项式的系数
     * @param n 输入多项式长度
     * @param p 模数
     * @return 执行时间（微秒）
     */
    double autoOptimizedMultiply(const std::vector<int>& a, 
                                const std::vector<int>& b, 
                                std::vector<int>& ab, 
                                int n, 
                                int p) {
        // 自动选择最优策略
        auto strategy = strategySelector_->selectOptimalStrategy(n, p);
        setStrategy(strategy);
        
        // 输出策略信息
        std::cout << "已选择策略: ";
        switch (strategy.parallelStrategy) {
            case ParallelStrategy::OPENMP_SIMD:
                std::cout << "OpenMP + SIMD";
                break;
            case ParallelStrategy::MPI_OPENMP_SIMD:
                std::cout << "MPI + OpenMP + SIMD";
                break;
            default:
                std::cout << "混合优化";
                break;
        }
        std::cout << std::endl;
        
        // 计时执行
        auto startTime = IOInterface::getCurrentTime();
        polynomialMultiply(a, b, ab, n, p);
        auto endTime = IOInterface::getCurrentTime();
        
        return IOInterface::getElapsedTime(startTime, endTime);
    }
    
    /**
     * @brief 获取当前策略
     */
    const OptimizationStrategy& getCurrentStrategy() const {
        return currentStrategy_;
    }

    /**
     * @brief 获取性能指标
     */
    std::unordered_map<std::string, double> getPerformanceMetrics() const {
        std::lock_guard<std::mutex> lock(perfMutex_);
        return performanceMetrics_;
    }

    /**
     * @brief 自适应性能调优
     */
    void adaptivePerformanceTuning(int problemSize, int modulus) {
        (void)problemSize; // 避免未使用参数警告
        (void)modulus;
        std::lock_guard<std::mutex> lock(perfMutex_);

        double totalCalls = performanceMetrics_["total_ntt_calls"];
        if (totalCalls < 5) return; // 需要足够的样本数据

        double avgTime = performanceMetrics_["total_execution_time"] / totalCalls;

        // 如果性能下降，尝试调整策略
        if (avgTime > performanceMetrics_["baseline_time"] * 1.2) {
            // 性能下降，尝试更保守的策略
            if (currentStrategy_.numThreads > 1) {
                currentStrategy_.numThreads = std::max(1, currentStrategy_.numThreads / 2);
            }
            if (currentStrategy_.blockSize > 64) {
                currentStrategy_.blockSize /= 2;
            }
        } else if (avgTime < performanceMetrics_["baseline_time"] * 0.8) {
            // 性能提升，尝试更激进的策略
            const auto& cpuInfo = hardwareDetector_->getCPUInfo();
            if (currentStrategy_.numThreads < cpuInfo.logicalCores) {
                currentStrategy_.numThreads = std::min(cpuInfo.logicalCores, currentStrategy_.numThreads * 2);
            }
            if (currentStrategy_.blockSize < 2048) {
                currentStrategy_.blockSize *= 2;
            }
        }
    }

    /**
     * @brief 打印性能报告
     */
    void printPerformanceReport() const {
        std::lock_guard<std::mutex> lock(perfMutex_);

        std::cout << "\n======== 性能报告 ========" << std::endl;
        std::cout << "总NTT调用次数: " << static_cast<int>(performanceMetrics_.at("total_ntt_calls")) << std::endl;

        if (performanceMetrics_.at("total_ntt_calls") > 0) {
            double avgTime = performanceMetrics_.at("total_execution_time") / performanceMetrics_.at("total_ntt_calls");
            std::cout << "平均执行时间: " << std::fixed << std::setprecision(2) << avgTime << " 微秒" << std::endl;

            if (performanceMetrics_.count("baseline_time") > 0) {
                double speedup = performanceMetrics_.at("baseline_time") / avgTime;
                std::cout << "相对加速比: " << std::fixed << std::setprecision(2) << speedup << "x" << std::endl;
            }
        }

        std::cout << "当前策略配置:" << std::endl;
        std::cout << "  线程数: " << currentStrategy_.numThreads << std::endl;
        std::cout << "  块大小: " << currentStrategy_.blockSize << std::endl;
        std::cout << "  向量宽度: " << currentStrategy_.vectorWidth << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    /**
     * @brief 重置性能统计
     */
    void resetPerformanceStats() {
        std::lock_guard<std::mutex> lock(perfMutex_);
        performanceMetrics_.clear();
        performanceMetrics_["total_ntt_calls"] = 0.0;
        performanceMetrics_["total_execution_time"] = 0.0;
        performanceMetrics_["average_speedup"] = 1.0;
    }
};

} // namespace HybridNTT

#endif // HYBRID_NTT_ENGINE_HPP 