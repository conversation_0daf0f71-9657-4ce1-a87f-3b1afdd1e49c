#include <cuda_runtime.h>
#include <iostream>
#include <vector>
#include <cstring>
#include <algorithm>
using namespace std;

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

typedef long long ll;

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CPU版本的calc_powg
void calc_powg(int w[], int G, int P, int gen) {
    w[0] = 1; ll f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (ll)f * w[x - (1<<t)] % P;
    }
}

// CPU版本的DIF
void DIF_cpu(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = lim; len > 1; len >>= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len/2; ++i) {
                g = f[i];
                h = (ll)f[i + len/2] * w[t] % P;
                f[i] = (g + h) % P;
                f[i + len/2] = (P + g - h) % P;
            }
        }
    }
}

// CPU版本的DIT
void DIT_cpu(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = 2; len <= lim; len <<= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len/2; ++i) {
                g = f[i];
                h = f[i + len/2];
                f[i] = (g + h) % P;
                f[i + len/2] = (P + g - h) * w[t] % P;
            }
        }
    }
    const ll invl = qpow(lim, P-2, P);
    for (int i = 0; i < lim; ++i)
        f[i] = invl * f[i] % P;
    reverse(f + 1, f + lim);
}

// CUDA Kernel: DIF NTT蝶形运算
__global__ void dif_kernel(int *data, int len, const int *twiddles, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    int g = data[base + local_id];
    int h = data[base + local_id + half_len];

    // DIF: 对下半部分先乘旋转因子，然后加减
    long long w = twiddles[block_id];
    h = (1LL * h * w) % p;

    data[base + local_id] = (g + h) % p;
    data[base + local_id + half_len] = (g - h + p) % p;
}

// CUDA Kernel: DIT NTT蝶形运算
__global__ void dit_kernel(int *data, int len, const int *twiddles, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    int g = data[base + local_id];
    int h = data[base + local_id + half_len];

    // DIT: 先加减，对差值乘旋转因子
    data[base + local_id] = (g + h) % p;
    long long w = twiddles[block_id];
    data[base + local_id + half_len] = (1LL * (g - h + p) * w) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 数组反转（除了第一个元素）
__global__ void reverse_kernel(int *data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int total_pairs = (n - 1) / 2;  // 除了第一个元素，剩下n-1个元素，需要交换的对数

    if(idx < total_pairs) {
        int left = idx + 1;  // 从第二个元素开始
        int right = n - 1 - idx;  // 对应的右边元素

        int temp = data[left];
        data[left] = data[right];
        data[right] = temp;
    }
}

// CUDA版本的DIF
void DIF_cuda(int *h_data, int n, int p) {
    int *d_data, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));

    // 使用CPU版本的calc_powg预计算旋转因子
    vector<int> twiddles(n);
    calc_powg(twiddles.data(), n, p, 3);

    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    for(int len = n; len > 1; len >>= 1) {
        int half_len = len >> 1;
        int num_blocks = n / len;
        int total_butterflies = num_blocks * half_len;

        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;

        dif_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_twiddles));
}

// CUDA版本的DIT
void DIT_cuda(int *h_data, int n, int p) {
    int *d_data, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));

    // 使用CPU版本的calc_powg预计算旋转因子
    vector<int> twiddles(n);
    calc_powg(twiddles.data(), n, p, 3);

    // 对旋转因子取逆
    for(int& w : twiddles) {
        w = qpow(w, p-2, p);
    }

    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    for(int len = 2; len <= n; len <<= 1) {
        int half_len = len >> 1;
        int num_blocks = n / len;
        int total_butterflies = num_blocks * half_len;

        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;

        dit_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    // 缩放
    int inv_n = qpow(n, p-2, p);
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
    CHECK_CUDA(cudaDeviceSynchronize());

    // 反转数组（除了第一个元素）
    reverse_kernel<<<blocks, threads>>>(d_data, n);
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_twiddles));
}

int main() {
    const int n = 8;
    const int p = 7340033;

    // 测试完整的DIF+DIT流程
    int data_cpu[n] = {1, 2, 3, 4, 5, 6, 7, 8};
    int data_gpu[n] = {1, 2, 3, 4, 5, 6, 7, 8};
    int original[n] = {1, 2, 3, 4, 5, 6, 7, 8};

    // 预计算旋转因子
    vector<int> w(n);
    calc_powg(w.data(), n, p, 3);

    cout << "原始数据: ";
    for(int i = 0; i < n; i++) {
        cout << original[i] << " ";
    }
    cout << endl;

    // CPU版本: DIF + DIT
    DIF_cpu(data_cpu, 3, p, w.data());  // log2(8) = 3
    cout << "CPU DIF结果: ";
    for(int i = 0; i < n; i++) {
        cout << data_cpu[i] << " ";
    }
    cout << endl;

    DIT_cpu(data_cpu, 3, p, w.data());

    cout << "CPU DIF+DIT结果: ";
    for(int i = 0; i < n; i++) {
        cout << data_cpu[i] << " ";
    }
    cout << endl;

    // GPU版本: DIF + DIT
    DIF_cuda(data_gpu, n, p);
    cout << "GPU DIF结果: ";
    for(int i = 0; i < n; i++) {
        cout << data_gpu[i] << " ";
    }
    cout << endl;

    DIT_cuda(data_gpu, n, p);

    cout << "GPU DIF+DIT结果: ";
    for(int i = 0; i < n; i++) {
        cout << data_gpu[i] << " ";
    }
    cout << endl;

    // 比较结果
    bool same = true;
    for(int i = 0; i < n; i++) {
        if(data_cpu[i] != data_gpu[i]) {
            same = false;
            break;
        }
    }

    cout << (same ? "结果一致" : "结果不一致") << endl;

    // 检查是否恢复到原始数据
    bool restored = true;
    for(int i = 0; i < n; i++) {
        if(data_cpu[i] != original[i]) {
            restored = false;
            break;
        }
    }

    cout << (restored ? "成功恢复原始数据" : "未能恢复原始数据") << endl;

    return 0;
}
