#include <iostream>
#include <vector>
using namespace std;

typedef long long ll;

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

void calc_powg(int w[], int G, int P, int gen) {
    w[0] = 1; ll f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (ll)f * w[x - (1<<t)] % P;
    }
}

int main() {
    const int n = 8;
    const int p = 7340033;
    
    vector<int> w(n);
    calc_powg(w.data(), n, p, 3);
    
    cout << "旋转因子数组: ";
    for(int i = 0; i < n; i++) {
        cout << "w[" << i << "]=" << w[i] << " ";
    }
    cout << endl;
    
    // 模拟DIT的旋转因子使用
    cout << "\nDIT旋转因子使用模式:" << endl;
    
    // len = 2
    cout << "len=2: ";
    for (int st = 0, t = 0; st < n; st += 2, ++t) {
        cout << "块" << t << "(st=" << st << ")使用w[" << t << "]=" << w[t] << " ";
    }
    cout << endl;
    
    // len = 4  
    cout << "len=4: ";
    for (int st = 0, t = 0; st < n; st += 4, ++t) {
        cout << "块" << t << "(st=" << st << ")使用w[" << t << "]=" << w[t] << " ";
    }
    cout << endl;
    
    // len = 8
    cout << "len=8: ";
    for (int st = 0, t = 0; st < n; st += 8, ++t) {
        cout << "块" << t << "(st=" << st << ")使用w[" << t << "]=" << w[t] << " ";
    }
    cout << endl;
    
    return 0;
}
