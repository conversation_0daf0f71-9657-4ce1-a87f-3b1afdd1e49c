/**
 * @file adaptive_load_balancer.hpp
 * @brief 自适应负载均衡器 - 动态工作分配和性能监控
 */

#pragma once

#include "intelligent_strategy_selector.hpp"
#include <vector>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <queue>
#include <chrono>
#include <functional>
#include <iostream>
#include <ostream>

namespace HybridNTT {

/**
 * @brief 工作任务结构
 */
struct WorkTask {
    int taskId;
    int startIndex;
    int endIndex;
    int problemSize;
    int modulus;
    std::vector<int>* data;
    bool inverse;
    
    // 任务优先级和预估执行时间
    int priority = 0;
    double estimatedTime = 0.0;
    
    // 任务状态
    enum class Status {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED
    } status = Status::PENDING;
    
    std::chrono::time_point<std::chrono::high_resolution_clock> startTime;
    std::chrono::time_point<std::chrono::high_resolution_clock> endTime;
};

/**
 * @brief 工作线程信息
 */
struct WorkerInfo {
    int workerId;
    std::thread thread;
    std::atomic<bool> active{false};
    std::atomic<bool> busy{false};
    std::atomic<int> tasksCompleted{0};
    std::atomic<double> totalExecutionTime{0.0};
    
    // 线程特定的硬件亲和性
    int cpuAffinity = -1;
    bool useGPU = false;
    bool useSIMD = false;
    
    // 性能统计
    double averageTaskTime = 0.0;
    double throughput = 0.0;
    
    WorkerInfo(int id) : workerId(id) {}
};

/**
 * @brief 自适应负载均衡器
 */
class AdaptiveLoadBalancer {
private:
    std::shared_ptr<IntelligentStrategySelector> strategySelector_;
    
    // 工作队列和线程管理
    std::queue<WorkTask> taskQueue_;
    std::vector<std::unique_ptr<WorkerInfo>> workers_;
    std::mutex queueMutex_;
    std::condition_variable queueCondition_;
    std::atomic<bool> shutdown_{false};
    
    // 负载均衡参数
    int numWorkers_;
    double loadBalanceThreshold_ = 0.2; // 负载不平衡阈值
    int taskStealingEnabled_ = true;
    
    // 性能监控
    std::atomic<int> totalTasksCompleted_{0};
    std::atomic<double> totalExecutionTime_{0.0};
    std::chrono::time_point<std::chrono::high_resolution_clock> startTime_;
    
    // 自适应参数
    double adaptationRate_ = 0.1;
    int performanceWindowSize_ = 100; // 性能统计窗口大小
    
public:
    explicit AdaptiveLoadBalancer(std::shared_ptr<IntelligentStrategySelector> selector, int numWorkers = 0)
        : strategySelector_(selector) {
        
        if (numWorkers <= 0) {
            numWorkers_ = std::thread::hardware_concurrency();
        } else {
            numWorkers_ = numWorkers;
        }
        
        startTime_ = std::chrono::high_resolution_clock::now();
        initializeWorkers();
    }
    
    ~AdaptiveLoadBalancer() {
        shutdown();
    }
    
    /**
     * @brief 提交NTT计算任务
     */
    void submitNTTTask(std::vector<int>& data, int modulus, bool inverse, int priority = 0) {
        int problemSize = data.size();
        
        // 根据问题规模和硬件能力决定任务分割策略
        auto tasks = createSubTasks(data, modulus, inverse, priority);
        
        {
            std::lock_guard<std::mutex> lock(queueMutex_);
            for (auto& task : tasks) {
                taskQueue_.push(task);
            }
        }
        
        queueCondition_.notify_all();
    }
    
    /**
     * @brief 等待所有任务完成
     */
    void waitForCompletion() {
        std::unique_lock<std::mutex> lock(queueMutex_);
        queueCondition_.wait(lock, [this] {
            return taskQueue_.empty() && allWorkersIdle();
        });
    }
    
    /**
     * @brief 获取负载均衡统计信息
     */
    void printLoadBalanceStatistics() {
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto totalTime = std::chrono::duration<double>(currentTime - startTime_).count();
        
        std::cout << "\n=== 自适应负载均衡统计 ===" << std::endl;
        std::cout << "总运行时间: " << totalTime << " 秒" << std::endl;
        std::cout << "总完成任务数: " << totalTasksCompleted_.load() << std::endl;
        std::cout << "平均任务吞吐量: " << totalTasksCompleted_.load() / totalTime << " 任务/秒" << std::endl;
        std::cout << "工作线程数: " << numWorkers_ << std::endl;
        
        std::cout << "\n工作线程详细统计:" << std::endl;
        for (const auto& worker : workers_) {
            if (worker->tasksCompleted.load() > 0) {
                double avgTime = worker->totalExecutionTime.load() / worker->tasksCompleted.load();
                std::cout << "线程 " << worker->workerId 
                          << " - 完成任务: " << worker->tasksCompleted.load()
                          << " - 平均时间: " << avgTime << " 秒"
                          << " - 吞吐量: " << worker->throughput << " MB/s"
                          << " - CPU亲和性: " << worker->cpuAffinity
                          << " - GPU: " << (worker->useGPU ? "是" : "否")
                          << " - SIMD: " << (worker->useSIMD ? "是" : "否") << std::endl;
            }
        }
        
        // 负载均衡效率分析
        analyzeLoadBalanceEfficiency();
    }
    
    /**
     * @brief 动态调整负载均衡参数
     */
    void adaptLoadBalanceParameters() {
        // 分析工作线程性能差异
        std::vector<double> workerThroughputs;
        for (const auto& worker : workers_) {
            if (worker->tasksCompleted.load() > 0) {
                workerThroughputs.push_back(worker->throughput);
            }
        }
        
        if (workerThroughputs.size() < 2) return;
        
        // 计算性能方差
        double avgThroughput = std::accumulate(workerThroughputs.begin(), workerThroughputs.end(), 0.0) / workerThroughputs.size();
        double variance = 0.0;
        for (double throughput : workerThroughputs) {
            variance += (throughput - avgThroughput) * (throughput - avgThroughput);
        }
        variance /= workerThroughputs.size();
        double stdDev = std::sqrt(variance);
        
        // 根据性能差异调整负载均衡策略
        double coefficientOfVariation = stdDev / avgThroughput;
        if (coefficientOfVariation > loadBalanceThreshold_) {
            // 性能差异较大，启用更积极的任务窃取
            taskStealingEnabled_ = true;
            loadBalanceThreshold_ = std::max(0.1, loadBalanceThreshold_ - adaptationRate_);
        } else {
            // 性能相对均衡，减少任务窃取开销
            loadBalanceThreshold_ = std::min(0.5, loadBalanceThreshold_ + adaptationRate_);
        }
        
        std::cout << "负载均衡参数自适应调整:" << std::endl;
        std::cout << "性能变异系数: " << coefficientOfVariation << std::endl;
        std::cout << "负载均衡阈值: " << loadBalanceThreshold_ << std::endl;
        std::cout << "任务窃取: " << (taskStealingEnabled_ ? "启用" : "禁用") << std::endl;
    }

private:
    /**
     * @brief 初始化工作线程
     */
    void initializeWorkers() {
        workers_.reserve(numWorkers_);
        
        for (int i = 0; i < numWorkers_; ++i) {
            auto worker = std::make_unique<WorkerInfo>(i);
            
            // 设置硬件亲和性和特性
            configureWorkerHardware(*worker);
            
            // 启动工作线程
            worker->thread = std::thread(&AdaptiveLoadBalancer::workerLoop, this, worker.get());
            worker->active = true;
            
            workers_.push_back(std::move(worker));
        }
    }
    
    /**
     * @brief 配置工作线程硬件特性
     */
    void configureWorkerHardware(WorkerInfo& worker) {
        // CPU亲和性设置
        worker.cpuAffinity = worker.workerId % std::thread::hardware_concurrency();
        
        // GPU使用策略：部分线程使用GPU
        if (strategySelector_) {
            const auto& gpuInfo = strategySelector_->hardwareDetector_->getGPUInfo();
            if (gpuInfo.available && worker.workerId < gpuInfo.device_count) {
                worker.useGPU = true;
            }
        }
        
        // SIMD使用策略：大部分线程启用SIMD
        if (strategySelector_) {
            const auto& cpuInfo = strategySelector_->hardwareDetector_->getCPUInfo();
            if (cpuInfo.avx2_support || cpuInfo.neon_support || cpuInfo.sse_support) {
                worker.useSIMD = true;
            }
        }
    }
    
    /**
     * @brief 工作线程主循环
     */
    void workerLoop(WorkerInfo* worker);
    
    /**
     * @brief 创建子任务
     */
    std::vector<WorkTask> createSubTasks(std::vector<int>& data, int modulus, bool inverse, int priority);
    
    /**
     * @brief 检查所有工作线程是否空闲
     */
    bool allWorkersIdle() const {
        for (const auto& worker : workers_) {
            if (worker->busy.load()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * @brief 分析负载均衡效率
     */
    void analyzeLoadBalanceEfficiency();
    
    /**
     * @brief 关闭负载均衡器
     */
    void shutdown() {
        shutdown_ = true;
        queueCondition_.notify_all();
        
        for (auto& worker : workers_) {
            if (worker->thread.joinable()) {
                worker->thread.join();
            }
        }
    }
};

} // namespace HybridNTT
