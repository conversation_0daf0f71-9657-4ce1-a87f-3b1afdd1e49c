/**
 * @file complete_parallel_strategies.cpp
 * @brief 完整的并行策略实现
 * 
 * 基于现有项目中的所有并行实现，提供完整的并行策略支持
 */

#include "complete_ntt_engine.hpp"
#include <thread>
#include <future>
#include <queue>
#include <condition_variable>

#ifdef USE_MPI
#include <mpi.h>
#endif

#ifdef USE_CUDA
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

// ============ OpenMP任务并行实现 ============
void CompleteNTTEngine::openmp_task_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        // 使用OpenMP任务并行处理每个块
        #pragma omp parallel
        {
            #pragma omp single
            {
                for (int i = 0; i < n; i += len) {
                    #pragma omp task firstprivate(i, len, half, wn, modulus)
                    {
                        uint32_t w = 1;
                        for (int j = 0; j < half; j++) {
                            int u = data[i + j];
                            int v = (1LL * data[i + j + half] * w) % modulus;
                            data[i + j] = (u + v) % modulus;
                            data[i + j + half] = (u - v + modulus) % modulus;
                            w = (1LL * w * wn) % modulus;
                        }
                    }
                }
                #pragma omp taskwait
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        #pragma omp parallel for
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ pthread多线程实现 ============
struct ThreadData {
    std::vector<int>* data;
    int start;
    int len;
    int half;
    uint32_t wn;
    int modulus;
};

void* butterfly_worker(void* arg) {
    ThreadData* td = static_cast<ThreadData*>(arg);
    uint32_t w = 1;
    
    for (int j = 0; j < td->half; j++) {
        int u = (*td->data)[td->start + j];
        int v = (1LL * (*td->data)[td->start + j + td->half] * w) % td->modulus;
        (*td->data)[td->start + j] = (u + v) % td->modulus;
        (*td->data)[td->start + j + td->half] = (u - v + td->modulus) % td->modulus;
        w = (1LL * w * td->wn) % td->modulus;
    }
    
    return nullptr;
}

void CompleteNTTEngine::pthread_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    int num_threads = std::thread::hardware_concurrency();
    
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        std::vector<pthread_t> threads;
        std::vector<ThreadData> thread_data;
        
        int blocks_per_thread = std::max(1, (n / len) / num_threads);
        
        for (int t = 0; t < num_threads && t * blocks_per_thread * len < n; t++) {
            int start = t * blocks_per_thread * len;
            int end = std::min(start + blocks_per_thread * len, n);
            
            for (int i = start; i < end; i += len) {
                ThreadData td = {&data, i, len, half, wn, modulus};
                thread_data.push_back(td);
                
                pthread_t thread;
                pthread_create(&thread, nullptr, butterfly_worker, &thread_data.back());
                threads.push_back(thread);
            }
        }
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            pthread_join(thread, nullptr);
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ MPI分布式并行实现 ============
void CompleteNTTEngine::mpi_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_MPI
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    int n = data.size();

    // 初始化Barrett约简参数
    initBarrettParams(modulus);

    // 位反转（仅在rank 0执行）
    if (rank == 0) {
        bitReversePermutation(data);
    }

    // 广播位反转后的数据
    MPI_Bcast(data.data(), n, MPI_INT, 0, MPI_COMM_WORLD);

    // NTT主循环 - 分布式并行处理
    for (int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        // 计算每个进程负责的块数
        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / size;
        int remainder_blocks = total_blocks % size;

        int my_blocks = blocks_per_proc + (rank < remainder_blocks ? 1 : 0);
        int start_block = rank * blocks_per_proc + std::min(rank, remainder_blocks);

        // 每个进程处理分配给它的块
        for (int b = 0; b < my_blocks; ++b) {
            int block_start = (start_block + b) * len;
            uint32_t w = 1;

            for (int j = 0; j < m; ++j) {
                if (block_start + j + m < n) {
                    // 使用Barrett约简优化模运算
                    uint32_t u = data[block_start + j];
                    uint32_t v = barrett_params_.mul(data[block_start + j + m], w);

                    data[block_start + j] = barrett_params_.add(u, v);
                    data[block_start + j + m] = barrett_params_.sub(u, v);

                    w = barrett_params_.mul(w, wn);
                }
            }
        }

        // 准备通信数据
        std::vector<int> recvcounts(size), displs(size);
        for (int r = 0; r < size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + std::min(r, remainder_blocks)) * len;
        }

        // 收集本进程处理的数据
        std::vector<int> temp_data(my_blocks * len);
        if (my_blocks > 0) {
            std::memcpy(temp_data.data(), data.data() + start_block * len,
                       my_blocks * len * sizeof(int));
        }

        // 使用Allgatherv同步所有进程的结果
        MPI_Allgatherv(my_blocks > 0 ? temp_data.data() : MPI_IN_PLACE,
                       my_blocks * len, MPI_INT,
                       data.data(), recvcounts.data(), displs.data(), MPI_INT,
                       MPI_COMM_WORLD);
    }

    // 逆变换处理
    if (inverse) {
        if (rank == 0) {
            uint32_t inv_n = modularInverse(n, modulus);
            for (int& val : data) {
                val = barrett_params_.mul(val, inv_n);
            }
        }
        MPI_Bcast(data.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
    }

#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

// ============ Radix-4 DIT实现 ============
void CompleteNTTEngine::radix4_dit_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 4; len <= n; len <<= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = (1LL * data[i + j + quarter] * w1) % modulus;
                int c = (1LL * data[i + j + 2*quarter] * w2) % modulus;
                int d = (1LL * data[i + j + 3*quarter] * w3) % modulus;
                
                // Radix-4蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (t2 + t4) % modulus;
                data[i + j + 2*quarter] = (t1 - t3 + modulus) % modulus;
                data[i + j + 3*quarter] = (t2 - t4 + modulus) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    // 处理剩余的Radix-2层
    for (int len = 2; len <= n && (len & (len-1)) == 0; len <<= 1) {
        if (len >= 4 && (len & 3) == 0) continue; // 已经被Radix-4处理
        
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ Radix-4 DIF实现 ============
void CompleteNTTEngine::radix4_dif_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // Radix-4 DIF蝶形运算
    for (int len = n; len >= 4; len >>= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = data[i + j + quarter];
                int c = data[i + j + 2*quarter];
                int d = data[i + j + 3*quarter];
                
                // Radix-4 DIF蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (1LL * (t2 + t4) * w1) % modulus;
                data[i + j + 2*quarter] = (1LL * (t1 - t3 + modulus) * w2) % modulus;
                data[i + j + 3*quarter] = (1LL * (t2 - t4 + modulus) * w3) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    bitReversePermutation(data);
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ CUDA GPU并行实现 ============
void CompleteNTTEngine::cuda_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_CUDA
    // TODO: 实现CUDA GPU并行
    // 目前回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

} // namespace HybridNTT
