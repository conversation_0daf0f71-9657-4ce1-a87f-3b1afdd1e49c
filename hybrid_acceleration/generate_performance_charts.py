#!/usr/bin/env python3
"""
生成精美的性能分析图表
包括性能对比、加速比分析、吞吐量对比等多维度可视化
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体和图表样式
plt.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
try:
    plt.style.use('seaborn-v0_8-darkgrid')
except:
    plt.style.use('seaborn-darkgrid')
sns.set_palette("husl")

def create_sample_data():
    """创建示例性能数据"""
    sizes = [256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536]
    strategies = ['Serial', 'OpenMP', 'SIMD AVX', 'SIMD NEON', 'pthread', 'Hybrid']
    
    data = []
    for size in sizes:
        # 模拟性能数据（基于实际测试结果的趋势）
        base_time = size * 0.1  # 基础时间
        
        for strategy in strategies:
            if strategy == 'Serial':
                time = base_time
                speedup = 1.0
            elif strategy == 'OpenMP':
                speedup = min(4.0, size / 1024 + 1.0)
                time = base_time / speedup
            elif strategy == 'SIMD AVX':
                speedup = min(2.5, size / 2048 + 1.2)
                time = base_time / speedup
            elif strategy == 'SIMD NEON':
                speedup = min(2.0, size / 2048 + 1.1)
                time = base_time / speedup
            elif strategy == 'pthread':
                speedup = min(3.5, size / 1024 + 0.8)
                time = base_time / speedup
            elif strategy == 'Hybrid':
                speedup = min(6.0, size / 512 + 1.5)
                time = base_time / speedup
            
            throughput = (size * 8) / (time / 1000) / (1024 * 1024)  # MB/s
            
            data.append({
                'Size': size,
                'Strategy': strategy,
                'Time_us': time,
                'Speedup': speedup,
                'Throughput_MBps': throughput
            })
    
    return pd.DataFrame(data)

def plot_performance_comparison(df):
    """绘制性能对比图"""
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 执行时间对比
    for strategy in df['Strategy'].unique():
        strategy_data = df[df['Strategy'] == strategy]
        ax1.plot(strategy_data['Size'], strategy_data['Time_us'], 
                marker='o', linewidth=2.5, markersize=6, label=strategy)
    
    ax1.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Execution Time (μs)', fontsize=12, fontweight='bold')
    ax1.set_title('NTT Performance Comparison\nExecution Time vs Problem Size', 
                  fontsize=14, fontweight='bold')
    ax1.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    ax1.set_xscale('log')
    
    # 2. 吞吐量对比
    for strategy in df['Strategy'].unique():
        strategy_data = df[df['Strategy'] == strategy]
        ax2.plot(strategy_data['Size'], strategy_data['Throughput_MBps'], 
                marker='s', linewidth=2.5, markersize=6, label=strategy)
    
    ax2.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Throughput (MB/s)', fontsize=12, fontweight='bold')
    ax2.set_title('NTT Throughput Comparison\nThroughput vs Problem Size', 
                  fontsize=14, fontweight='bold')
    ax2.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')
    
    # 3. 加速比对比
    serial_data = df[df['Strategy'] == 'Serial'].set_index('Size')['Time_us']
    for strategy in df['Strategy'].unique():
        if strategy == 'Serial':
            continue
        strategy_data = df[df['Strategy'] == strategy].set_index('Size')['Time_us']
        speedup = serial_data / strategy_data
        ax3.plot(speedup.index, speedup.values, 
                marker='^', linewidth=2.5, markersize=6, label=f'{strategy}')
    
    ax3.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Speedup vs Serial', fontsize=12, fontweight='bold')
    ax3.set_title('Speedup Analysis\nParallel Efficiency vs Problem Size', 
                  fontsize=14, fontweight='bold')
    ax3.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=1, color='red', linestyle='--', alpha=0.7, linewidth=2)
    ax3.set_xscale('log')
    
    # 4. 效率热力图
    pivot_df = df.pivot(index='Size', columns='Strategy', values='Speedup')
    im = ax4.imshow(pivot_df.values, cmap='RdYlGn', aspect='auto', interpolation='nearest')
    ax4.set_xticks(range(len(pivot_df.columns)))
    ax4.set_xticklabels(pivot_df.columns, rotation=45, ha='right')
    ax4.set_yticks(range(len(pivot_df.index)))
    ax4.set_yticklabels(pivot_df.index)
    ax4.set_title('Speedup Heatmap\nStrategy Efficiency Matrix', 
                  fontsize=14, fontweight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax4, shrink=0.8)
    cbar.set_label('Speedup Factor', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('charts/comprehensive_performance_analysis.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

def plot_scalability_analysis(df):
    """绘制可扩展性分析图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 1. 强扩展性分析
    large_sizes = df[df['Size'] >= 4096]
    strategies_to_plot = ['OpenMP', 'SIMD AVX', 'pthread', 'Hybrid']
    
    x_pos = np.arange(len(strategies_to_plot))
    colors = plt.cm.Set3(np.linspace(0, 1, len(large_sizes['Size'].unique())))
    
    width = 0.15
    for i, size in enumerate(sorted(large_sizes['Size'].unique())):
        size_data = large_sizes[large_sizes['Size'] == size]
        speedups = []
        for strategy in strategies_to_plot:
            strategy_speedup = size_data[size_data['Strategy'] == strategy]['Speedup'].iloc[0]
            speedups.append(strategy_speedup)
        
        ax1.bar(x_pos + i * width, speedups, width, 
               label=f'n={size}', color=colors[i], alpha=0.8)
    
    ax1.set_xlabel('Parallel Strategy', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Speedup Factor', fontsize=12, fontweight='bold')
    ax1.set_title('Strong Scalability Analysis\nSpeedup by Strategy and Problem Size', 
                  fontsize=14, fontweight='bold')
    ax1.set_xticks(x_pos + width * 1.5)
    ax1.set_xticklabels(strategies_to_plot)
    ax1.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. 效率趋势分析
    for strategy in ['OpenMP', 'SIMD AVX', 'pthread', 'Hybrid']:
        strategy_data = df[df['Strategy'] == strategy]
        efficiency = strategy_data['Speedup'] / 4  # 假设4核心
        ax2.plot(strategy_data['Size'], efficiency * 100, 
                marker='o', linewidth=2.5, markersize=6, label=strategy)
    
    ax2.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Parallel Efficiency (%)', fontsize=12, fontweight='bold')
    ax2.set_title('Parallel Efficiency Trends\nEfficiency vs Problem Size', 
                  fontsize=14, fontweight='bold')
    ax2.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log')
    ax2.axhline(y=100, color='red', linestyle='--', alpha=0.7, linewidth=2, label='Ideal')
    
    plt.tight_layout()
    plt.savefig('charts/scalability_analysis.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

def plot_algorithm_comparison(df):
    """绘制算法对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. SIMD技术对比
    simd_strategies = ['Serial', 'SIMD AVX', 'SIMD NEON']
    simd_data = df[df['Strategy'].isin(simd_strategies)]
    
    for strategy in simd_strategies:
        strategy_data = simd_data[simd_data['Strategy'] == strategy]
        ax1.plot(strategy_data['Size'], strategy_data['Throughput_MBps'], 
                marker='o', linewidth=2.5, markersize=6, label=strategy)
    
    ax1.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Throughput (MB/s)', fontsize=12, fontweight='bold')
    ax1.set_title('SIMD Technology Comparison\nVector Processing Performance', 
                  fontsize=14, fontweight='bold')
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')
    
    # 2. 并行策略对比
    parallel_strategies = ['Serial', 'OpenMP', 'pthread', 'Hybrid']
    parallel_data = df[df['Strategy'].isin(parallel_strategies)]
    
    sizes_to_plot = [1024, 4096, 16384, 65536]
    x_pos = np.arange(len(parallel_strategies))
    width = 0.2
    
    for i, size in enumerate(sizes_to_plot):
        size_data = parallel_data[parallel_data['Size'] == size]
        throughputs = []
        for strategy in parallel_strategies:
            throughput = size_data[size_data['Strategy'] == strategy]['Throughput_MBps'].iloc[0]
            throughputs.append(throughput)
        
        ax2.bar(x_pos + i * width, throughputs, width, 
               label=f'n={size}', alpha=0.8)
    
    ax2.set_xlabel('Parallel Strategy', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Throughput (MB/s)', fontsize=12, fontweight='bold')
    ax2.set_title('Parallel Strategy Comparison\nThroughput by Strategy', 
                  fontsize=14, fontweight='bold')
    ax2.set_xticks(x_pos + width * 1.5)
    ax2.set_xticklabels(parallel_strategies)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 性能提升百分比
    serial_throughput = df[df['Strategy'] == 'Serial'].set_index('Size')['Throughput_MBps']
    improvement_data = []
    
    for strategy in ['OpenMP', 'SIMD AVX', 'pthread', 'Hybrid']:
        strategy_throughput = df[df['Strategy'] == strategy].set_index('Size')['Throughput_MBps']
        improvement = ((strategy_throughput - serial_throughput) / serial_throughput * 100)
        
        ax3.plot(improvement.index, improvement.values, 
                marker='s', linewidth=2.5, markersize=6, label=strategy)
    
    ax3.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Performance Improvement (%)', fontsize=12, fontweight='bold')
    ax3.set_title('Performance Improvement Analysis\nRelative to Serial Implementation', 
                  fontsize=14, fontweight='bold')
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    ax3.set_xscale('log')
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.7, linewidth=2)
    
    # 4. 综合性能雷达图
    categories = ['Small\n(≤1K)', 'Medium\n(1K-8K)', 'Large\n(8K-32K)', 'XLarge\n(≥32K)']
    strategies_radar = ['OpenMP', 'SIMD AVX', 'pthread', 'Hybrid']
    
    # 计算各策略在不同规模下的平均性能
    performance_matrix = []
    for strategy in strategies_radar:
        strategy_data = df[df['Strategy'] == strategy]
        small = strategy_data[strategy_data['Size'] <= 1024]['Speedup'].mean()
        medium = strategy_data[(strategy_data['Size'] > 1024) & (strategy_data['Size'] <= 8192)]['Speedup'].mean()
        large = strategy_data[(strategy_data['Size'] > 8192) & (strategy_data['Size'] <= 32768)]['Speedup'].mean()
        xlarge = strategy_data[strategy_data['Size'] > 32768]['Speedup'].mean()
        performance_matrix.append([small, medium, large, xlarge])
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    ax4 = plt.subplot(2, 2, 4, projection='polar')
    
    for i, strategy in enumerate(strategies_radar):
        values = performance_matrix[i] + performance_matrix[i][:1]  # 闭合图形
        ax4.plot(angles, values, 'o-', linewidth=2, label=strategy)
        ax4.fill(angles, values, alpha=0.25)
    
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(categories)
    ax4.set_title('Performance Radar Chart\nStrategy Comparison by Problem Scale', 
                  fontsize=14, fontweight='bold', pad=20)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig('charts/algorithm_comparison.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

def main():
    """主函数"""
    print("Generating comprehensive performance charts...")
    
    # 创建输出目录
    import os
    os.makedirs('charts', exist_ok=True)
    
    # 创建或读取数据
    try:
        df = pd.read_csv('results/performance_results.csv')
        print("Using actual performance data from results/performance_results.csv")
    except FileNotFoundError:
        print("Performance results not found. Using sample data for demonstration.")
        df = create_sample_data()
    
    # 生成各种图表
    print("1. Generating comprehensive performance analysis...")
    plot_performance_comparison(df)
    
    print("2. Generating scalability analysis...")
    plot_scalability_analysis(df)
    
    print("3. Generating algorithm comparison...")
    plot_algorithm_comparison(df)
    
    print("\nAll charts generated successfully!")
    print("Charts saved in charts/ directory:")
    print("- comprehensive_performance_analysis.png")
    print("- scalability_analysis.png") 
    print("- algorithm_comparison.png")

if __name__ == "__main__":
    main()
