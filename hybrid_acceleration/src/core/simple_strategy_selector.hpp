/**
 * @file simple_strategy_selector.hpp
 * @brief 简化的策略选择器实现
 */

#pragma once

#include "simple_hardware_detector.hpp"
#include <memory>

namespace HybridNTT {

/**
 * @brief 优化策略结构
 */
struct OptimizationStrategy {
    enum class ParallelStrategy {
        SERIAL,
        OPENMP_SIMD,
        MPI_OPENMP_SIMD,
        HYBRID_ALL
    } parallelStrategy;
    
    enum class NTTAlgorithm {
        RADIX_2_DIT,
        RADIX_2_DIF,
        RADIX_4_DIT,
        RADIX_4_DIF,
        ADAPTIVE_RADIX,
        MIXED_RADIX
    } nttAlgorithm;
    
    int numThreads = 1;
    int vectorWidth = 1;
    bool useBarrettReduction = false;
    bool useMontgomeryReduction = false;
};

/**
 * @brief 简化的策略选择器
 */
class StrategySelector {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    
public:
    explicit StrategySelector(std::shared_ptr<HardwareDetector> detector)
        : hardwareDetector_(detector) {}
    
    /**
     * @brief 选择最优策略
     */
    OptimizationStrategy selectOptimalStrategy(int problemSize, int modulus) {
        OptimizationStrategy strategy;
        
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();
        
        // 根据问题规模选择并行策略
        if (problemSize >= 16384 && networkInfo.hasMPI) {
            strategy.parallelStrategy = OptimizationStrategy::ParallelStrategy::MPI_OPENMP_SIMD;
        } else if (problemSize >= 1024 && cpuInfo.cores > 1) {
            strategy.parallelStrategy = OptimizationStrategy::ParallelStrategy::OPENMP_SIMD;
        } else if (problemSize >= 256 && (cpuInfo.avx_support || cpuInfo.neon_support)) {
            strategy.parallelStrategy = OptimizationStrategy::ParallelStrategy::OPENMP_SIMD;
        } else {
            strategy.parallelStrategy = OptimizationStrategy::ParallelStrategy::SERIAL;
        }
        
        // 根据问题规模选择NTT算法
        if (problemSize >= 4096) {
            strategy.nttAlgorithm = OptimizationStrategy::NTTAlgorithm::RADIX_4_DIT;
        } else if (problemSize >= 1024) {
            strategy.nttAlgorithm = OptimizationStrategy::NTTAlgorithm::RADIX_2_DIT;
        } else {
            strategy.nttAlgorithm = OptimizationStrategy::NTTAlgorithm::RADIX_2_DIT;
        }
        
        // 设置线程数
        strategy.numThreads = std::min(cpuInfo.cores, problemSize / 64);
        if (strategy.numThreads < 1) strategy.numThreads = 1;
        
        // 设置向量宽度
        if (cpuInfo.avx2_support) {
            strategy.vectorWidth = 8;
        } else if (cpuInfo.avx_support) {
            strategy.vectorWidth = 8;
        } else if (cpuInfo.neon_support) {
            strategy.vectorWidth = 4;
        } else if (cpuInfo.sse_support) {
            strategy.vectorWidth = 4;
        } else {
            strategy.vectorWidth = 1;
        }
        
        // 根据模数大小选择约简算法
        if (modulus > 1000000000) {
            strategy.useBarrettReduction = true;
        } else if (modulus > 100000) {
            strategy.useMontgomeryReduction = true;
        }
        
        return strategy;
    }
    
    /**
     * @brief 获取性能评分
     */
    double getPerformanceScore(const OptimizationStrategy& strategy, int problemSize) {
        double score = 1.0;
        
        // 并行策略评分
        switch (strategy.parallelStrategy) {
            case OptimizationStrategy::ParallelStrategy::SERIAL:
                score *= 1.0;
                break;
            case OptimizationStrategy::ParallelStrategy::OPENMP_SIMD:
                score *= std::min(4.0, static_cast<double>(strategy.numThreads));
                break;
            case OptimizationStrategy::ParallelStrategy::MPI_OPENMP_SIMD:
                score *= std::min(8.0, static_cast<double>(strategy.numThreads) * 2);
                break;
            case OptimizationStrategy::ParallelStrategy::HYBRID_ALL:
                score *= std::min(16.0, static_cast<double>(strategy.numThreads) * 4);
                break;
        }
        
        // 向量化评分
        score *= std::min(2.0, static_cast<double>(strategy.vectorWidth) / 4.0 + 1.0);
        
        // 算法评分
        if (strategy.nttAlgorithm == OptimizationStrategy::NTTAlgorithm::RADIX_4_DIT ||
            strategy.nttAlgorithm == OptimizationStrategy::NTTAlgorithm::RADIX_4_DIF) {
            score *= 1.2; // Radix-4稍快
        }
        
        // 约简算法评分
        if (strategy.useBarrettReduction || strategy.useMontgomeryReduction) {
            score *= 1.1;
        }
        
        return score * problemSize / 1000.0; // 基于问题规模的基础评分
    }
    
    /**
     * @brief 获取置信度
     */
    double getConfidence(const OptimizationStrategy& strategy, int problemSize) {
        // 简化的置信度计算
        double confidence = 0.8;
        
        if (problemSize >= 1024) {
            confidence += 0.1;
        }
        
        if (strategy.parallelStrategy != OptimizationStrategy::ParallelStrategy::SERIAL) {
            confidence += 0.05;
        }
        
        return std::min(1.0, confidence);
    }
};

} // namespace HybridNTT
