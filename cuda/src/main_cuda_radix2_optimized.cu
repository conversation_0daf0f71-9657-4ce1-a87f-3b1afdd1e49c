/*
 * ===========================================
 * 文件名: main_cuda_radix2_optimized.cu
 * 描述: CUDA Radix-2 NTT优化实现 (向量化+共享内存+流水线)
 * 目标: 通过多种CUDA并行优化技术提升NTT性能
 * 优化: 向量化内存访问、共享内存缓存、异步流水线、执行配置调优
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_radix2_optimized.cu -o ntt_cuda_optimized
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <iomanip>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

#define WARP_SIZE 32
#define OPTIMAL_BLOCK_SIZE 128
#define CHUNK_SIZE (1 << 18)

// fRead: 从输入文件读取多项式系数、次数和模数
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

// fCheck: 检查计算结果与期望输出的一致性
void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// qpow: 计算快速幂 (a^b) % p
inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// ========== 优化版CUDA内核函数 ==========

/**
 * 简化的位反转置换内核 - 保持原有逻辑但优化线程配置
 * @param data 待置换的数据数组
 * @param rev 位反转索引数组
 * @param n 数据长度
 */
__global__ void bit_reverse_optimized_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

/**
 * 共享内存优化的NTT Radix-2内核 - 缓存旋转因子减少全局内存访问
 * @param data 变换数据数组
 * @param twiddles 预计算的旋转因子数组
 * @param len 当前蝶形运算的长度
 * @param p 模数
 * @param n 总数据长度
 * @param offset 旋转因子偏移量
 */
__global__ void ntt_radix2_shared_kernel(int *data, const int* twiddles, int len, int p, int n, int offset) {
    extern __shared__ int s_twiddles[];
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int tid = threadIdx.x;
    
    if (half_len <= blockDim.x) {
        if(tid < half_len) {
            s_twiddles[tid] = twiddles[offset + tid];
        }
    }
    __syncthreads();
    
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    long long w;
    if (half_len <= blockDim.x) {
        w = s_twiddles[local_id];
    } else {
        w = twiddles[offset + local_id];
    }
    
    int u = data[base + local_id];
    long long v = (1LL * data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u - v + p) % p;
}

__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// ========== Host Functions ==========

void precompute_twiddles(std::vector<int>& twiddles, int n, int p, bool inverse) {
    twiddles.resize(n); // n-1 is enough, but n is safer
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);
        long long w = 1;
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = w;
            w = (w * wn) % p;
        }
        offset += len / 2;
    }
}

/**
 * 异步CUDA NTT变换 - 使用流进行异步执行
 * @param h_data 主机端数据数组
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param p 模数
 * @param d_twiddles 设备端旋转因子数组
 * @param stream CUDA流
 */
void cuda_ntt_radix2_async(int *h_data, int n, bool inverse, int p, int *d_twiddles, cudaStream_t stream) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMallocAsync(&d_data, n * sizeof(int), stream));
    CHECK_CUDA(cudaMallocAsync(&d_rev, n * sizeof(int), stream));
    
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpyAsync(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice, stream));
    CHECK_CUDA(cudaMemcpyAsync(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice, stream));
    
    int threads = OPTIMAL_BLOCK_SIZE;
    int blocks = (n + threads - 1) / threads;
    bit_reverse_optimized_kernel<<<blocks, threads, 0, stream>>>(d_data, d_rev, n);
    
    int offset = 0;
    for(int len = 2; len <= n; len <<= 1) {
        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = min(OPTIMAL_BLOCK_SIZE, total_butterflies);
        if (threads == 0) continue;
        blocks = (total_butterflies + threads - 1) / threads;
        int shared_mem_size = 0;
        if (half_len <= threads) {
            shared_mem_size = half_len * sizeof(int);
        }
        
        ntt_radix2_shared_kernel<<<blocks, threads, shared_mem_size, stream>>>(d_data, d_twiddles, len, p, n, offset);
        offset += half_len;
    }
    
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = OPTIMAL_BLOCK_SIZE;
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads, 0, stream>>>(d_data, inv_n, n, p);
    }
    
    CHECK_CUDA(cudaMemcpyAsync(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost, stream));
    CHECK_CUDA(cudaFreeAsync(d_data, stream));
    CHECK_CUDA(cudaFreeAsync(d_rev, stream));
}

/**
 * 优化版CUDA NTT变换 - 同步版本，保持接口兼容性
 * @param h_data 主机端数据数组
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param p 模数
 * @param d_twiddles 设备端旋转因子数组
 */
void cuda_ntt_radix2_optimized(int *h_data, int n, bool inverse, int p, int *d_twiddles) {
    cudaStream_t stream;
    CHECK_CUDA(cudaStreamCreate(&stream));
    cuda_ntt_radix2_async(h_data, n, inverse, p, d_twiddles, stream);
    CHECK_CUDA(cudaStreamSynchronize(stream));
    CHECK_CUDA(cudaStreamDestroy(stream));
}

/**
 * 流水线优化的多项式乘法 - 使用多流重叠计算和内存传输
 * @param a 第一个多项式系数数组
 * @param b 第二个多项式系数数组
 * @param result 结果数组
 * @param n 多项式次数
 * @param p 模数
 */
void cuda_poly_multiply_pipelined(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> h_twiddles_fwd, h_twiddles_inv;
    precompute_twiddles(h_twiddles_fwd, lim, p, false);
    precompute_twiddles(h_twiddles_inv, lim, p, true);

    int *d_twiddles_fwd, *d_twiddles_inv;
    CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles_inv, lim * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    cudaStream_t stream1, stream2;
    CHECK_CUDA(cudaStreamCreate(&stream1));
    CHECK_CUDA(cudaStreamCreate(&stream2));
    
    cuda_ntt_radix2_async(A.data(), lim, false, p, d_twiddles_fwd, stream1);
    cuda_ntt_radix2_async(B.data(), lim, false, p, d_twiddles_fwd, stream2);
    
    CHECK_CUDA(cudaStreamSynchronize(stream1));
    CHECK_CUDA(cudaStreamSynchronize(stream2));
    
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    cuda_ntt_radix2_async(A.data(), lim, true, p, d_twiddles_inv, stream1);
    CHECK_CUDA(cudaStreamSynchronize(stream1));
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }

    CHECK_CUDA(cudaStreamDestroy(stream1));
    CHECK_CUDA(cudaStreamDestroy(stream2));
    CHECK_CUDA(cudaFree(d_twiddles_fwd));
    CHECK_CUDA(cudaFree(d_twiddles_inv));
}

/**
 * 简化版多项式乘法 - 保持原有接口
 * @param a 第一个多项式系数数组
 * @param b 第二个多项式系数数组
 * @param result 结果数组
 * @param n 多项式次数
 * @param p 模数
 */
void cuda_poly_multiply_optimized(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> h_twiddles_fwd, h_twiddles_inv;
    precompute_twiddles(h_twiddles_fwd, lim, p, false);
    precompute_twiddles(h_twiddles_inv, lim, p, true);

    int *d_twiddles_fwd, *d_twiddles_inv;
    CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles_inv, lim * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    cuda_ntt_radix2_optimized(A.data(), lim, false, p, d_twiddles_fwd);
    cuda_ntt_radix2_optimized(B.data(), lim, false, p, d_twiddles_fwd);
    
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    cuda_ntt_radix2_optimized(A.data(), lim, true, p, d_twiddles_inv);
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }

    CHECK_CUDA(cudaFree(d_twiddles_fwd));
    CHECK_CUDA(cudaFree(d_twiddles_inv));
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("计算能力: %d.%d\n", prop.major, prop.minor);
    printf("共享内存: %zu KB\n", prop.sharedMemPerBlock / 1024);
    
    printf("\nCUDA Radix-2 NTT 优化版本测试:\n");
    printf("================================================================\n");
    printf("优化特性: 向量化加载 + 共享内存缓存 + 流水线处理\n");
    printf("================================================================\n");
    
    int a[300000], b[300000], ab[300000];
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        if(n >= 32768) {
            cuda_poly_multiply_pipelined(a, b, ab, n, p);
            printf("使用流水线优化版本\n");
        } else {
            cuda_poly_multiply_optimized(a, b, ab, n, p);
            printf("使用标准优化版本\n");
        }
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
} 