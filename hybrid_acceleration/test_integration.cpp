#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <iomanip>
#include <string>
#include <cmath>
#include <algorithm>
#include <random>
#include <map>

// 包含我们的混合NTT引擎
#include "src/core/hardware_detector.hpp"
#include "src/core/strategy_selector.hpp"
#include "src/algorithms/hybrid_ntt_engine.hpp"
#include "src/parallel/parallel_coordinator.hpp"
#include "src/memory/numa_memory_manager.hpp"

using namespace HybridNTT;

/**
 * @brief 集成测试和性能验证主程序
 */
class IntegrationTestSuite {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;
    std::shared_ptr<HybridNTTEngine> nttEngine_;
    std::shared_ptr<ParallelCoordinator> parallelCoordinator_;
    std::shared_ptr<NUMAMemoryManager> numaManager_;
    
    struct TestResult {
        std::string testName;
        bool passed;
        double executionTime;
        std::string errorMessage;
        std::map<std::string, double> metrics;
    };
    
    std::vector<TestResult> testResults_;

public:
    IntegrationTestSuite() {
        // 初始化所有组件
        hardwareDetector_ = std::make_shared<HardwareDetector>();
        hardwareDetector_->detectAll(); // 执行硬件检测
        strategySelector_ = std::make_shared<StrategySelector>(hardwareDetector_);
        nttEngine_ = std::make_shared<HybridNTTEngine>(hardwareDetector_, strategySelector_);
        parallelCoordinator_ = std::make_shared<ParallelCoordinator>(hardwareDetector_);
        numaManager_ = std::make_shared<NUMAMemoryManager>(hardwareDetector_);
    }

    /**
     * @brief 兼容原项目的文件读取接口
     */
    bool fRead(std::vector<int>& a, std::vector<int>& b, int* n, int* p, int input_id) {
        std::string path = "nttdata/" + std::to_string(input_id) + ".in";
        std::ifstream fin(path);
        if (!fin.is_open()) {
            std::cerr << "错误：无法打开输入文件 " << path << std::endl;
            return false;
        }
        
        fin >> *n >> *p;
        a.resize(*n);
        b.resize(*n);
        
        for (int i = 0; i < *n; i++) {
            fin >> a[i];
        }
        for (int i = 0; i < *n; i++) {
            fin >> b[i];
        }
        fin.close();
        return true;
    }

    /**
     * @brief 兼容原项目的结果验证接口
     */
    bool fCheck(const std::vector<int>& result, int n, int input_id) {
        std::string path = "nttdata/" + std::to_string(input_id) + ".out";
        std::ifstream fin(path);
        if (!fin.is_open()) {
            std::cerr << "错误：无法打开输出文件 " << path << std::endl;
            return false;
        }
        
        for (int i = 0; i < 2 * n - 1; i++) {
            int expected;
            fin >> expected;
            if (i < result.size() && result[i] != expected) {
                std::cerr << "结果不匹配：位置 " << i << ", 期望 " << expected 
                         << ", 实际 " << result[i] << std::endl;
                return false;
            }
        }
        fin.close();
        return true;
    }

    /**
     * @brief 多项式乘法（使用NTT）
     */
    std::vector<int> polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b, int modulus) {
        int n = a.size();
        int resultSize = 1;
        while (resultSize < 2 * n - 1) {
            resultSize <<= 1;
        }
        
        std::vector<int> fa(resultSize, 0), fb(resultSize, 0);
        for (int i = 0; i < n; i++) {
            fa[i] = a[i];
            fb[i] = b[i];
        }
        
        // 设置最优策略
        auto strategy = strategySelector_->selectOptimalStrategy(resultSize, modulus);
        nttEngine_->setStrategy(strategy);
        
        // 执行NTT
        nttEngine_->computeNTT(fa, resultSize, modulus);
        nttEngine_->computeNTT(fb, resultSize, modulus);
        
        // 点乘
        for (int i = 0; i < resultSize; i++) {
            fa[i] = (1LL * fa[i] * fb[i]) % modulus;
        }
        
        // 逆NTT
        nttEngine_->computeINTT(fa, resultSize, modulus);
        
        // 返回结果（去除填充的零）
        std::vector<int> result(2 * n - 1);
        for (int i = 0; i < 2 * n - 1; i++) {
            result[i] = fa[i];
        }
        
        return result;
    }

    /**
     * @brief 运行标准测试用例
     */
    void runStandardTestCases() {
        std::cout << "\n========================================" << std::endl;
        std::cout << "          标准测试用例验证" << std::endl;
        std::cout << "========================================" << std::endl;
        
        std::vector<int> testCases = {0, 1, 2, 3};
        bool allPassed = true;
        
        for (int testId : testCases) {
            TestResult result;
            result.testName = "标准测试用例 " + std::to_string(testId);
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            try {
                std::vector<int> a, b;
                int n, p;
                
                if (!fRead(a, b, &n, &p, testId)) {
                    result.passed = false;
                    result.errorMessage = "文件读取失败";
                } else {
                    auto polyResult = polynomialMultiply(a, b, p);
                    result.passed = fCheck(polyResult, n, testId);
                    
                    if (!result.passed) {
                        result.errorMessage = "结果验证失败";
                    }
                }
            } catch (const std::exception& e) {
                result.passed = false;
                result.errorMessage = std::string("异常: ") + e.what();
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
            
            std::cout << "测试用例 " << testId << ": " 
                      << (result.passed ? "✓ 通过" : "✗ 失败")
                      << " (" << std::fixed << std::setprecision(2) << result.executionTime << " ms)";
            
            if (!result.passed) {
                std::cout << " - " << result.errorMessage;
                allPassed = false;
            }
            std::cout << std::endl;
            
            testResults_.push_back(result);
        }
        
        std::cout << "\n总体结果: " << (allPassed ? "✓ 所有测试通过" : "✗ 部分测试失败") << std::endl;
    }

    /**
     * @brief 性能基准测试
     */
    void runPerformanceBenchmark() {
        std::cout << "\n========================================" << std::endl;
        std::cout << "          性能基准测试" << std::endl;
        std::cout << "========================================" << std::endl;
        
        std::vector<int> testSizes = {256, 512, 1024, 2048, 4096, 8192, 16384, 32768};
        int modulus = 998244353;
        int iterations = 5;
        
        std::cout << std::setw(10) << "大小" << std::setw(15) << "平均时间(μs)" 
                  << std::setw(15) << "吞吐量(MB/s)" << std::setw(12) << "策略" << std::endl;
        std::cout << std::string(52, '-') << std::endl;
        
        for (int size : testSizes) {
            // 生成随机测试数据
            std::vector<int> a(size), b(size);
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, modulus - 1);
            
            for (int i = 0; i < size; i++) {
                a[i] = dis(gen);
                b[i] = dis(gen);
            }
            
            double totalTime = 0.0;
            std::string strategyName;
            
            for (int iter = 0; iter < iterations; iter++) {
                auto testA = a, testB = b;
                
                auto startTime = std::chrono::high_resolution_clock::now();
                auto result = polynomialMultiply(testA, testB, modulus);
                auto endTime = std::chrono::high_resolution_clock::now();
                
                double iterTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();
                totalTime += iterTime;
                
                if (iter == 0) {
                    auto strategy = strategySelector_->selectOptimalStrategy(size * 2, modulus);
                    strategyName = getStrategyName(strategy.parallelStrategy);
                }
            }
            
            double avgTime = totalTime / iterations;
            double throughput = (size * sizeof(int) * 2) / (avgTime / 1e6) / (1024 * 1024); // MB/s
            
            std::cout << std::setw(10) << size 
                      << std::setw(15) << std::fixed << std::setprecision(2) << avgTime
                      << std::setw(15) << std::fixed << std::setprecision(1) << throughput
                      << std::setw(12) << strategyName << std::endl;
        }
    }

private:
    std::string getStrategyName(ParallelStrategy strategy) {
        switch (strategy) {
            case ParallelStrategy::SERIAL: return "串行";
            case ParallelStrategy::OPENMP_SIMD: return "OpenMP+SIMD";
            case ParallelStrategy::MPI_OPENMP_SIMD: return "MPI+OpenMP+SIMD";
            case ParallelStrategy::HYBRID_ALL: return "混合全部";
            default: return "未知";
        }
    }
};

int main() {
    std::cout << "=====================================================" << std::endl;
    std::cout << "    混合并行NTT集成测试和性能验证套件" << std::endl;
    std::cout << "=====================================================" << std::endl;
    
    try {
        IntegrationTestSuite testSuite;
        
        // 运行标准测试用例
        testSuite.runStandardTestCases();
        
        // 运行性能基准测试
        testSuite.runPerformanceBenchmark();
        
        std::cout << "\n=====================================================" << std::endl;
        std::cout << "    集成测试完成" << std::endl;
        std::cout << "=====================================================" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
