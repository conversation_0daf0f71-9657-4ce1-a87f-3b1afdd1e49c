/**
 * @file io_interface.hpp
 * @brief 统一的I/O接口，兼容原有项目规范
 * <AUTHOR> Acceleration Framework
 */

#ifndef IO_INTERFACE_HPP
#define IO_INTERFACE_HPP

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <chrono>
#include <iomanip>

namespace HybridNTT {

/**
 * @brief 统一的I/O管理器，提供与原项目兼容的接口
 */
class IOInterface {
public:
    /**
     * @brief 读取输入数据文件
     * @param a 多项式A的系数数组
     * @param b 多项式B的系数数组  
     * @param n 数组长度
     * @param p 模数
     * @param input_id 输入文件ID
     */
    static void fRead(std::vector<int>& a, std::vector<int>& b, int* n, int* p, int input_id) {
        std::string str1 = "../nttdata/";
        std::string str2 = std::to_string(input_id);
        std::string strin = str1 + str2 + ".in";
        
        std::ifstream fin(strin, std::ios::in);
        if (!fin) {
            std::cerr << "错误：无法打开输入文件 " << strin << std::endl;
            throw std::runtime_error("文件读取失败");
        }
        
        fin >> *n >> *p;
        a.resize(*n);
        b.resize(*n);
        
        for (int i = 0; i < *n; i++) {
            fin >> a[i];
        }
        for (int i = 0; i < *n; i++) {
            fin >> b[i];
        }
        fin.close();
    }

    /**
     * @brief 检查多项式乘法结果正确性
     * @param ab 结果数组
     * @param n 输入数组长度
     * @param input_id 输入文件ID
     * @return 结果是否正确
     */
    static bool fCheck(const std::vector<int>& ab, int n, int input_id) {
        std::string str1 = "../nttdata/";
        std::string str2 = std::to_string(input_id);
        std::string strout = str1 + str2 + ".out";
        
        std::ifstream fin(strout, std::ios::in);
        if (!fin) {
            std::cerr << "错误：无法打开输出文件 " << strout << std::endl;
            return false;
        }
        
        for (int i = 0; i < n * 2 - 1; i++) {
            int x;
            fin >> x;
            if (x != ab[i]) {
                std::cout << "多项式乘法结果错误 (测试用例 " << input_id << ")" << std::endl;
                fin.close();
                return false;
            }
        }
        
        std::cout << "多项式乘法结果正确 (测试用例 " << input_id << ")" << std::endl;
        fin.close();
        return true;
    }

    /**
     * @brief 写入输出结果文件
     * @param ab 结果数组
     * @param n 输入数组长度
     * @param input_id 输入文件ID
     */
    static void fWrite(const std::vector<int>& ab, int n, int input_id) {
        std::string str1 = "files/";
        std::string str2 = std::to_string(input_id);
        std::string strout = str1 + str2 + ".out";
        
        std::ofstream fout(strout, std::ios::out);
        if (!fout) {
            std::cerr << "错误：无法创建输出文件 " << strout << std::endl;
            return;
        }
        
        for (int i = 0; i < n * 2 - 1; i++) {
            fout << ab[i] << '\n';
        }
        fout.close();
    }

    /**
     * @brief 记录性能测试结果到CSV文件
     * @param algorithm_name 算法名称
     * @param test_case_id 测试用例ID
     * @param n 问题规模
     * @param p 模数
     * @param processes 进程数
     * @param time_us 执行时间（微秒）
     * @param additional_info 额外信息
     */
    static void recordPerformance(const std::string& algorithm_name, 
                                 int test_case_id, 
                                 int n, 
                                 int p, 
                                 int processes, 
                                 double time_us,
                                 const std::string& additional_info = "") {
        static bool header_written = false;
        
        std::ofstream fout("hybrid_results.csv", std::ios::app);
        if (!fout) {
            std::cerr << "错误：无法写入性能结果文件" << std::endl;
            return;
        }
        
        if (!header_written) {
            fout << "algorithm,test_case_id,n,p,processes,time_us,additional_info\n";
            header_written = true;
        }
        
        fout << algorithm_name << "," 
             << test_case_id << "," 
             << n << "," 
             << p << "," 
             << processes << "," 
             << std::fixed << std::setprecision(2) << time_us;
             
        if (!additional_info.empty()) {
            fout << "," << additional_info;
        }
        fout << "\n";
        fout.close();
    }

    /**
     * @brief 获取高精度时间戳
     * @return 当前时间点
     */
    static std::chrono::high_resolution_clock::time_point getCurrentTime() {
        return std::chrono::high_resolution_clock::now();
    }

    /**
     * @brief 计算时间差（微秒）
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（微秒）
     */
    static double getElapsedTime(const std::chrono::high_resolution_clock::time_point& start,
                               const std::chrono::high_resolution_clock::time_point& end) {
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        return static_cast<double>(duration.count());
    }

    /**
     * @brief 打印测试结果摘要
     * @param algorithm_name 算法名称
     * @param n 问题规模
     * @param p 模数
     * @param time_us 执行时间
     * @param speedup 加速比
     */
    static void printSummary(const std::string& algorithm_name,
                           int n, 
                           int p, 
                           double time_us,
                           double speedup = 0.0) {
        std::cout << "算法: " << algorithm_name << std::endl;
        std::cout << "规模: n = " << n << ", p = " << p << std::endl;
        std::cout << "时间: " << std::fixed << std::setprecision(2) << time_us << " 微秒" << std::endl;
        if (speedup > 0.0) {
            std::cout << "加速比: " << std::fixed << std::setprecision(2) << speedup << "x" << std::endl;
        }
        std::cout << "----------------------------------------" << std::endl;
    }
};

} // namespace HybridNTT

#endif // IO_INTERFACE_HPP 