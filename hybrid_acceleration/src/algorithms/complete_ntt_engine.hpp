/**
 * @file complete_ntt_engine.hpp
 * @brief 完整的混合并行NTT引擎实现
 * <AUTHOR> Acceleration Framework
 * 
 * 基于现有项目的所有并行策略，实现完整的混合并行框架
 * 包括MPI、OpenMP、SIMD(AVX/SSE/NEON)、CUDA、pthread等所有并行技术
 */

#pragma once

#include <vector>
#include <memory>
#include <cmath>
#include <algorithm>
#include <immintrin.h>
#include <omp.h>
#include <thread>
#include <mutex>
#include <atomic>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

#ifdef USE_MPI
#include <mpi.h>
#endif

#ifdef USE_CUDA
#include <cuda_runtime.h>
#endif

#include "../core/simple_hardware_detector.hpp"
#include "../core/simple_strategy_selector.hpp"

namespace HybridNTT {

/**
 * @brief 完整的混合并行NTT引擎
 * 
 * 实现所有粒度的并行策略：
 * 1. 数据并行：将数据分块并行处理
 * 2. 任务并行：将计算任务并行分发
 * 3. 混合并行：结合多种并行技术
 * 
 * 支持的算法变体：
 * - Radix-2 DIT/DIF
 * - Radix-4 DIT/DIF  
 * - Mixed-Radix
 * - Split-Radix
 * 
 * 支持的约简算法：
 * - 标准模运算
 * - Barrett约简
 * - Montgomery约简
 */
class CompleteNTTEngine {
public:
    // 算法变体枚举
    enum class NTTVariant {
        RADIX_2_DIT,
        RADIX_2_DIF,
        RADIX_4_DIT, 
        RADIX_4_DIF,
        MIXED_RADIX,
        SPLIT_RADIX
    };
    
    // 约简算法枚举
    enum class ReductionType {
        STANDARD,
        BARRETT,
        MONTGOMERY
    };
    
    // 并行策略枚举
    enum class ParallelStrategy {
        SERIAL,
        OPENMP_DATA_PARALLEL,
        OPENMP_TASK_PARALLEL,
        SIMD_AVX,
        SIMD_SSE,
        SIMD_NEON,
        MPI_DATA_PARALLEL,
        MPI_TASK_PARALLEL,
        CUDA_PARALLEL,
        PTHREAD_PARALLEL,
        HYBRID_ALL
    };

private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;
    
    // 预计算表
    std::vector<uint32_t> twiddle_factors_;
    std::vector<uint32_t> inv_twiddle_factors_;
    std::vector<int> bit_reverse_table_;
    
    // Barrett约简参数
    struct BarrettParams {
        uint64_t mod;
        uint64_t inv;
        int shift;
    };
    BarrettParams barrett_params_;
    
    // Montgomery约简参数
    struct MontgomeryParams {
        uint64_t mod;
        uint64_t r;
        uint64_t r_inv;
        uint64_t mod_inv;
    };
    MontgomeryParams montgomery_params_;

public:
    CompleteNTTEngine(std::shared_ptr<HardwareDetector> detector,
                     std::shared_ptr<StrategySelector> selector)
        : hardwareDetector_(detector), strategySelector_(selector) {}
    
    /**
     * @brief 执行NTT变换
     */
    void executeNTT(std::vector<int>& data, int modulus, bool inverse = false,
                   NTTVariant variant = NTTVariant::RADIX_2_DIT,
                   ReductionType reduction = ReductionType::STANDARD,
                   ParallelStrategy strategy = ParallelStrategy::HYBRID_ALL);
    
    /**
     * @brief 多项式乘法（使用NTT）
     */
    void polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b,
                           std::vector<int>& result, int modulus);

private:
    // ============ 核心算法实现 ============
    
    /**
     * @brief Radix-2 DIT NTT实现
     */
    void radix2_dit_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-2 DIF NTT实现  
     */
    void radix2_dif_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-4 DIT NTT实现
     */
    void radix4_dit_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-4 DIF NTT实现
     */
    void radix4_dif_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    // ============ 并行实现 ============
    
    /**
     * @brief OpenMP数据并行实现
     */
    void openmp_data_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief OpenMP任务并行实现
     */
    void openmp_task_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief SIMD向量化实现（AVX）
     */
    void simd_avx_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief SIMD向量化实现（NEON）
     */
    void simd_neon_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief MPI分布式并行实现
     */
    void mpi_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief CUDA GPU并行实现
     */
    void cuda_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief pthread多线程实现
     */
    void pthread_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    // ============ 工具函数 ============
    
    /**
     * @brief 预计算旋转因子
     */
    void precomputeTwiddleFactors(int n, int modulus);
    
    /**
     * @brief 预计算位反转表
     */
    void precomputeBitReverseTable(int n);
    
    /**
     * @brief 初始化Barrett约简参数
     */
    void initBarrettParams(int modulus);
    
    /**
     * @brief 初始化Montgomery约简参数
     */
    void initMontgomeryParams(int modulus);
    
    /**
     * @brief 快速幂运算
     */
    uint32_t fastPow(uint32_t base, uint32_t exp, uint32_t mod);
    
    /**
     * @brief 模逆元计算
     */
    uint32_t modularInverse(uint32_t a, uint32_t mod);
    
    /**
     * @brief Barrett约简
     */
    uint32_t barrettReduce(uint64_t x);
    
    /**
     * @brief Montgomery约简
     */
    uint32_t montgomeryReduce(uint64_t x);
    
    /**
     * @brief 位反转置换
     */
    void bitReversePermutation(std::vector<int>& data);
    
    /**
     * @brief 蝶形运算（标准）
     */
    void butterfly(int& a, int& b, uint32_t w, uint32_t mod);
    
    /**
     * @brief 蝶形运算（Barrett约简）
     */
    void butterflyBarrett(int& a, int& b, uint32_t w);
    
    /**
     * @brief 蝶形运算（Montgomery约简）
     */
    void butterflyMontgomery(int& a, int& b, uint32_t w);
};

} // namespace HybridNTT
