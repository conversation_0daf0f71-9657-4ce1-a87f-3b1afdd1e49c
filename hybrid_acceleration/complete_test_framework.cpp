/**
 * @file complete_test_framework.cpp
 * @brief 完整的测试框架，使用nttdata中的标准测试数据
 * 
 * 确保所有并行策略实现的正确性，提供全面的性能测试
 */

#include "src/algorithms/complete_ntt_engine.hpp"
#include "src/core/simple_hardware_detector.hpp"
#include "src/core/simple_strategy_selector.hpp"
#include <iostream>
#include <fstream>
#include <chrono>
#include <iomanip>
#include <vector>
#include <string>
#include <cassert>

using namespace HybridNTT;

/**
 * @brief 测试数据读取器
 */
class TestDataReader {
public:
    struct TestCase {
        int n;
        int p;
        std::vector<int> a;
        std::vector<int> b;
        std::vector<int> expected_result;
    };
    
    /**
     * @brief 从nttdata目录读取测试用例
     */
    static TestCase readTestCase(int id) {
        TestCase test_case;
        
        // 读取输入文件
        std::string input_path = "nttdata/" + std::to_string(id) + ".in";
        std::ifstream fin(input_path);
        if (!fin.is_open()) {
            throw std::runtime_error("Cannot open input file: " + input_path);
        }
        
        fin >> test_case.n >> test_case.p;
        test_case.a.resize(test_case.n);
        test_case.b.resize(test_case.n);
        
        for (int i = 0; i < test_case.n; i++) {
            fin >> test_case.a[i];
        }
        for (int i = 0; i < test_case.n; i++) {
            fin >> test_case.b[i];
        }
        fin.close();
        
        // 读取期望输出文件
        std::string output_path = "nttdata/" + std::to_string(id) + ".out";
        std::ifstream fout(output_path);
        if (!fout.is_open()) {
            throw std::runtime_error("Cannot open output file: " + output_path);
        }
        
        test_case.expected_result.resize(2 * test_case.n - 1);
        for (int i = 0; i < 2 * test_case.n - 1; i++) {
            fout >> test_case.expected_result[i];
        }
        fout.close();
        
        return test_case;
    }
    
    /**
     * @brief 获取可用的测试用例数量
     */
    static int getTestCaseCount() {
        int count = 0;
        while (true) {
            std::string path = "nttdata/" + std::to_string(count) + ".in";
            std::ifstream f(path);
            if (!f.is_open()) break;
            count++;
        }
        return count;
    }
};

/**
 * @brief 完整的测试套件
 */
class CompleteTestSuite {
private:
    std::shared_ptr<CompleteNTTEngine> engine_;
    std::vector<TestDataReader::TestCase> test_cases_;
    
public:
    CompleteTestSuite() {
        auto detector = std::make_shared<HardwareDetector>();
        auto selector = std::make_shared<StrategySelector>(detector);
        engine_ = std::make_shared<CompleteNTTEngine>(detector, selector);
        
        // 加载所有测试用例
        int test_count = TestDataReader::getTestCaseCount();
        std::cout << "Found " << test_count << " test cases in nttdata/" << std::endl;
        
        for (int i = 0; i < test_count; i++) {
            try {
                test_cases_.push_back(TestDataReader::readTestCase(i));
                std::cout << "Loaded test case " << i << ": n=" << test_cases_.back().n 
                         << ", p=" << test_cases_.back().p << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Error loading test case " << i << ": " << e.what() << std::endl;
            }
        }
    }
    
    /**
     * @brief 测试单个策略的正确性
     */
    bool testStrategy(CompleteNTTEngine::ParallelStrategy strategy, 
                     CompleteNTTEngine::NTTVariant variant,
                     const std::string& strategy_name) {
        std::cout << "\n=== Testing " << strategy_name << " ===\n";
        
        bool all_passed = true;
        
        for (size_t i = 0; i < test_cases_.size(); i++) {
            const auto& test_case = test_cases_[i];
            
            try {
                // 执行多项式乘法
                std::vector<int> result;
                auto start = std::chrono::high_resolution_clock::now();

                engine_->polynomialMultiply(test_case.a, test_case.b, result, test_case.p, strategy, variant);

                auto end = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
                
                // 验证结果
                bool correct = (result.size() == test_case.expected_result.size());
                if (correct) {
                    for (size_t j = 0; j < result.size(); j++) {
                        if (result[j] != test_case.expected_result[j]) {
                            correct = false;
                            break;
                        }
                    }
                }
                
                std::cout << "Test case " << i << ": ";
                if (correct) {
                    std::cout << "✓ PASS";
                } else {
                    std::cout << "✗ FAIL";
                    all_passed = false;
                }
                std::cout << " (n=" << test_case.n << ", time=" << duration.count() << "μs)" << std::endl;
                
            } catch (const std::exception& e) {
                std::cout << "Test case " << i << ": ✗ ERROR - " << e.what() << std::endl;
                all_passed = false;
            }
        }
        
        return all_passed;
    }
    
    /**
     * @brief 运行所有策略的正确性测试
     */
    void runCorrectnessTests() {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "           CORRECTNESS TESTING" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        struct StrategyTest {
            CompleteNTTEngine::ParallelStrategy strategy;
            CompleteNTTEngine::NTTVariant variant;
            std::string name;
        };
        
        std::vector<StrategyTest> strategies = {
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "Serial Radix-2 DIT"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIF, "Serial Radix-2 DIF"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::RADIX_4_DIT, "Serial Radix-4 DIT"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::RADIX_4_DIF, "Serial Radix-4 DIF"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::SPLIT_RADIX, "Serial Split-Radix"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::CACHE_OBLIVIOUS, "Serial Cache-Oblivious"},
            {CompleteNTTEngine::ParallelStrategy::OPENMP_DATA_PARALLEL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "OpenMP Data Parallel"},
            {CompleteNTTEngine::ParallelStrategy::OPENMP_TASK_PARALLEL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "OpenMP Task Parallel"},
            {CompleteNTTEngine::ParallelStrategy::OPENMP_DATA_PARALLEL,
             CompleteNTTEngine::NTTVariant::SPLIT_RADIX, "OpenMP Split-Radix"},
            {CompleteNTTEngine::ParallelStrategy::SIMD_AVX,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "SIMD AVX"},
            {CompleteNTTEngine::ParallelStrategy::SIMD_NEON,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "SIMD NEON"},
            {CompleteNTTEngine::ParallelStrategy::PTHREAD_PARALLEL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "pthread Parallel"},
            {CompleteNTTEngine::ParallelStrategy::PTHREAD_PARALLEL,
             CompleteNTTEngine::NTTVariant::WORK_STEALING, "pthread Work-Stealing"},
            {CompleteNTTEngine::ParallelStrategy::MPI_DATA_PARALLEL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "MPI Data Parallel"},
            {CompleteNTTEngine::ParallelStrategy::HYBRID_ALL,
             CompleteNTTEngine::NTTVariant::RADIX_2_DIT, "Hybrid All"},
            {CompleteNTTEngine::ParallelStrategy::SERIAL,
             CompleteNTTEngine::NTTVariant::MIXED_RADIX, "CRT Multi-Modulus"}
        };
        
        int passed = 0;
        int total = strategies.size();
        
        for (const auto& strategy_test : strategies) {
            if (testStrategy(strategy_test.strategy, strategy_test.variant, strategy_test.name)) {
                passed++;
            }
        }
        
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "CORRECTNESS TEST SUMMARY: " << passed << "/" << total << " strategies passed" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
    }
    
    /**
     * @brief 运行性能基准测试
     */
    void runPerformanceBenchmark() {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "           PERFORMANCE BENCHMARK" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        // 选择较大的测试用例进行性能测试
        std::vector<int> benchmark_cases;
        for (size_t i = 0; i < test_cases_.size(); i++) {
            if (test_cases_[i].n >= 256) {
                benchmark_cases.push_back(i);
            }
        }
        
        if (benchmark_cases.empty()) {
            std::cout << "No suitable test cases for performance benchmark" << std::endl;
            return;
        }
        
        struct BenchmarkStrategy {
            CompleteNTTEngine::ParallelStrategy strategy;
            std::string name;
        };
        
        std::vector<BenchmarkStrategy> strategies = {
            {CompleteNTTEngine::ParallelStrategy::SERIAL, "Serial"},
            {CompleteNTTEngine::ParallelStrategy::OPENMP_DATA_PARALLEL, "OpenMP"},
            {CompleteNTTEngine::ParallelStrategy::SIMD_AVX, "SIMD AVX"},
            {CompleteNTTEngine::ParallelStrategy::PTHREAD_PARALLEL, "pthread"},
            {CompleteNTTEngine::ParallelStrategy::HYBRID_ALL, "Hybrid"}
        };
        
        std::cout << std::setw(8) << "Size" << std::setw(12) << "Strategy" 
                  << std::setw(12) << "Time(μs)" << std::setw(12) << "Speedup" 
                  << std::setw(15) << "Throughput(MB/s)" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        for (int case_id : benchmark_cases) {
            const auto& test_case = test_cases_[case_id];
            double serial_time = 0;
            
            for (const auto& strategy : strategies) {
                std::vector<int> result;
                
                // 多次运行取平均
                const int runs = 5;
                double total_time = 0;
                
                for (int run = 0; run < runs; run++) {
                    auto start = std::chrono::high_resolution_clock::now();
                    engine_->polynomialMultiply(test_case.a, test_case.b, result, test_case.p);
                    auto end = std::chrono::high_resolution_clock::now();
                    
                    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
                    total_time += duration.count();
                }
                
                double avg_time = total_time / runs;
                if (strategy.strategy == CompleteNTTEngine::ParallelStrategy::SERIAL) {
                    serial_time = avg_time;
                }
                
                double speedup = (serial_time > 0) ? serial_time / avg_time : 1.0;
                double throughput = (test_case.n * sizeof(int) * 2) / (avg_time / 1e6) / (1024 * 1024);
                
                std::cout << std::setw(8) << test_case.n 
                         << std::setw(12) << strategy.name
                         << std::setw(12) << std::fixed << std::setprecision(1) << avg_time
                         << std::setw(12) << std::fixed << std::setprecision(2) << speedup
                         << std::setw(15) << std::fixed << std::setprecision(1) << throughput
                         << std::endl;
            }
            std::cout << std::string(60, '-') << std::endl;
        }
    }
};

int main() {
    try {
        CompleteTestSuite test_suite;
        
        // 运行正确性测试
        test_suite.runCorrectnessTests();
        
        // 运行性能基准测试
        test_suite.runPerformanceBenchmark();
        
        std::cout << "\nAll tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test framework error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
