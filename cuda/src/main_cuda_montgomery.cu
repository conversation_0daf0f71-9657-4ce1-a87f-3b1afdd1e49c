/*
 * ===========================================
 * 文件名: main_cuda_montgomery.cu
 * 描述: CUDA Montgomery规约优化的NTT实现 (Corrected)
 * 目标: 使用Montgomery规约优化模运算性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_montgomery.cu -o ntt_cuda_montgomery
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    bool ok = true;
    // std::cout << "Computed result for id " << id << ": ";
    // for(int i=0; i<2*n-1; ++i) std::cout << ab[i] << " ";
    // std::cout << std::endl;

    // std::cout << "Expected result for id " << id << ": ";
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        // std::cout << x << " ";
        if(x != ab[i]) {
            ok = false;
        }
    }
    // std::cout << std::endl;

    if(ok) {
        std::cout << "结果正确" << std::endl;
    } else {
        std::cout << "结果错误" << std::endl;
    }
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

__host__ unsigned int mod_inverse_host(unsigned int a, unsigned int m) {
    long long m0 = m, t, q;
    long long x0 = 0, x1 = 1;
    long long a_ll = a, m_ll = m;
    if (m_ll == 1) return 0;
    while (a_ll > 1) {
        if (m_ll == 0) return 0; // No inverse if gcd is not 1
        q = a_ll / m_ll;
        t = m_ll;
        m_ll = a_ll % m_ll;
        a_ll = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    if (a_ll != 1) return 0; // Inverse exists only if gcd is 1
    if (x1 < 0) x1 += m0;
    return (unsigned int)x1;
}

__host__ unsigned long long mod_inverse_host(unsigned long long a, unsigned long long m) {
    long long m0 = m, t, q;
    long long x0 = 0, x1 = 1;
    long long a_ll = a, m_ll = m;
    if (m_ll == 1) return 0;
    while (a_ll > 1) {
        if (m_ll == 0) return 0; // No inverse if gcd is not 1
        q = a_ll / m_ll;
        t = m_ll;
        m_ll = a_ll % m_ll;
        a_ll = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    if (a_ll != 1) return 0; // Inverse exists only if gcd is 1
    if (x1 < 0) x1 += m0;
    return (unsigned long long)x1;
}

struct MontgomeryParams {
    unsigned int mod;
    unsigned int mod_prime;
    unsigned int r2_mod;

    __host__ MontgomeryParams(unsigned int m = 0) : mod(m), mod_prime(0), r2_mod(0) {
        if (m > 0) {
            // 计算Montgomery约简所需参数
            // 计算-m^(-1) mod 2^32 使用Newton迭代法
            unsigned long long p_inv = m;
            for (int i = 0; i < 5; ++i) {
                p_inv = p_inv * (2 - m * p_inv);
            }
            mod_prime = (unsigned int)(-p_inv);

            // 计算R^2 mod m，其中R=2^32
            // 使用重复平方法计算
            unsigned long long r2 = 1ULL << 32;
            r2 %= m;
            r2 = (r2 * r2) % m;
            r2_mod = (unsigned int)r2;
        }
    }

    __host__ __device__ inline unsigned int reduce(unsigned long long x) const {
        unsigned int q = (unsigned int)x * mod_prime;
        unsigned long long t = x + (unsigned long long)q * mod;
        unsigned int res = (unsigned int)(t >> 32);
        return (res >= mod) ? res - mod : res;
    }

    __host__ __device__ inline unsigned int to_montgomery(unsigned int x) const {
        return reduce((unsigned long long)x * r2_mod);
    }

    __host__ __device__ inline unsigned int from_montgomery(unsigned int x) const {
        return reduce(x);
    }

    __host__ __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }

    __host__ __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? sum - mod : sum;
    }

    __host__ __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a - b + mod);
    }
};

__global__ void bit_reverse_kernel(int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        int target = rev[idx];
        if (idx < target) {
            int tmp = data[idx];
            data[idx] = data[target];
            data[target] = tmp;
        }
    }
}

__global__ void to_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.to_montgomery((unsigned int)data[idx]);
    }
}

__global__ void pointwise_mul_montgomery_kernel(int *A, const int *B, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        A[idx] = (int)mont.mul((unsigned int)A[idx], (unsigned int)B[idx]);
    }
}

__global__ void final_scaling_kernel(int *data, unsigned int factor_mont, MontgomeryParams mont, int n)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        unsigned int res_mont = mont.mul((unsigned int)data[idx], factor_mont);
        data[idx] = mont.from_montgomery(res_mont);
    }
}

__global__ void ntt_montgomery_kernel(int *data, int len, int wn_mont, MontgomeryParams mont, int n) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    // 使用快速幂计算旋转因子，避免精度损失
    unsigned int w = mont.to_montgomery(1);
    unsigned int wn_pow = wn_mont;
    int k_temp = k;

    // 计算w = wn^k mod p
    while(k_temp > 0) {
        if(k_temp & 1) {
            w = mont.mul(w, wn_pow);
        }
        wn_pow = mont.mul(wn_pow, wn_pow);
        k_temp >>= 1;
    }
    
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);
    
    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

__global__ void ntt_montgomery_precomp_kernel(int *data, const int *twiddles, int len, 
                                             MontgomeryParams mont, int n, int offset) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    unsigned int w = (unsigned int)twiddles[offset + k];
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);
    
    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

void precompute_twiddles_montgomery(std::vector<int>& twiddles, int n, int p, bool inverse, const MontgomeryParams& mont) {
    twiddles.assign(n, 0);
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        // 使用原根3计算旋转因子
        long long wn_normal = qpow(3, (p - 1) / len, p);
        if (inverse) {
            // 计算逆元 wn^(-1) = wn^(p-2) mod p
            wn_normal = qpow(wn_normal, p - 2, p);
        }
        
        // 初始化为1
        long long w_normal = 1;
        for (int i = 0; i < len / 2; i++) {
            // 转换为Montgomery形式并存储
            twiddles[offset + i] = mont.to_montgomery((unsigned int)w_normal);
            // 计算下一个旋转因子: w *= wn
            w_normal = (w_normal * wn_normal) % p;
        }
        offset += len / 2;
    }
}

void cuda_ntt_montgomery(int* d_data, int n, bool inverse, int p, bool use_precomp, const MontgomeryParams& mont, int* d_rev, int* d_twiddles) {
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());

    if (use_precomp) {
        int offset = 0;
        for (int len = 2; len <= n; len <<= 1) {
            int half_len = len >> 1;
            blocks = (n / len) * half_len / threads + 1;
            ntt_montgomery_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, mont, n, offset);
            offset += half_len;
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    } else {
        for (int len = 2; len <= n; len <<= 1) {
            long long wn_normal = qpow(3, (p - 1) / len, p);
            if (inverse) wn_normal = qpow(wn_normal, p - 2, p);
            unsigned int wn_mont = mont.to_montgomery((unsigned int)wn_normal);

            int half_len = len >> 1;
            blocks = (n / len) * half_len / threads + 1;

            ntt_montgomery_kernel<<<blocks, threads>>>(d_data, len, wn_mont, mont, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }
}


void cuda_poly_multiply_montgomery(int* a, int* b, int n, int p, bool use_precomp, double& time_ms_precomp, double& time_ms_no_precomp) {
    int lim = 1;
    while(lim < (2 * n - 1)) lim <<= 1;

    std::vector<int> rev(lim);
    for(int i = 0; i < lim; i++) {
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) ? (lim >> 1) : 0);
    }
    
    int *d_rev = nullptr;
    CHECK_CUDA(cudaMalloc(&d_rev, lim * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

    int *d_A = nullptr, *d_B = nullptr;
    CHECK_CUDA(cudaMalloc(&d_A, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_B, lim * sizeof(int)));
    
    std::vector<int> h_A(lim, 0), h_B(lim, 0);
    for(int i = 0; i < n; i++) h_A[i] = a[i];
    for(int i = 0; i < n; i++) h_B[i] = b[i];

    CHECK_CUDA(cudaMemcpy(d_A, h_A.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_B, h_B.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

    MontgomeryParams mont(p);
    int threads = 1024;
    int blocks = (lim + threads - 1) / threads;

    to_montgomery_kernel<<<blocks, threads>>>(d_A, mont, lim);
    to_montgomery_kernel<<<blocks, threads>>>(d_B, mont, lim);
    CHECK_CUDA(cudaDeviceSynchronize());

    int *d_twiddles = nullptr;

    // 确保重置计时器
    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    time_ms_precomp = 0;
    time_ms_no_precomp = 0;

    // With precomputation
    if (use_precomp) {
        // 预计算正向和逆向旋转因子（不计入时间）
        std::vector<int> h_twiddles_fwd, h_twiddles_inv;
        precompute_twiddles_montgomery(h_twiddles_fwd, lim, p, false, mont);
        precompute_twiddles_montgomery(h_twiddles_inv, lim, p, true, mont);

        // 分配GPU内存（不计入时间）
        int *d_twiddles_fwd = nullptr, *d_twiddles_inv = nullptr;
        CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, lim * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&d_twiddles_inv, lim * sizeof(int)));
        CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

        // 开始计时
        start = std::chrono::high_resolution_clock::now();

        cuda_ntt_montgomery(d_A, lim, false, p, use_precomp, mont, d_rev, d_twiddles_fwd);
        cuda_ntt_montgomery(d_B, lim, false, p, use_precomp, mont, d_rev, d_twiddles_fwd);

        pointwise_mul_montgomery_kernel<<<blocks, threads>>>(d_A, d_B, mont, lim);

        cuda_ntt_montgomery(d_A, lim, true, p, use_precomp, mont, d_rev, d_twiddles_inv);

        end = std::chrono::high_resolution_clock::now();
        time_ms_precomp = std::chrono::duration<double, std::milli>(end - start).count();

        // 清理预计算内存
        CHECK_CUDA(cudaFree(d_twiddles_fwd));
        CHECK_CUDA(cudaFree(d_twiddles_inv));
    } else {
        // 开始计时
        start = std::chrono::high_resolution_clock::now();

        cuda_ntt_montgomery(d_A, lim, false, p, use_precomp, mont, d_rev, d_twiddles);
        cuda_ntt_montgomery(d_B, lim, false, p, use_precomp, mont, d_rev, d_twiddles);

        pointwise_mul_montgomery_kernel<<<blocks, threads>>>(d_A, d_B, mont, lim);

        cuda_ntt_montgomery(d_A, lim, true, p, use_precomp, mont, d_rev, d_twiddles);

        end = std::chrono::high_resolution_clock::now();
        time_ms_no_precomp = std::chrono::duration<double, std::milli>(end - start).count();
    }

    unsigned int n_inv_p = mod_inverse_host((unsigned int)lim, (unsigned int)p);
    unsigned int factor_mont = mont.to_montgomery(n_inv_p);
    final_scaling_kernel<<<blocks, threads>>>(d_A, factor_mont, mont, 2 * n - 1);
    
    std::vector<int> h_res(2 * n - 1);
    CHECK_CUDA(cudaMemcpy(h_res.data(), d_A, (2 * n - 1) * sizeof(int), cudaMemcpyDeviceToHost));
    for(int i=0; i<2*n-1; ++i) a[i] = h_res[i];

    // Cleanup
    CHECK_CUDA(cudaFree(d_A));
    CHECK_CUDA(cudaFree(d_B));
    CHECK_CUDA(cudaFree(d_rev));
    if (d_twiddles != nullptr) {
        CHECK_CUDA(cudaFree(d_twiddles));
    }
}

int main() {
    for (int i = 0; i <= 3; ++i) {
        std::cout << "测试数据集 " << i << ":" << std::endl;
        
        // 读取输入数据
        int n, p;
        std::vector<int> a_vec(1 << 20), b_vec(1 << 20);
        int* a = a_vec.data();
        int* b = b_vec.data();
        fRead(a, b, &n, &p, i);
        
        // 创建副本用于不同测试，确保有足够空间存储结果
        std::vector<int> a_copy1(2 * n, 0);
        std::vector<int> b_copy1(2 * n, 0);
        std::vector<int> a_copy2(2 * n, 0);
        std::vector<int> b_copy2(2 * n, 0);

        // 复制原始数据
        for(int j = 0; j < n; ++j) {
            a_copy1[j] = a[j];
            b_copy1[j] = b[j];
            a_copy2[j] = a[j];
            b_copy2[j] = b[j];
        }
        
        // 无预计算版本
        double time_precomp = 0, time_no_precomp = 0;
        std::cout << "运行无预计算版本..." << std::endl;
        cuda_poly_multiply_montgomery(a_copy1.data(), b_copy1.data(), n, p, false, time_precomp, time_no_precomp);
        
        std::vector<int> result_no_precomp(2 * n);
        for(int j = 0; j < 2*n-1; ++j) result_no_precomp[j] = a_copy1[j];
        
        std::cout << "无预计算版本结果: ";
        fCheck(result_no_precomp.data(), n, i);
        std::cout << "无预计算版本执行时间: " << time_no_precomp << " ms" << std::endl;
        
        // 预计算版本
        time_precomp = 0;
        time_no_precomp = 0;
        std::cout << "运行预计算版本..." << std::endl;
        cuda_poly_multiply_montgomery(a_copy2.data(), b_copy2.data(), n, p, true, time_precomp, time_no_precomp);
        
        std::vector<int> result_precomp(2 * n);
        for(int j = 0; j < 2*n-1; ++j) result_precomp[j] = a_copy2[j];
        
        std::cout << "预计算版本结果: ";
        fCheck(result_precomp.data(), n, i);
        std::cout << "预计算版本执行时间: " << time_precomp << " ms" << std::endl;
        
        // 计算加速比
        if(time_precomp > 0 && time_no_precomp > 0) {
            double speedup = time_no_precomp / time_precomp;
            printf("加速比: %.2fx\n", speedup);
        }
        
        printf("----------------------------------------\n");
    }
    return 0;
}
