#!/usr/bin/env python3
"""
Comprehensive Performance Visualization for NTT Parallel Optimization
Generates publication-quality charts with English text and statistical analysis
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for publication-quality plots
plt.style.use('default')
sns.set_palette("husl")

def load_performance_data():
    """Load performance data from CSV files"""
    data_files = [
        'large_scale_performance_results.csv',
        'ultra_large_scale_performance_results.csv'
    ]
    
    all_data = []
    for file in data_files:
        if Path(file).exists():
            df = pd.read_csv(file)
            all_data.append(df)
            print(f"Loaded {len(df)} records from {file}")
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"Total records: {len(combined_df)}")
        return combined_df
    else:
        print("No data files found!")
        return None

def create_performance_comparison_chart(df):
    """Create performance comparison chart across different strategies"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('NTT Parallel Optimization Performance Analysis', fontsize=16, fontweight='bold')
    
    # Filter only PASS results
    df_pass = df[df['Correctness'] == 'PASS'].copy()
    
    # 1. Execution Time Comparison
    ax1 = axes[0, 0]
    pivot_time = df_pass.pivot_table(values='Time_ms', index='Size', columns='Strategy', aggfunc='mean')
    pivot_time.plot(kind='bar', ax=ax1, width=0.8)
    ax1.set_title('Execution Time Comparison (Lower is Better)', fontweight='bold')
    ax1.set_xlabel('Problem Size')
    ax1.set_ylabel('Execution Time (ms)')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. Throughput Comparison
    ax2 = axes[0, 1]
    pivot_throughput = df_pass.pivot_table(values='Throughput_MBps', index='Size', columns='Strategy', aggfunc='mean')
    pivot_throughput.plot(kind='bar', ax=ax2, width=0.8)
    ax2.set_title('Throughput Comparison (Higher is Better)', fontweight='bold')
    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Throughput (MB/s)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # 3. Speedup Analysis
    ax3 = axes[1, 0]
    speedup_data = []
    for size in df_pass['Size'].unique():
        size_data = df_pass[df_pass['Size'] == size]
        serial_time = size_data[size_data['Strategy'] == '串行']['Time_ms'].iloc[0] if len(size_data[size_data['Strategy'] == '串行']) > 0 else None
        
        if serial_time:
            for strategy in ['OpenMP并行', 'SIMD向量化', 'OpenMP+SIMD混合']:
                strategy_data = size_data[size_data['Strategy'] == strategy]
                if len(strategy_data) > 0:
                    parallel_time = strategy_data['Time_ms'].iloc[0]
                    speedup = serial_time / parallel_time
                    speedup_data.append({'Size': size, 'Strategy': strategy, 'Speedup': speedup})
    
    speedup_df = pd.DataFrame(speedup_data)
    if not speedup_df.empty:
        speedup_pivot = speedup_df.pivot_table(values='Speedup', index='Size', columns='Strategy', aggfunc='mean')
        speedup_pivot.plot(kind='bar', ax=ax3, width=0.8)
        ax3.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Baseline (1x)')
        ax3.set_title('Speedup Analysis (Higher is Better)', fontweight='bold')
        ax3.set_xlabel('Problem Size')
        ax3.set_ylabel('Speedup (x)')
        ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax3.grid(True, alpha=0.3)
    
    # 4. Scalability Analysis
    ax4 = axes[1, 1]
    for strategy in df_pass['Strategy'].unique():
        strategy_data = df_pass[df_pass['Strategy'] == strategy]
        strategy_grouped = strategy_data.groupby('Size')['Throughput_MBps'].mean()
        ax4.plot(strategy_grouped.index, strategy_grouped.values, marker='o', linewidth=2, label=strategy)
    
    ax4.set_title('Scalability Analysis', fontweight='bold')
    ax4.set_xlabel('Problem Size')
    ax4.set_ylabel('Average Throughput (MB/s)')
    ax4.set_xscale('log', base=2)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('comprehensive_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_statistical_summary(df):
    """Create statistical summary and analysis"""
    df_pass = df[df['Correctness'] == 'PASS'].copy()
    
    print("\n" + "="*80)
    print("COMPREHENSIVE STATISTICAL ANALYSIS")
    print("="*80)
    
    # Overall Statistics
    total_tests = len(df)
    passed_tests = len(df_pass)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n📊 OVERALL PERFORMANCE SUMMARY")
    print(f"{'Total Tests:':<25} {total_tests}")
    print(f"{'Passed Tests:':<25} {passed_tests}")
    print(f"{'Success Rate:':<25} {success_rate:.1f}%")
    
    # Strategy Performance Analysis
    print(f"\n🚀 STRATEGY PERFORMANCE ANALYSIS")
    strategy_stats = df_pass.groupby('Strategy').agg({
        'Time_ms': ['mean', 'std', 'min', 'max'],
        'Throughput_MBps': ['mean', 'std', 'min', 'max']
    }).round(3)
    
    print("\nExecution Time Statistics (ms):")
    print(strategy_stats['Time_ms'])
    
    print("\nThroughput Statistics (MB/s):")
    print(strategy_stats['Throughput_MBps'])
    
    # Best Performance Analysis
    print(f"\n🏆 BEST PERFORMANCE ANALYSIS")
    for size in sorted(df_pass['Size'].unique()):
        size_data = df_pass[df_pass['Size'] == size]
        best_time = size_data.loc[size_data['Time_ms'].idxmin()]
        best_throughput = size_data.loc[size_data['Throughput_MBps'].idxmax()]
        
        print(f"\nSize {size}:")
        print(f"  Best Time: {best_time['Time_ms']:.3f}ms ({best_time['Strategy']})")
        print(f"  Best Throughput: {best_throughput['Throughput_MBps']:.1f} MB/s ({best_throughput['Strategy']})")
    
    return df_pass

def main():
    """Main function to generate comprehensive performance analysis"""
    print("🔬 NTT Parallel Optimization - Comprehensive Performance Analysis")
    print("="*80)
    
    # Load data
    df = load_performance_data()
    if df is None:
        return
    
    # Create visualizations
    print("\n📈 Generating performance comparison charts...")
    create_performance_comparison_chart(df)
    
    # Generate statistical summary
    print("\n📋 Generating statistical analysis...")
    df_analysis = create_statistical_summary(df)
    
    # Export detailed analysis
    analysis_file = 'detailed_performance_analysis.csv'
    df_analysis.to_csv(analysis_file, index=False)
    print(f"\n💾 Detailed analysis exported to: {analysis_file}")
    
    print("\n✅ Comprehensive performance analysis completed!")
    print("Generated files:")
    print("  - comprehensive_performance_analysis.png")
    print("  - detailed_performance_analysis.csv")

if __name__ == "__main__":
    main()
