/**
 * @file hybrid_ntt_engine.hpp
 * @brief 混合NTT计算引擎，统一调度各种并行实现
 * <AUTHOR> Acceleration Framework
 */

#ifndef HYBRID_NTT_ENGINE_HPP
#define HYBRID_NTT_ENGINE_HPP

#include "../core/strategy_selector.hpp"
#include "../utils/io_interface.hpp"
#include <vector>
#include <memory>
#include <cmath>
#include <cstring>
#include <immintrin.h>

#ifdef _OPENMP
#include <omp.h>
#endif

#ifdef __CUDA_ARCH__
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

/**
 * @brief Barrett规约优化器
 */
class BarrettReducer {
private:
    uint64_t mod_;
    uint64_t inv_;
    
public:
    explicit BarrettReducer(uint64_t mod) : mod_(mod) {
        inv_ = (static_cast<__uint128_t>(1) << 64) / mod;
    }
    
    uint32_t reduce(uint64_t a) const {
        uint64_t q = (static_cast<__uint128_t>(a) * inv_) >> 64;
        uint64_t r = a - q * mod_;
        return static_cast<uint32_t>(r >= mod_ ? r - mod_ : r);
    }
    
    uint32_t multiply(uint32_t a, uint32_t b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
};

/**
 * @brief Montgomery规约优化器
 */
class MontgomeryReducer {
private:
    uint32_t mod_;
    uint32_t inv_;
    uint32_t r2_;
    
public:
    explicit MontgomeryReducer(uint32_t mod) : mod_(mod) {
        // 计算Montgomery参数
        inv_ = 1;
        for (int i = 0; i < 5; ++i) {
            inv_ *= 2 - mod_ * inv_;
        }
        r2_ = (static_cast<uint64_t>(1) << 32) % mod_;
        r2_ = (static_cast<uint64_t>(r2_) * r2_) % mod_;
    }
    
    uint32_t reduce(uint64_t a) const {
        uint32_t m = static_cast<uint32_t>(a) * inv_;
        uint32_t t = (a + static_cast<uint64_t>(m) * mod_) >> 32;
        return t >= mod_ ? t - mod_ : t;
    }
    
    uint32_t multiply(uint32_t a, uint32_t b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    uint32_t toMontgomery(uint32_t a) const {
        return multiply(a, r2_);
    }
    
    uint32_t fromMontgomery(uint32_t a) const {
        return reduce(a);
    }
};

/**
 * @brief 混合NTT计算引擎
 */
class HybridNTTEngine {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;
    OptimizationStrategy currentStrategy_;
    
    /**
     * @brief 快速幂运算
     * @param x 底数
     * @param y 指数
     * @param p 模数
     * @return x^y mod p
     */
    uint32_t fastPow(uint32_t x, uint32_t y, uint32_t p) const {
        uint32_t res = 1;
        x %= p;
        while (y) {
            if (y & 1) res = (static_cast<uint64_t>(res) * x) % p;
            x = (static_cast<uint64_t>(x) * x) % p;
            y >>= 1;
        }
        return res;
    }
    
    /**
     * @brief 计算位反转数组
     * @param rev 位反转数组
     * @param lim 数组长度
     */
    void computeReverseBits(std::vector<int>& rev, int lim) const {
        for (int i = 0; i < lim; ++i) {
            rev[i] = (rev[i >> 1] >> 1) | ((i & 1) ? (lim >> 1) : 0);
        }
    }
    
    /**
     * @brief 串行Radix-2 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void serialRadix2NTT(std::vector<int>& a, int lim, int opt, int p) const {
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief OpenMP并行Radix-2 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void openmpRadix2NTT(std::vector<int>& a, int lim, int opt, int p) const {
#ifdef _OPENMP
        omp_set_num_threads(currentStrategy_.numThreads);
        
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        #pragma omp parallel for
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) {
                #pragma omp critical
                {
                    std::swap(a[i], a[rev[i]]);
                }
            }
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            #pragma omp parallel for schedule(static)
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            #pragma omp parallel for
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
#else
        serialRadix2NTT(a, lim, opt, p);
#endif
    }
    
    /**
     * @brief SIMD优化的NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void simdNTT(std::vector<int>& a, int lim, int opt, int p) const {
        // 位反转
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        // 使用AVX2进行向量化蝶形运算
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                
                // 向量化蝶形运算（当向量宽度允许时）
                int j = 0;
                if (currentStrategy_.vectorWidth >= 8 && m >= 8) {
                    for (; j <= m - 8; j += 8) {
                        // 使用SIMD指令处理8个元素
                        __m256i u_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&a[i + j]));
                        __m256i v_vec = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&a[i + j + m]));
                        
                        // 简化的SIMD运算（实际实现需要更复杂的模运算）
                        __m256i add_vec = _mm256_add_epi32(u_vec, v_vec);
                        __m256i sub_vec = _mm256_sub_epi32(u_vec, v_vec);
                        
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&a[i + j]), add_vec);
                        _mm256_storeu_si256(reinterpret_cast<__m256i*>(&a[i + j + m]), sub_vec);
                        
                        w = (static_cast<uint64_t>(w) * fastPow(wn, 8, p)) % p;
                    }
                }
                
                // 处理剩余元素
                for (; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + m] = (u - v + p) % p;
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief Barrett规约优化的NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void barrettNTT(std::vector<int>& a, int lim, int opt, int p) const {
        BarrettReducer reducer(p);
        
        std::vector<int> rev(lim);
        computeReverseBits(rev, lim);
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        for (int len = 2; len <= lim; len <<= 1) {
            int m = len >> 1;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t u = a[i + j];
                    uint32_t v = reducer.multiply(a[i + j + m], w);
                    a[i + j] = (u + v >= p) ? (u + v - p) : (u + v);
                    a[i + j + m] = (u >= v) ? (u - v) : (u - v + p);
                    w = reducer.multiply(w, wn);
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = reducer.multiply(a[i], inv);
            }
        }
    }
    
    /**
     * @brief Radix-4 NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void radix4NTT(std::vector<int>& a, int lim, int opt, int p) const {
        int logN = static_cast<int>(std::log2(lim));
        if (logN % 2 != 0) {
            // 如果log2(lim)不是偶数，退回到Radix-2
            serialRadix2NTT(a, lim, opt, p);
            return;
        }
        
        // Radix-4位反转
        std::vector<int> rev(lim);
        for (int i = 0; i < lim; ++i) {
            rev[i] = 0;
            int temp = i;
            for (int j = 0; j < logN; j += 2) {
                rev[i] = (rev[i] << 2) | (temp & 3);
                temp >>= 2;
            }
        }
        
        for (int i = 0; i < lim; ++i) {
            if (i < rev[i]) std::swap(a[i], a[rev[i]]);
        }
        
        // Radix-4蝶形运算
        for (int len = 4; len <= lim; len <<= 2) {
            int m = len >> 2;
            uint32_t wn = fastPow(3, (p - 1) / len, p);
            if (opt == -1) wn = fastPow(wn, p - 2, p);
            
            for (int i = 0; i < lim; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < m; ++j) {
                    uint32_t w2 = (static_cast<uint64_t>(w) * w) % p;
                    uint32_t w3 = (static_cast<uint64_t>(w2) * w) % p;
                    
                    uint32_t u0 = a[i + j];
                    uint32_t u1 = (static_cast<uint64_t>(a[i + j + m]) * w) % p;
                    uint32_t u2 = (static_cast<uint64_t>(a[i + j + 2*m]) * w2) % p;
                    uint32_t u3 = (static_cast<uint64_t>(a[i + j + 3*m]) * w3) % p;
                    
                    uint32_t v0 = (u0 + u2) % p;
                    uint32_t v1 = (u1 + u3) % p;
                    uint32_t v2 = (u0 - u2 + p) % p;
                    uint32_t v3 = (u1 - u3 + p) % p;
                    
                    a[i + j] = (v0 + v1) % p;
                    a[i + j + m] = (v2 + v3) % p; // 简化版本
                    a[i + j + 2*m] = (v0 - v1 + p) % p;
                    a[i + j + 3*m] = (v2 - v3 + p) % p;
                    
                    w = (static_cast<uint64_t>(w) * wn) % p;
                }
            }
        }
        
        if (opt == -1) {
            uint32_t inv = fastPow(lim, p - 2, p);
            for (int i = 0; i < lim; ++i) {
                a[i] = (static_cast<uint64_t>(a[i]) * inv) % p;
            }
        }
    }
    
    /**
     * @brief 混合策略NTT实现
     * @param a 输入/输出数组
     * @param lim 变换长度
     * @param opt 1为正变换，-1为逆变换
     * @param p 模数
     */
    void hybridNTT(std::vector<int>& a, int lim, int opt, int p) const {
        // 根据当前策略选择具体实现
        switch (currentStrategy_.parallelStrategy) {
            case ParallelStrategy::SERIAL:
                if (currentStrategy_.nttAlgorithm == NTTAlgorithm::RADIX_4_DIT || 
                    currentStrategy_.nttAlgorithm == NTTAlgorithm::RADIX_4_DIF) {
                    radix4NTT(a, lim, opt, p);
                } else {
                    serialRadix2NTT(a, lim, opt, p);
                }
                break;
                
            case ParallelStrategy::OPENMP_ONLY:
            case ParallelStrategy::MPI_OPENMP:
                openmpRadix2NTT(a, lim, opt, p);
                break;
                
            case ParallelStrategy::SIMD_ONLY:
            case ParallelStrategy::OPENMP_SIMD:
            case ParallelStrategy::MPI_OPENMP_SIMD:
                if (currentStrategy_.modularStrategy == ModularStrategy::BARRETT) {
                    barrettNTT(a, lim, opt, p);
                } else {
                    simdNTT(a, lim, opt, p);
                }
                break;
                
            default:
                // 根据模运算策略选择
                if (currentStrategy_.modularStrategy == ModularStrategy::BARRETT) {
                    barrettNTT(a, lim, opt, p);
                } else if (currentStrategy_.nttAlgorithm == NTTAlgorithm::RADIX_4_DIT || 
                          currentStrategy_.nttAlgorithm == NTTAlgorithm::RADIX_4_DIF) {
                    radix4NTT(a, lim, opt, p);
                } else {
                    openmpRadix2NTT(a, lim, opt, p);
                }
                break;
        }
    }
    
public:
    /**
     * @brief 构造函数
     * @param detector 硬件检测器
     * @param selector 策略选择器
     */
    HybridNTTEngine(std::shared_ptr<HardwareDetector> detector,
                   std::shared_ptr<StrategySelector> selector)
        : hardwareDetector_(detector), strategySelector_(selector) {}
    
    /**
     * @brief 设置优化策略
     * @param strategy 优化策略
     */
    void setStrategy(const OptimizationStrategy& strategy) {
        currentStrategy_ = strategy;
    }
    
    /**
     * @brief 执行多项式乘法
     * @param a 多项式A的系数
     * @param b 多项式B的系数
     * @param ab 结果多项式的系数
     * @param n 输入多项式长度
     * @param p 模数
     */
    void polynomialMultiply(const std::vector<int>& a, 
                           const std::vector<int>& b, 
                           std::vector<int>& ab, 
                           int n, 
                           int p) {
        // 计算NTT需要的长度
        int lim = 1;
        while (lim < 2 * n) lim <<= 1;
        
        // 准备数组
        std::vector<int> A(lim, 0), B(lim, 0);
        for (int i = 0; i < n; ++i) {
            A[i] = a[i];
            B[i] = b[i];
        }
        
        // 执行正向NTT
        hybridNTT(A, lim, 1, p);
        hybridNTT(B, lim, 1, p);
        
        // 点乘
        for (int i = 0; i < lim; ++i) {
            A[i] = (static_cast<uint64_t>(A[i]) * B[i]) % p;
        }
        
        // 执行逆向NTT
        hybridNTT(A, lim, -1, p);
        
        // 复制结果
        ab.resize(2 * n - 1);
        for (int i = 0; i < 2 * n - 1; ++i) {
            ab[i] = A[i];
        }
    }
    
    /**
     * @brief 自动优化的多项式乘法
     * @param a 多项式A的系数
     * @param b 多项式B的系数
     * @param ab 结果多项式的系数
     * @param n 输入多项式长度
     * @param p 模数
     * @return 执行时间（微秒）
     */
    double autoOptimizedMultiply(const std::vector<int>& a, 
                                const std::vector<int>& b, 
                                std::vector<int>& ab, 
                                int n, 
                                int p) {
        // 自动选择最优策略
        auto strategy = strategySelector_->selectOptimalStrategy(n, p);
        setStrategy(strategy);
        
        // 输出策略信息
        std::cout << "已选择策略: ";
        switch (strategy.parallelStrategy) {
            case ParallelStrategy::OPENMP_SIMD:
                std::cout << "OpenMP + SIMD";
                break;
            case ParallelStrategy::MPI_OPENMP_SIMD:
                std::cout << "MPI + OpenMP + SIMD";
                break;
            default:
                std::cout << "混合优化";
                break;
        }
        std::cout << std::endl;
        
        // 计时执行
        auto startTime = IOInterface::getCurrentTime();
        polynomialMultiply(a, b, ab, n, p);
        auto endTime = IOInterface::getCurrentTime();
        
        return IOInterface::getElapsedTime(startTime, endTime);
    }
    
    /**
     * @brief 获取当前策略
     */
    const OptimizationStrategy& getCurrentStrategy() const {
        return currentStrategy_;
    }
};

} // namespace HybridNTT

#endif // HYBRID_NTT_ENGINE_HPP 