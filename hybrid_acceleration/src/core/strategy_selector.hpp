/**
 * @file strategy_selector.hpp
 * @brief 智能策略选择器，自动选择最优并行策略组合
 * <AUTHOR> Acceleration Framework
 */

#ifndef STRATEGY_SELECTOR_HPP
#define STRATEGY_SELECTOR_HPP

#include "hardware_detector.hpp"
#include <algorithm>
#include <cmath>
#include <memory>

namespace HybridNTT {

/**
 * @brief 并行策略类型枚举
 */
enum class ParallelStrategy {
    SERIAL,              // 串行
    OPENMP_ONLY,         // 仅OpenMP
    PTHREAD_ONLY,        // 仅pthread
    SIMD_ONLY,           // 仅SIMD
    CUDA_ONLY,           // 仅CUDA
    MPI_ONLY,            // 仅MPI
    OPENMP_SIMD,         // OpenMP + SIMD
    PTHREAD_SIMD,        // pthread + SIMD
    CUDA_SIMD,           // CUDA + SIMD
    MPI_OPENMP,          // MPI + OpenMP
    MPI_PTHREAD,         // MPI + pthread
    MPI_SIMD,            // MPI + SIMD
    MPI_CUDA,            // MPI + CUDA
    MPI_OPENMP_SIMD,     // MPI + OpenMP + SIMD
    MPI_PTHREAD_SIMD,    // MPI + pthread + SIMD
    HYBRID_ALL           // 全混合策略
};

/**
 * @brief NTT算法变体枚举
 */
enum class NTTAlgorithm {
    RADIX_2_DIT,         // Radix-2 DIT
    RADIX_2_DIF,         // Radix-2 DIF
    RADIX_4_DIT,         // Radix-4 DIT
    RADIX_4_DIF,         // Radix-4 DIF
    MIXED_RADIX,         // 混合Radix
    ADAPTIVE_RADIX       // 自适应Radix选择
};

/**
 * @brief 模运算优化策略枚举
 */
enum class ModularStrategy {
    NAIVE,               // 朴素模运算
    BARRETT,             // Barrett规约
    MONTGOMERY,          // Montgomery规约
    ADAPTIVE             // 自适应选择
};

/**
 * @brief 完整的优化策略配置
 */
struct OptimizationStrategy {
    ParallelStrategy parallelStrategy;    // 并行策略
    NTTAlgorithm nttAlgorithm;           // NTT算法
    ModularStrategy modularStrategy;      // 模运算策略
    
    int numThreads;                      // 线程数
    int blockSize;                       // 块大小
    int vectorWidth;                     // 向量宽度
    bool useSharedMemory;                // 使用共享内存
    bool usePrecomputation;              // 使用预计算
    bool usePipelining;                  // 使用流水线
    
    double estimatedPerformance;         // 预估性能评分
    double confidence;                   // 置信度
};

/**
 * @brief 策略选择器类
 */
class StrategySelector {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::vector<OptimizationStrategy> candidateStrategies_;
    
    /**
     * @brief 基于问题规模评估复杂度
     * @param n 问题规模
     * @return 复杂度评分
     */
    double estimateComplexity(int n) const {
        if (n <= 0) return 0.0;
        
        double logN = std::log2(n);
        return n * logN; // O(n log n)的复杂度
    }
    
    /**
     * @brief 估算并行效率
     * @param strategy 并行策略
     * @param numCores 核心数
     * @param problemSize 问题规模
     * @return 并行效率评分
     */
    double estimateParallelEfficiency(ParallelStrategy strategy, int numCores, int problemSize) const {
        double efficiency = 1.0;
        
        switch (strategy) {
            case ParallelStrategy::SERIAL:
                return 1.0;
                
            case ParallelStrategy::OPENMP_ONLY:
            case ParallelStrategy::PTHREAD_ONLY:
                // Amdahl定律近似
                efficiency = std::min(static_cast<double>(numCores), problemSize / 1000.0);
                break;
                
            case ParallelStrategy::SIMD_ONLY:
                efficiency = std::min(8.0, problemSize / 100.0); // 假设8路SIMD
                break;
                
            case ParallelStrategy::CUDA_ONLY:
                if (hardwareDetector_->getGPUInfo().available) {
                    int multiProcessors = hardwareDetector_->getGPUInfo().multiProcessors;
                    efficiency = std::min(static_cast<double>(multiProcessors * 32), problemSize / 10.0);
                }
                break;
                
            case ParallelStrategy::MPI_ONLY:
                if (hardwareDetector_->getNetworkInfo().hasMPI) {
                    int mpiRanks = hardwareDetector_->getNetworkInfo().mpiRanks;
                    efficiency = std::min(static_cast<double>(mpiRanks), problemSize / 10000.0);
                }
                break;
                
            default:
                // 组合策略：估算协同效应
                efficiency = estimateHybridEfficiency(strategy, numCores, problemSize);
                break;
        }
        
        return std::max(1.0, efficiency);
    }
    
    /**
     * @brief 估算混合策略效率
     */
    double estimateHybridEfficiency(ParallelStrategy strategy, int numCores, int problemSize) const {
        double baseEfficiency = 1.0;
        double synergy = 1.0; // 协同效应系数
        
        switch (strategy) {
            case ParallelStrategy::OPENMP_SIMD:
            case ParallelStrategy::PTHREAD_SIMD:
                baseEfficiency = estimateParallelEfficiency(ParallelStrategy::OPENMP_ONLY, numCores, problemSize);
                synergy = 1.5; // SIMD与线程并行的协同效应
                break;
                
            case ParallelStrategy::MPI_OPENMP:
            case ParallelStrategy::MPI_PTHREAD:
                if (hardwareDetector_->getNetworkInfo().hasMPI) {
                    double mpiEff = estimateParallelEfficiency(ParallelStrategy::MPI_ONLY, numCores, problemSize);
                    double threadEff = estimateParallelEfficiency(ParallelStrategy::OPENMP_ONLY, numCores, problemSize);
                    baseEfficiency = mpiEff * threadEff * 0.8; // 考虑通信开销
                }
                break;
                
            case ParallelStrategy::MPI_CUDA:
                if (hardwareDetector_->getNetworkInfo().hasMPI && hardwareDetector_->getGPUInfo().available) {
                    double mpiEff = estimateParallelEfficiency(ParallelStrategy::MPI_ONLY, numCores, problemSize);
                    double cudaEff = estimateParallelEfficiency(ParallelStrategy::CUDA_ONLY, numCores, problemSize);
                    baseEfficiency = mpiEff * cudaEff * 0.7; // GPU-MPI协调开销更大
                }
                break;
                
            case ParallelStrategy::HYBRID_ALL:
                // 全混合策略：考虑所有因素
                baseEfficiency = calculateOptimalHybridEfficiency(numCores, problemSize);
                break;
                
            default:
                break;
        }
        
        return baseEfficiency * synergy;
    }
    
    /**
     * @brief 计算最优混合策略效率
     */
    double calculateOptimalHybridEfficiency(int numCores, int problemSize) const {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();
        
        double efficiency = 1.0;
        
        // CPU并行贡献
        efficiency += cpuInfo.logicalCores * 0.8;
        
        // SIMD贡献
        if (cpuInfo.hasAVX512) efficiency *= 1.8;
        else if (cpuInfo.hasAVX2) efficiency *= 1.5;
        else if (cpuInfo.hasAVX) efficiency *= 1.3;
        
        // GPU贡献
        if (gpuInfo.available) {
            efficiency += gpuInfo.multiProcessors * 2.0;
        }
        
        // MPI贡献
        if (networkInfo.hasMPI) {
            efficiency *= networkInfo.mpiRanks * 0.9; // 考虑通信开销
        }
        
        // 根据问题规模调整
        double sizeScale = std::log10(std::max(1, problemSize)) / 6.0; // 归一化到0-1
        efficiency *= (0.5 + 0.5 * sizeScale);
        
        return efficiency;
    }
    
    /**
     * @brief 评估算法变体适用性
     */
    double evaluateAlgorithmVariant(NTTAlgorithm algorithm, int problemSize, const CPUInfo& cpuInfo) const {
        double score = 1.0;
        
        switch (algorithm) {
            case NTTAlgorithm::RADIX_2_DIT:
            case NTTAlgorithm::RADIX_2_DIF:
                // Radix-2适用于所有规模，但不是最优
                score = 1.0;
                break;
                
            case NTTAlgorithm::RADIX_4_DIT:
            case NTTAlgorithm::RADIX_4_DIF:
                // Radix-4适用于大规模，需要log_2(n)为偶数
                if (problemSize >= 1024) {
                    int logN = static_cast<int>(std::log2(problemSize));
                    if (logN % 2 == 0) {
                        score = 1.5; // 更高效
                    } else {
                        score = 0.5; // 不适用
                    }
                }
                break;
                
            case NTTAlgorithm::MIXED_RADIX:
                // 混合Radix适用于复杂情况
                if (problemSize >= 4096) {
                    score = 1.3;
                }
                break;
                
            case NTTAlgorithm::ADAPTIVE_RADIX:
                // 自适应选择总是最优
                score = 1.8;
                break;
        }
        
        // 考虑CPU特性
        if (cpuInfo.hasAVX512) score *= 1.2;
        else if (cpuInfo.hasAVX2) score *= 1.1;
        
        return score;
    }
    
    /**
     * @brief 评估模运算策略
     */
    double evaluateModularStrategy(ModularStrategy strategy, int modulus, const CPUInfo& cpuInfo) const {
        double score = 1.0;
        
        switch (strategy) {
            case ModularStrategy::NAIVE:
                score = 1.0; // 基准
                break;
                
            case ModularStrategy::BARRETT:
                // Barrett适用于固定模数的大量运算
                if (modulus > 1000000) {
                    score = 1.5;
                } else {
                    score = 1.2;
                }
                break;
                
            case ModularStrategy::MONTGOMERY:
                // Montgomery适用于连续的模运算
                score = 1.4;
                if (cpuInfo.hasAVX2) score *= 1.1;
                break;
                
            case ModularStrategy::ADAPTIVE:
                // 自适应选择
                score = 1.6;
                break;
        }
        
        return score;
    }
    
    /**
     * @brief 生成所有候选策略
     */
    void generateCandidateStrategies(int problemSize, int modulus) {
        candidateStrategies_.clear();
        
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        const auto& networkInfo = hardwareDetector_->getNetworkInfo();
        
        // 并行策略候选
        std::vector<ParallelStrategy> parallelStrategies = {
            ParallelStrategy::SERIAL,
            ParallelStrategy::OPENMP_ONLY,
            ParallelStrategy::SIMD_ONLY,
            ParallelStrategy::OPENMP_SIMD
        };
        
        if (gpuInfo.available) {
            parallelStrategies.push_back(ParallelStrategy::CUDA_ONLY);
            parallelStrategies.push_back(ParallelStrategy::CUDA_SIMD);
        }
        
        if (networkInfo.hasMPI) {
            parallelStrategies.push_back(ParallelStrategy::MPI_ONLY);
            parallelStrategies.push_back(ParallelStrategy::MPI_OPENMP);
            parallelStrategies.push_back(ParallelStrategy::MPI_OPENMP_SIMD);
            if (gpuInfo.available) {
                parallelStrategies.push_back(ParallelStrategy::MPI_CUDA);
            }
            if (problemSize >= 16384) {
                parallelStrategies.push_back(ParallelStrategy::HYBRID_ALL);
            }
        }
        
        // NTT算法候选
        std::vector<NTTAlgorithm> nttAlgorithms = {
            NTTAlgorithm::RADIX_2_DIT,
            NTTAlgorithm::RADIX_2_DIF,
            NTTAlgorithm::ADAPTIVE_RADIX
        };
        
        if (problemSize >= 1024) {
            nttAlgorithms.push_back(NTTAlgorithm::RADIX_4_DIT);
            nttAlgorithms.push_back(NTTAlgorithm::RADIX_4_DIF);
        }
        
        if (problemSize >= 4096) {
            nttAlgorithms.push_back(NTTAlgorithm::MIXED_RADIX);
        }
        
        // 模运算策略候选
        std::vector<ModularStrategy> modularStrategies = {
            ModularStrategy::NAIVE,
            ModularStrategy::BARRETT,
            ModularStrategy::MONTGOMERY,
            ModularStrategy::ADAPTIVE
        };
        
        // 生成所有组合
        for (auto parallelStrategy : parallelStrategies) {
            for (auto nttAlgorithm : nttAlgorithms) {
                for (auto modularStrategy : modularStrategies) {
                    OptimizationStrategy strategy;
                    strategy.parallelStrategy = parallelStrategy;
                    strategy.nttAlgorithm = nttAlgorithm;
                    strategy.modularStrategy = modularStrategy;
                    
                    // 设置具体参数
                    configureStrategyParameters(strategy, problemSize, modulus);
                    
                    // 评估性能
                    strategy.estimatedPerformance = evaluateStrategy(strategy, problemSize, modulus);
                    strategy.confidence = calculateConfidence(strategy, problemSize);
                    
                    candidateStrategies_.push_back(strategy);
                }
            }
        }
    }
    
    /**
     * @brief 配置策略参数
     */
    void configureStrategyParameters(OptimizationStrategy& strategy, int problemSize, int modulus) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        const auto& gpuInfo = hardwareDetector_->getGPUInfo();
        
        // 线程数配置
        switch (strategy.parallelStrategy) {
            case ParallelStrategy::OPENMP_ONLY:
            case ParallelStrategy::OPENMP_SIMD:
            case ParallelStrategy::MPI_OPENMP:
            case ParallelStrategy::MPI_OPENMP_SIMD:
                strategy.numThreads = cpuInfo.logicalCores;
                break;
            default:
                strategy.numThreads = 1;
                break;
        }
        
        // 块大小配置
        if (problemSize <= 1024) {
            strategy.blockSize = 64;
        } else if (problemSize <= 16384) {
            strategy.blockSize = 256;
        } else {
            strategy.blockSize = 1024;
        }
        
        // 向量宽度配置
        if (cpuInfo.hasAVX512) {
            strategy.vectorWidth = 16; // 512位 / 32位 = 16
        } else if (cpuInfo.hasAVX2) {
            strategy.vectorWidth = 8;  // 256位 / 32位 = 8
        } else if (cpuInfo.hasAVX) {
            strategy.vectorWidth = 8;
        } else {
            strategy.vectorWidth = 4;  // SSE
        }
        
        // 优化选项配置
        strategy.useSharedMemory = gpuInfo.available && (strategy.parallelStrategy == ParallelStrategy::CUDA_ONLY || strategy.parallelStrategy == ParallelStrategy::CUDA_SIMD);
        strategy.usePrecomputation = problemSize >= 4096;
        strategy.usePipelining = strategy.numThreads > 1 || gpuInfo.available;
    }
    
    /**
     * @brief 评估策略性能
     */
    double evaluateStrategy(const OptimizationStrategy& strategy, int problemSize, int modulus) {
        const auto& cpuInfo = hardwareDetector_->getCPUInfo();
        
        double complexity = estimateComplexity(problemSize);
        double parallelEfficiency = estimateParallelEfficiency(strategy.parallelStrategy, cpuInfo.logicalCores, problemSize);
        double algorithmScore = evaluateAlgorithmVariant(strategy.nttAlgorithm, problemSize, cpuInfo);
        double modularScore = evaluateModularStrategy(strategy.modularStrategy, modulus, cpuInfo);
        
        // 综合评分
        double baseScore = complexity / parallelEfficiency;
        double optimizationBonus = 1.0;
        
        if (strategy.usePrecomputation) optimizationBonus *= 1.2;
        if (strategy.usePipelining) optimizationBonus *= 1.1;
        if (strategy.useSharedMemory) optimizationBonus *= 1.15;
        
        double finalScore = baseScore * algorithmScore * modularScore / optimizationBonus;
        
        return 1000000.0 / finalScore; // 转换为性能评分（越高越好）
    }
    
    /**
     * @brief 计算策略置信度
     */
    double calculateConfidence(const OptimizationStrategy& strategy, int problemSize) {
        double confidence = 0.8; // 基础置信度
        
        // 根据硬件匹配度调整
        if (strategy.parallelStrategy == ParallelStrategy::CUDA_ONLY && !hardwareDetector_->getGPUInfo().available) {
            confidence = 0.0; // 无GPU时CUDA策略置信度为0
        }
        
        if (strategy.parallelStrategy == ParallelStrategy::MPI_ONLY && !hardwareDetector_->getNetworkInfo().hasMPI) {
            confidence = 0.0; // 无MPI时MPI策略置信度为0
        }
        
        // 根据问题规模调整
        if (problemSize < 1024 && strategy.parallelStrategy == ParallelStrategy::HYBRID_ALL) {
            confidence *= 0.5; // 小规模问题不适合复杂策略
        }
        
        return confidence;
    }

public:
    /**
     * @brief 构造函数
     * @param detector 硬件检测器
     */
    explicit StrategySelector(std::shared_ptr<HardwareDetector> detector) 
        : hardwareDetector_(detector) {}
    
    /**
     * @brief 选择最优策略
     * @param problemSize 问题规模 (n)
     * @param modulus 模数 (p)
     * @return 最优策略
     */
    OptimizationStrategy selectOptimalStrategy(int problemSize, int modulus) {
        if (!hardwareDetector_) {
            throw std::runtime_error("硬件检测器未初始化");
        }
        
        std::cout << "正在分析最优策略 (n=" << problemSize << ", p=" << modulus << ")..." << std::endl;
        
        // 生成候选策略
        generateCandidateStrategies(problemSize, modulus);
        
        // 筛选有效策略
        std::vector<OptimizationStrategy> validStrategies;
        for (const auto& strategy : candidateStrategies_) {
            if (strategy.confidence > 0.1) { // 最低置信度阈值
                validStrategies.push_back(strategy);
            }
        }
        
        if (validStrategies.empty()) {
            throw std::runtime_error("未找到有效的优化策略");
        }
        
        // 选择最优策略
        auto bestStrategy = *std::max_element(validStrategies.begin(), validStrategies.end(),
            [](const OptimizationStrategy& a, const OptimizationStrategy& b) {
                return a.estimatedPerformance * a.confidence < b.estimatedPerformance * b.confidence;
            });
        
        std::cout << "已选择最优策略，预估性能评分: " << bestStrategy.estimatedPerformance 
                  << " (置信度: " << bestStrategy.confidence << ")" << std::endl;
        
        return bestStrategy;
    }
    
    /**
     * @brief 获取所有候选策略
     */
    const std::vector<OptimizationStrategy>& getCandidateStrategies() const {
        return candidateStrategies_;
    }
    
    /**
     * @brief 打印策略详情
     */
    void printStrategyDetails(const OptimizationStrategy& strategy) const {
        std::cout << "\n======== 策略详情 ========" << std::endl;
        
        std::cout << "并行策略: ";
        switch (strategy.parallelStrategy) {
            case ParallelStrategy::SERIAL: std::cout << "串行"; break;
            case ParallelStrategy::OPENMP_ONLY: std::cout << "OpenMP"; break;
            case ParallelStrategy::SIMD_ONLY: std::cout << "SIMD"; break;
            case ParallelStrategy::CUDA_ONLY: std::cout << "CUDA"; break;
            case ParallelStrategy::MPI_ONLY: std::cout << "MPI"; break;
            case ParallelStrategy::OPENMP_SIMD: std::cout << "OpenMP + SIMD"; break;
            case ParallelStrategy::MPI_OPENMP_SIMD: std::cout << "MPI + OpenMP + SIMD"; break;
            case ParallelStrategy::HYBRID_ALL: std::cout << "全混合"; break;
            default: std::cout << "其他"; break;
        }
        std::cout << std::endl;
        
        std::cout << "NTT算法: ";
        switch (strategy.nttAlgorithm) {
            case NTTAlgorithm::RADIX_2_DIT: std::cout << "Radix-2 DIT"; break;
            case NTTAlgorithm::RADIX_2_DIF: std::cout << "Radix-2 DIF"; break;
            case NTTAlgorithm::RADIX_4_DIT: std::cout << "Radix-4 DIT"; break;
            case NTTAlgorithm::RADIX_4_DIF: std::cout << "Radix-4 DIF"; break;
            case NTTAlgorithm::MIXED_RADIX: std::cout << "混合Radix"; break;
            case NTTAlgorithm::ADAPTIVE_RADIX: std::cout << "自适应Radix"; break;
        }
        std::cout << std::endl;
        
        std::cout << "模运算策略: ";
        switch (strategy.modularStrategy) {
            case ModularStrategy::NAIVE: std::cout << "朴素"; break;
            case ModularStrategy::BARRETT: std::cout << "Barrett规约"; break;
            case ModularStrategy::MONTGOMERY: std::cout << "Montgomery规约"; break;
            case ModularStrategy::ADAPTIVE: std::cout << "自适应"; break;
        }
        std::cout << std::endl;
        
        std::cout << "线程数: " << strategy.numThreads << std::endl;
        std::cout << "块大小: " << strategy.blockSize << std::endl;
        std::cout << "向量宽度: " << strategy.vectorWidth << std::endl;
        std::cout << "共享内存: " << (strategy.useSharedMemory ? "是" : "否") << std::endl;
        std::cout << "预计算: " << (strategy.usePrecomputation ? "是" : "否") << std::endl;
        std::cout << "流水线: " << (strategy.usePipelining ? "是" : "否") << std::endl;
        std::cout << "性能评分: " << strategy.estimatedPerformance << std::endl;
        std::cout << "置信度: " << strategy.confidence << std::endl;
        
        std::cout << "========================\n" << std::endl;
    }
};

} // namespace HybridNTT

#endif // STRATEGY_SELECTOR_HPP 