#!/usr/bin/env python3
"""
生成NTT测试数据
与现有项目的测试数据格式兼容
"""

import os
import random

def generate_ntt_data(directory, n, p, file_id):
    """
    生成NTT测试数据文件
    .in文件格式: n p, 然后是两个长度为n的多项式
    .out文件格式: 多项式乘法的结果（长度为2n-1）
    """
    os.makedirs(directory, exist_ok=True)
    
    # 生成输入文件
    in_path = os.path.join(directory, f"{file_id}.in")
    
    # 生成两个随机多项式，系数在[0, p-1]范围内
    a = [random.randint(0, p - 1) for _ in range(n)]
    b = [random.randint(0, p - 1) for _ in range(n)]

    with open(in_path, 'w') as f:
        f.write(f"{n} {p}\n")
        f.write(" ".join(map(str, a)) + "\n")
        f.write(" ".join(map(str, b)) + "\n")
    
    # 计算多项式乘法结果（朴素方法）
    result = [0] * (2 * n - 1)
    for i in range(n):
        for j in range(n):
            result[i + j] = (result[i + j] + a[i] * b[j]) % p
    
    # 生成输出文件
    out_path = os.path.join(directory, f"{file_id}.out")
    with open(out_path, 'w') as f:
        f.write(" ".join(map(str, result)) + "\n")
    
    print(f"生成测试数据 {file_id}: n={n}, p={p}")

def main():
    """生成标准测试数据"""
    print("生成NTT测试数据...")
    
    # 标准测试用例（与现有项目兼容）
    test_cases = [
        (4, 7340033, 0),        # 小规模验证
        (1024, 998244353, 1),   # 中等规模测试1
        (2048, 998244353, 2),   # 中等规模测试2
        (4096, 998244353, 3),   # 中等规模测试3
    ]

    for n, p, file_id in test_cases:
        generate_ntt_data("nttdata", n, p, file_id)

    # 生成额外的测试用例用于性能测试
    additional_cases = [
        (64, 998244353, 4),
        (128, 998244353, 5),
        (256, 998244353, 6),
        (512, 998244353, 7),
        (8192, 998244353, 8),
        (16384, 998244353, 9),
    ]

    for n, p, file_id in additional_cases:
        generate_ntt_data("nttdata", n, p, file_id)
    
    print(f"\n总共生成了 {len(test_cases) + len(additional_cases)} 个测试用例")
    print("测试数据保存在 nttdata/ 目录")

if __name__ == "__main__":
    main()
