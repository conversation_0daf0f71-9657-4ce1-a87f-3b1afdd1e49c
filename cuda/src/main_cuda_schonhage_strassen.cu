/*
 * ===========================================
 * 文件名: main_cuda_schonhage_strassen.cu
 * 描述: Schönhage-Strassen 快速整数乘法算法的 CUDA 实现
 * 特性:
 *   - 递归分解为更小的 NTT 问题
 *   - 适合超大整数乘法 (> 10^6 位)
 *   - 理论复杂度 O(n log n log log n)
 *   - GPU 并行优化的递归实现
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_schonhage_strassen.cu -o ntt_cuda_schonhage_strassen
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <memory>
#include <cmath>

#define CHECK_CUDA(call)                                                                 \
    do {                                                                                 \
        cudaError_t err = call;                                                          \
        if (err != cudaSuccess) {                                                        \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE);                                                          \
        }                                                                                \
    } while (0)

// =========================================
// 1. 基础数学工具
// =========================================

/**
 * 快速幂运算
 * @param x 底数
 * @param y 指数  
 * @param p 模数
 * @return x^y mod p
 */
__host__ __device__ static inline long long qpow(long long x, long long y, long long p) {
    long long res = 1;
    x %= p;
    while (y) {
        if (y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

/**
 * 扩展欧几里得算法求模逆
 */
__host__ static long long mod_inverse(long long a, long long m) {
    if (m == 1) return 0;
    long long m0 = m, x0 = 0, x1 = 1;
    while (a > 1) {
        long long q = a / m;
        long long t = m;
        m = a % m;
        a = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    return x1 < 0 ? x1 + m0 : x1;
}

// =========================================
// 2. I/O 工具函数
// =========================================

static void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

static void fCheck(const int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) {
            std::cout << "结果错误" << std::endl; return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// =========================================
// 3. Schönhage-Strassen 核心数据结构
// =========================================

/**
 * 大整数表示结构
 */
struct BigInteger {
    std::vector<unsigned int> digits;  // 以2^32为基数的数字表示
    bool negative;
    
    BigInteger() : negative(false) {}
    BigInteger(const std::vector<int>& poly) : negative(false) {
        digits.resize(poly.size());
        for (size_t i = 0; i < poly.size(); ++i) {
            digits[i] = static_cast<unsigned int>(poly[i]);
        }
        normalize();
    }
    
    void normalize() {
        while (digits.size() > 1 && digits.back() == 0) {
            digits.pop_back();
        }
        if (digits.empty()) digits.push_back(0);
    }
    
    size_t size() const { return digits.size(); }
};

/**
 * Schönhage-Strassen 参数配置
 */
struct SSParams {
    int threshold;          // 递归阈值
    int base_bits;          // 基数位数
    unsigned int ntt_mod;   // NTT 模数
    unsigned int primitive_root; // 原根
    
    SSParams(int thresh = 64, int bits = 16, unsigned int mod = 998244353)
        : threshold(thresh), base_bits(bits), ntt_mod(mod), primitive_root(3) {}
};

// =========================================
// 4. CUDA 核函数
// =========================================

/**
 * GPU 位反转核函数
 */
__global__ void bit_reverse_kernel(unsigned int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n && idx < rev[idx]) {
        unsigned int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

/**
 * GPU NTT 变换核函数
 */
__global__ void ntt_ss_kernel(unsigned int *data, int len, unsigned int wn, 
                             unsigned int mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if (block_id * len >= n) return;
    int base = block_id * len;
    if (base + local_id + half_len >= n) return;

    unsigned int w = 1;
    for (int i = 0; i < local_id; ++i) {
        w = ((unsigned long long)w * wn) % mod;
    }

    unsigned int u = data[base + local_id];
    unsigned int v = ((unsigned long long)data[base + local_id + half_len] * w) % mod;

    data[base + local_id] = (u + v) % mod;
    data[base + local_id + half_len] = (u - v + mod) % mod;
}

/**
 * 点乘核函数
 */
__global__ void pointwise_mul_ss_kernel(unsigned int *A, const unsigned int *B, 
                                        unsigned int mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        A[idx] = ((unsigned long long)A[idx] * B[idx]) % mod;
    }
}

/**
 * 缩放核函数 (逆变换)
 */
__global__ void scale_ss_kernel(unsigned int *data, unsigned int inv_n, 
                               unsigned int mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = ((unsigned long long)data[idx] * inv_n) % mod;
    }
}

// =========================================
// 5. GPU 辅助函数
// =========================================

/**
 * GPU NTT 实现
 */
void cuda_ntt_ss(unsigned int *h_data, int n, bool inverse, const SSParams& params) {
    unsigned int *d_data;
    int *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));

    // 位反转表生成
    std::vector<int> rev(n);
    int lg = 0; while ((1 << lg) < n) ++lg;
    for (int i = 0; i < n; ++i) {
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) ? (1 << (lg - 1)) : 0);
    }

    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    int threads = 512;
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());

    for (int len = 2; len <= n; len <<= 1) {
        unsigned int wn = qpow(params.primitive_root, 
                              (params.ntt_mod - 1) / len, params.ntt_mod);
        if (inverse) {
            wn = qpow(wn, params.ntt_mod - 2, params.ntt_mod);
        }
        
        int half_len = len >> 1;
        int total = n / len * half_len;
        blocks = (total + threads - 1) / threads;
        ntt_ss_kernel<<<blocks, threads>>>(d_data, len, wn, params.ntt_mod, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    if (inverse) {
        unsigned int inv_n = qpow(n, params.ntt_mod - 2, params.ntt_mod);
        blocks = (n + threads - 1) / threads;
        scale_ss_kernel<<<blocks, threads>>>(d_data, inv_n, params.ntt_mod, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(unsigned int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// =========================================
// 6. Schönhage-Strassen 主算法
// =========================================

/**
 * 基础多项式乘法 (小尺寸情况)
 */
std::vector<unsigned int> basic_multiply(const std::vector<unsigned int>& a, 
                                        const std::vector<unsigned int>& b,
                                        const SSParams& params) {
    int n = a.size(), m = b.size();
    std::vector<unsigned int> result(n + m - 1, 0);
    
    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < m; ++j) {
            unsigned long long prod = (unsigned long long)a[i] * b[j];
            result[i + j] = (result[i + j] + prod) % params.ntt_mod;
        }
    }
    return result;
}

/**
 * NTT 卷积乘法
 */
std::vector<unsigned int> ntt_multiply(const std::vector<unsigned int>& a, 
                                      const std::vector<unsigned int>& b,
                                      const SSParams& params) {
    int result_size = a.size() + b.size() - 1;
    int lim = 1;
    while (lim < result_size) lim <<= 1;
    
    std::vector<unsigned int> A(lim, 0), B(lim, 0);
    for (size_t i = 0; i < a.size(); ++i) A[i] = a[i];
    for (size_t i = 0; i < b.size(); ++i) B[i] = b[i];
    
    cuda_ntt_ss(A.data(), lim, false, params);
    cuda_ntt_ss(B.data(), lim, false, params);
    
    // 点乘
    unsigned int *d_A, *d_B;
    CHECK_CUDA(cudaMalloc(&d_A, lim * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_B, lim * sizeof(unsigned int)));
    CHECK_CUDA(cudaMemcpy(d_A, A.data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_B, B.data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
    
    int threads = 512;
    int blocks = (lim + threads - 1) / threads;
    pointwise_mul_ss_kernel<<<blocks, threads>>>(d_A, d_B, params.ntt_mod, lim);
    CHECK_CUDA(cudaDeviceSynchronize());
    CHECK_CUDA(cudaMemcpy(A.data(), d_A, lim * sizeof(unsigned int), cudaMemcpyDeviceToHost));
    
    cuda_ntt_ss(A.data(), lim, true, params);
    
    A.resize(result_size);
    CHECK_CUDA(cudaFree(d_A));
    CHECK_CUDA(cudaFree(d_B));
    return A;
}

/**
 * Schönhage-Strassen 递归乘法
 */
std::vector<unsigned int> schonhage_strassen_multiply(const std::vector<unsigned int>& a, 
                                                     const std::vector<unsigned int>& b,
                                                     const SSParams& params) {
    // 基础情况
    if (a.size() <= params.threshold || b.size() <= params.threshold) {
        return basic_multiply(a, b, params);
    }
    
    // 如果尺寸适中，使用 NTT
    if (a.size() <= 4096 && b.size() <= 4096) {
        return ntt_multiply(a, b, params);
    }
    
    // 递归分解
    int k = std::max(a.size(), b.size());
    int half = (k + 1) / 2;
    
    std::vector<unsigned int> a0(a.begin(), a.begin() + std::min(half, (int)a.size()));
    std::vector<unsigned int> a1;
    if ((int)a.size() > half) {
        a1.assign(a.begin() + half, a.end());
    }
    
    std::vector<unsigned int> b0(b.begin(), b.begin() + std::min(half, (int)b.size()));
    std::vector<unsigned int> b1;
    if ((int)b.size() > half) {
        b1.assign(b.begin() + half, b.end());
    }
    
    // 递归计算三个乘积
    auto z0 = schonhage_strassen_multiply(a0, b0, params);
    
    std::vector<unsigned int> z2;
    if (!a1.empty() && !b1.empty()) {
        z2 = schonhage_strassen_multiply(a1, b1, params);
    }
    
    // 计算 (a0 + a1) * (b0 + b1)
    std::vector<unsigned int> a_sum = a0;
    if (!a1.empty()) {
        a_sum.resize(std::max(a_sum.size(), a1.size()));
        for (size_t i = 0; i < a1.size(); ++i) {
            a_sum[i] = (a_sum[i] + a1[i]) % params.ntt_mod;
        }
    }
    
    std::vector<unsigned int> b_sum = b0;
    if (!b1.empty()) {
        b_sum.resize(std::max(b_sum.size(), b1.size()));
        for (size_t i = 0; i < b1.size(); ++i) {
            b_sum[i] = (b_sum[i] + b1[i]) % params.ntt_mod;
        }
    }
    
    auto z1_temp = schonhage_strassen_multiply(a_sum, b_sum, params);
    
    // z1 = z1_temp - z0 - z2
    std::vector<unsigned int> z1 = z1_temp;
    for (size_t i = 0; i < z0.size() && i < z1.size(); ++i) {
        z1[i] = (z1[i] - z0[i] + params.ntt_mod) % params.ntt_mod;
    }
    if (!z2.empty()) {
        for (size_t i = 0; i < z2.size() && i < z1.size(); ++i) {
            z1[i] = (z1[i] - z2[i] + params.ntt_mod) % params.ntt_mod;
        }
    }
    
    // 组合结果: result = z0 + z1*B^half + z2*B^(2*half)
    std::vector<unsigned int> result(z0.size() + z1.size() + z2.size() + 2 * half, 0);
    
    for (size_t i = 0; i < z0.size(); ++i) {
        result[i] = (result[i] + z0[i]) % params.ntt_mod;
    }
    
    for (size_t i = 0; i < z1.size(); ++i) {
        if (half + i < result.size()) {
            result[half + i] = (result[half + i] + z1[i]) % params.ntt_mod;
        }
    }
    
    for (size_t i = 0; i < z2.size(); ++i) {
        if (2 * half + i < result.size()) {
            result[2 * half + i] = (result[2 * half + i] + z2[i]) % params.ntt_mod;
        }
    }
    
    // 移除前导零
    while (result.size() > 1 && result.back() == 0) {
        result.pop_back();
    }
    
    return result;
}

// =========================================
// 7. 主函数接口
// =========================================

/**
 * 多项式乘法主函数
 */
void cuda_poly_multiply_ss(int *a, int *b, int *res, int n, int p) {
    SSParams params(64, 16, p);
    
    std::vector<unsigned int> poly_a(n), poly_b(n);
    for (int i = 0; i < n; ++i) {
        poly_a[i] = static_cast<unsigned int>(a[i]);
        poly_b[i] = static_cast<unsigned int>(b[i]);
    }
    
    auto result = schonhage_strassen_multiply(poly_a, poly_b, params);
    
    for (int i = 0; i < 2 * n - 1; ++i) {
        if (i < (int)result.size()) {
            res[i] = static_cast<int>(result[i]);
        } else {
            res[i] = 0;
        }
    }
}

// =========================================
// 8. 主函数
// =========================================

int main() {
    printf("Schönhage-Strassen CUDA 实现\n");
    printf("=========================================\n");
    
    int a[300000], b[300000], res[300000];

    for (int testcase = 0; testcase <= 3; ++testcase) {
        int n, p;
        fRead(a, b, &n, &p, testcase);
        memset(res, 0, sizeof(res));
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_ss(a, b, res, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(res, n, testcase);
        double t = std::chrono::duration<double, std::milli>(end - start).count();
        printf("用Schönhage-Strassen算法完成 n=%d p=%d 的多项式乘法, 用时 %.3f ms\n", 
               n, p, t);
    }
    
    printf("=========================================\n");
    printf("算法特性:\n");
    printf("  - 理论复杂度: O(n log n log log n)\n");
    printf("  - 递归分解: 适合超大整数\n");
    printf("  - GPU加速: 并行NTT计算\n");
    printf("  - 自适应: 小尺寸退化到基础算法\n");
    
    return 0;
} 