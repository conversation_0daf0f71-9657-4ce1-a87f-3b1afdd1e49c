#include "src/algorithms/complete_ntt_engine.hpp"
#include "src/core/hardware_detector.hpp"
#include "src/core/strategy_selector.hpp"
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <iomanip>

using namespace HybridNTT;

int main() {
    auto detector = std::make_shared<HardwareDetector>();
    auto selector = std::make_shared<StrategySelector>(detector);
    CompleteNTTEngine engine(detector, selector);
    
    std::vector<int> sizes = {256, 512, 1024, 2048, 4096, 8192, 16384};
    int modulus = 998244353;
    
    std::ofstream csv("results/performance_results.csv");
    csv << "Size,Strategy,Time_us,Throughput_MBps\n";
    
    for (int n : sizes) {
        std::vector<int> a(n), b(n);
        for (int i = 0; i < n; i++) {
            a[i] = rand() % modulus;
            b[i] = rand() % modulus;
        }
        
        std::vector<CompleteNTTEngine::ParallelStrategy> strategies = {
            CompleteNTTEngine::ParallelStrategy::SERIAL,
            CompleteNTTEngine::ParallelStrategy::OPENMP_DATA_PARALLEL,
            CompleteNTTEngine::ParallelStrategy::SIMD_AVX,
            CompleteNTTEngine::ParallelStrategy::PTHREAD_PARALLEL
        };
        
        std::vector<std::string> strategy_names = {
            "Serial", "OpenMP", "SIMD_AVX", "pthread"
        };
        
        for (size_t i = 0; i < strategies.size(); i++) {
            std::vector<int> result;
            
            auto start = std::chrono::high_resolution_clock::now();
            engine.polynomialMultiply(a, b, result, modulus);
            auto end = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            double throughput = (n * sizeof(int) * 2) / (duration.count() / 1e6) / (1024 * 1024);
            
            csv << n << "," << strategy_names[i] << "," << duration.count() << "," << throughput << "\n";
            
            std::cout << "n=" << n << " " << strategy_names[i] << ": " 
                     << duration.count() << "μs, " << throughput << " MB/s" << std::endl;
        }
    }
    
    csv.close();
    return 0;
}
