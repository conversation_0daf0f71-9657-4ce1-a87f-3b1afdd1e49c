/*
 * ===========================================
 * 文件名: main_cuda_advanced_optimizations.cu
 * 描述: CUDA NTT高级优化技术集合
 * 特性: 
 *   - Lemire Reduction (用于随机数生成和范围映射)
 *   - Barrett Reduction (快速模运算)
 *   - 多流并行处理
 *   - 共享内存优化
 *   - Warp-level原语
 *   - 内存合并访问
 *   - 预取优化
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_advanced_optimizations.cu -o ntt_cuda_advanced
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <random>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

#define MAX_STREAMS 8
#define WARP_SIZE 32

// Barrett Reduction参数 (修复版本 - 使用128位精度)
struct BarrettParams {
    unsigned int mod;
    unsigned __int128 inv128;

    __host__ __device__ BarrettParams(unsigned int m = 0) : mod(m) {
        if (m == 0) {
            inv128 = 0;
        } else {
            // 使用128位精度的Barrett因子
            inv128 = ((unsigned __int128)1 << 64) / m;
        }
    }

    __device__ inline unsigned int reduce(unsigned long long x) const {
        if (mod == 0) return (unsigned int)x;

        // Barrett规约: q = floor(x * inv128 / 2^64)
        unsigned __int128 q = ((unsigned __int128)x * inv128) >> 64;
        unsigned long long r = x - (unsigned long long)q * mod;

        // 最多需要一次额外的减法
        if (r >= mod) r -= mod;
        return (unsigned int)r;
    }

    __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }

    __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? sum - mod : sum;
    }

    __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a + mod - b);
    }
};

// Lemire Range Reduction (用于快速范围映射)
struct LemireRange {
    unsigned int range;
    
    __host__ __device__ LemireRange(unsigned int r) : range(r) {}
    
    // Lemire快速范围映射: ((uint64_t)x * range) >> 32
    __device__ inline unsigned int map_to_range(unsigned int x) const {
        return ((unsigned long long)x * range) >> 32;
    }
    
    // 生成范围内的伪随机数
    __device__ inline unsigned int fast_random(unsigned int seed, unsigned int index) const {
        // 简单的线性同余生成器
        unsigned int rnd = seed * 1664525U + index * 1013904223U;
        return map_to_range(rnd);
    }
};

// Warp-level优化的蝶形运算
__device__ inline void warp_butterfly(unsigned int &u, unsigned int &v, 
                                     unsigned int w, const BarrettParams &barrett) {
    unsigned int temp_v = barrett.mul(v, w);
    unsigned int new_u = barrett.add(u, temp_v);
    unsigned int new_v = barrett.sub(u, temp_v);
    
    u = new_u;
    v = new_v;
}

// CUDA Kernel: Warp优化的NTT蝶形运算
__global__ void ntt_warp_optimized_kernel(int *data, int len, unsigned int wn,
                                         BarrettParams barrett, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int warp_id = idx / WARP_SIZE;
    int lane_id = idx % WARP_SIZE;
    
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    // 使用Warp内协作计算旋转因子
    unsigned int w = 1;
    for(int i = 0; i < local_id; i++) {
        w = barrett.mul(w, wn);
    }
    
    // 预取数据到寄存器
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = (unsigned int)data[base + local_id + half_len];
    
    // Warp级别的蝶形运算
    warp_butterfly(u, v, w, barrett);
    
    // 写回结果
    data[base + local_id] = (int)u;
    data[base + local_id + half_len] = (int)v;
}

// CUDA Kernel: 使用Lemire生成测试数据
__global__ void generate_test_data_lemire(int *data, int n, unsigned int seed, 
                                         LemireRange lemire_range) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)lemire_range.fast_random(seed, idx);
    }
}

// CUDA Kernel: 简化的位反转 (修复版本)
__global__ void bit_reverse_coalesced_kernel(int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;

    if(idx < n && idx < rev[idx]) {
        // 简单直接的交换，避免复杂的共享内存操作
        int temp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = temp;
    }
}

// CUDA Kernel: 向量化点乘 (使用int2进行2路并行)
__global__ void pointwise_mul_vectorized_kernel(int2 *a, const int2 *b, 
                                               BarrettParams barrett, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        int2 va = a[idx];
        int2 vb = b[idx];
        
        // 并行处理两个元素
        va.x = (int)barrett.mul((unsigned int)va.x, (unsigned int)vb.x);
        va.y = (int)barrett.mul((unsigned int)va.y, (unsigned int)vb.y);
        
        a[idx] = va;
    }
}

// 多流管理器 (增强版)
struct AdvancedStreamManager {
    cudaStream_t streams[MAX_STREAMS];
    cudaEvent_t events[MAX_STREAMS];
    int num_streams;
    int current_stream;
    
    AdvancedStreamManager(int n_streams = 4) : num_streams(n_streams), current_stream(0) {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamCreate(&streams[i]));
            CHECK_CUDA(cudaEventCreate(&events[i]));
        }
    }
    
    ~AdvancedStreamManager() {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamDestroy(streams[i]));
            CHECK_CUDA(cudaEventDestroy(events[i]));
        }
    }
    
    cudaStream_t get_next_stream() {
        cudaStream_t stream = streams[current_stream];
        current_stream = (current_stream + 1) % num_streams;
        return stream;
    }
    
    void record_event(int stream_id) {
        CHECK_CUDA(cudaEventRecord(events[stream_id], streams[stream_id]));
    }
    
    void wait_for_event(int stream_id, int wait_stream_id) {
        CHECK_CUDA(cudaStreamWaitEvent(streams[wait_stream_id], events[stream_id], 0));
    }
    
    void synchronize_all() {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamSynchronize(streams[i]));
        }
    }
    
    float get_elapsed_time(int stream_id) {
        float time;
        CHECK_CUDA(cudaEventElapsedTime(&time, events[stream_id], events[stream_id]));
        return time;
    }
};

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 缩放操作
__global__ void scale_barrett_kernel(int *data, int inv_n,
                                    BarrettParams barrett, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)barrett.mul((unsigned int)data[idx], (unsigned int)inv_n);
    }
}

// 高级多流并行NTT实现
void cuda_ntt_advanced_multistream(int *h_data, int n, bool inverse, int p,
                                  AdvancedStreamManager& stream_mgr) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));

    BarrettParams barrett((unsigned int)p);

    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }

    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }

    // 使用流进行异步内存传输
    cudaStream_t stream = stream_mgr.get_next_stream();
    CHECK_CUDA(cudaMemcpyAsync(d_data, h_data, n * sizeof(int),
                              cudaMemcpyHostToDevice, stream));
    CHECK_CUDA(cudaMemcpyAsync(d_rev, rev.data(), n * sizeof(int),
                              cudaMemcpyHostToDevice, stream));

    // 位反转置换 (使用内存合并优化)
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_coalesced_kernel<<<blocks, threads, 0, stream>>>(d_data, d_rev, n);

    // NTT主循环 - 使用Warp优化
    for(int len = 2; len <= n; len <<= 1) {
        unsigned int wn = (unsigned int)qpow(3, (p-1)/len, p);
        if(inverse) wn = (unsigned int)qpow(wn, p-2, p);

        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;

        // 优化线程配置以适应Warp大小
        threads = ((total_butterflies + WARP_SIZE - 1) / WARP_SIZE) * WARP_SIZE;
        threads = std::min(threads, 1024);
        blocks = (total_butterflies + threads - 1) / threads;

        ntt_warp_optimized_kernel<<<blocks, threads, 0, stream>>>
            (d_data, len, wn, barrett, n);
    }

    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = std::min(1024, n);
        blocks = (n + threads - 1) / threads;

        scale_barrett_kernel<<<blocks, threads, 0, stream>>>(d_data, inv_n, barrett, n);
    }

    // 异步内存传输回主机
    CHECK_CUDA(cudaMemcpyAsync(h_data, d_data, n * sizeof(int),
                              cudaMemcpyDeviceToHost, stream));
    CHECK_CUDA(cudaStreamSynchronize(stream));

    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// Lemire范围映射演示
void demonstrate_lemire_reduction() {
    printf("\n🎲 Lemire Reduction 演示\n");
    printf("================================================================\n");

    const int n = 1000000;
    const unsigned int range = 100;

    // 在GPU上生成随机数
    int *d_data;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));

    LemireRange lemire_range(range);
    unsigned int seed = 12345;

    int threads = 256;
    int blocks = (n + threads - 1) / threads;

    auto start = std::chrono::high_resolution_clock::now();
    generate_test_data_lemire<<<blocks, threads>>>(d_data, n, seed, lemire_range);
    CHECK_CUDA(cudaDeviceSynchronize());
    auto end = std::chrono::high_resolution_clock::now();

    // 验证结果
    std::vector<int> h_data(n);
    CHECK_CUDA(cudaMemcpy(h_data.data(), d_data, n * sizeof(int), cudaMemcpyDeviceToHost));

    // 统计分布
    std::vector<int> histogram(range, 0);
    for(int i = 0; i < n; i++) {
        if(h_data[i] >= 0 && h_data[i] < range) {
            histogram[h_data[i]]++;
        }
    }

    double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
    printf("生成 %d 个范围在 [0, %d) 的随机数\n", n, range);
    printf("生成时间: %.3f ms\n", time_ms);
    printf("生成速度: %.2f M numbers/sec\n", n / (time_ms * 1000.0));

    // 检查分布均匀性
    int min_count = *std::min_element(histogram.begin(), histogram.end());
    int max_count = *std::max_element(histogram.begin(), histogram.end());
    double uniformity = (double)min_count / max_count;
    printf("分布均匀性: %.3f (1.0为完全均匀)\n", uniformity);

    CHECK_CUDA(cudaFree(d_data));
    printf("================================================================\n");
}

// 性能对比测试
void benchmark_optimization_techniques(int n, int p) {
    printf("\n⚡ 优化技术性能对比 (n=%d)\n", n);
    printf("================================================================\n");

    // 生成测试数据
    std::vector<int> data(n);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, p-1);

    for(int i = 0; i < n; i++) {
        data[i] = dis(gen);
    }

    AdvancedStreamManager stream_mgr(4);

    // 测试高级多流NTT
    std::vector<int> test_data = data;
    auto start = std::chrono::high_resolution_clock::now();
    cuda_ntt_advanced_multistream(test_data.data(), n, false, p, stream_mgr);
    cuda_ntt_advanced_multistream(test_data.data(), n, true, p, stream_mgr);
    auto end = std::chrono::high_resolution_clock::now();

    double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
    printf("高级多流NTT (正变换+逆变换): %.3f ms\n", time_ms);

    // 计算理论性能
    double flops = 2.0 * n * log2(n) * 5; // 估算浮点运算数
    printf("估算性能: %.2f GFLOPS\n", flops / (time_ms * 1e6));

    // 内存带宽估算
    size_t data_size = n * sizeof(int) * 4; // 读写数据
    double bandwidth_gbps = (data_size / (1024.0 * 1024.0 * 1024.0)) / (time_ms / 1000.0);
    printf("估算内存带宽: %.2f GB/s\n", bandwidth_gbps);

    printf("================================================================\n");
}

int main() {
    printf("🚀 CUDA 高级优化技术集合测试\n");
    printf("================================================================\n");

    // 检查CUDA设备
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }

    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));

    printf("GPU: %s\n", prop.name);
    printf("计算能力: %d.%d\n", prop.major, prop.minor);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    printf("Warp大小: %d\n", prop.warpSize);
    printf("全局内存: %.1f GB\n", prop.totalGlobalMem / (1024.0 * 1024.0 * 1024.0));
    printf("共享内存每块: %d KB\n", prop.sharedMemPerBlock / 1024);
    printf("================================================================\n");

    // 演示Lemire Reduction
    demonstrate_lemire_reduction();

    // 性能基准测试
    std::vector<int> test_sizes = {1024, 4096, 16384};
    for(int size : test_sizes) {
        benchmark_optimization_techniques(size, 998244353);
    }

    // 运行标准测试用例
    printf("\n📊 标准测试用例验证\n");
    printf("================================================================\n");

    int a[300000], b[300000], ab[300000];
    AdvancedStreamManager stream_mgr(4);

    for(int test_id = 0; test_id <= 3; test_id++) { // 测试所有用例
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));

        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);

        int lim = 1;
        while(lim < 2 * n) lim <<= 1;

        // 准备数据
        std::vector<int> A(lim, 0), B(lim, 0);
        for(int i = 0; i < n; i++) {
            A[i] = a[i];
            B[i] = b[i];
        }

        auto start = std::chrono::high_resolution_clock::now();

        // 正变换
        cuda_ntt_advanced_multistream(A.data(), lim, false, p, stream_mgr);
        cuda_ntt_advanced_multistream(B.data(), lim, false, p, stream_mgr);

        // 点乘
        for(int i = 0; i < lim; i++) {
            A[i] = (1LL * A[i] * B[i]) % p;
        }

        // 逆变换
        cuda_ntt_advanced_multistream(A.data(), lim, true, p, stream_mgr);

        auto end = std::chrono::high_resolution_clock::now();

        // 拷贝结果
        for(int i = 0; i < 2*n-1; i++) {
            ab[i] = A[i];
        }

        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        printf("----------------------------------------\n");
    }

    printf("\n🎯 优化技术总结\n");
    printf("================================================================\n");
    printf("✅ Barrett Reduction: 快速模运算，避免昂贵的除法\n");
    printf("✅ Lemire Reduction: 快速范围映射，用于随机数生成\n");
    printf("✅ 多流并行: 重叠计算和内存传输，提高GPU利用率\n");
    printf("✅ Warp优化: 利用SIMT架构，减少分支发散\n");
    printf("✅ 内存合并: 优化全局内存访问模式\n");
    printf("✅ 共享内存: 减少全局内存访问延迟\n");
    printf("✅ 向量化: 使用int2等向量类型提高吞吐量\n");
    printf("================================================================\n");

    printf("\n✅ 所有测试完成!\n");
    return 0;
}
