/**
 * @file numa_memory_manager.hpp
 * @brief NUMA感知内存管理器，实现高效的内存分配和数据局部性优化
 * <AUTHOR> Acceleration Framework
 */

#ifndef NUMA_MEMORY_MANAGER_HPP
#define NUMA_MEMORY_MANAGER_HPP

#include "../core/hardware_detector.hpp"
#include <vector>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <cstdlib>
#include <cstring>

#ifdef __linux__
#include <numa.h>
#include <numaif.h>
#endif

namespace HybridNTT {

/**
 * @brief 内存分配策略
 */
enum class MemoryAllocationStrategy {
    DEFAULT,           // 系统默认分配
    NUMA_LOCAL,        // NUMA本地分配
    NUMA_INTERLEAVED,  // NUMA交错分配
    HUGE_PAGES,        // 大页内存
    CACHE_ALIGNED,     // 缓存行对齐
    PREFETCH_OPTIMIZED // 预取优化
};

/**
 * @brief 内存块信息
 */
struct MemoryBlock {
    void* ptr;
    size_t size;
    int numa_node;
    MemoryAllocationStrategy strategy;
    bool is_aligned;
    std::chrono::time_point<std::chrono::steady_clock> last_access;
    
    MemoryBlock(void* p, size_t s, int node, MemoryAllocationStrategy strat, bool aligned)
        : ptr(p), size(s), numa_node(node), strategy(strat), is_aligned(aligned),
          last_access(std::chrono::steady_clock::now()) {}
};

/**
 * @brief NUMA感知内存管理器
 */
class NUMAMemoryManager {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    
    // 内存块管理
    std::unordered_map<void*, std::unique_ptr<MemoryBlock>> allocatedBlocks_;
    mutable std::mutex blocksMutex_;
    
    // 内存池
    std::vector<std::vector<void*>> memoryPools_;  // 每个NUMA节点一个池
    std::vector<std::unique_ptr<std::mutex>> poolMutexes_;
    
    // 性能统计
    mutable std::mutex statsMutex_;
    std::unordered_map<std::string, std::atomic<uint64_t>> stats_;
    
    // NUMA信息
    int numNUMANodes_;
    bool numaAvailable_;
    std::vector<int> cpuToNodeMapping_;
    
    /**
     * @brief 获取当前线程的NUMA节点
     */
    int getCurrentNUMANode() const {
#ifdef __linux__
        if (numaAvailable_) {
            int cpu = sched_getcpu();
            if (cpu >= 0 && cpu < cpuToNodeMapping_.size()) {
                return cpuToNodeMapping_[cpu];
            }
        }
#endif
        return 0;
    }
    
    /**
     * @brief 对齐内存地址到缓存行
     */
    void* alignToCache(void* ptr, size_t alignment = 64) const {
        uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
        uintptr_t aligned = (addr + alignment - 1) & ~(alignment - 1);
        return reinterpret_cast<void*>(aligned);
    }
    
    /**
     * @brief 分配NUMA本地内存
     */
    void* allocateNUMALocal(size_t size, int node) {
#ifdef __linux__
        if (numaAvailable_ && node >= 0 && node < numNUMANodes_) {
            void* ptr = numa_alloc_onnode(size, node);
            if (ptr) {
                stats_["numa_local_allocations"]++;
                return ptr;
            }
        }
#endif
        // 回退到普通分配
        return std::aligned_alloc(64, size);
    }
    
    /**
     * @brief 分配NUMA交错内存
     */
    void* allocateNUMAInterleaved(size_t size) {
#ifdef __linux__
        if (numaAvailable_) {
            void* ptr = numa_alloc_interleaved(size);
            if (ptr) {
                stats_["numa_interleaved_allocations"]++;
                return ptr;
            }
        }
#endif
        return std::aligned_alloc(64, size);
    }
    
    /**
     * @brief 分配大页内存
     */
    void* allocateHugePages(size_t size) {
        // 简化实现，实际需要系统支持
        void* ptr = std::aligned_alloc(2 * 1024 * 1024, size); // 2MB对齐
        if (ptr) {
            stats_["huge_page_allocations"]++;
        }
        return ptr;
    }

public:
    /**
     * @brief 构造函数
     */
    explicit NUMAMemoryManager(std::shared_ptr<HardwareDetector> detector)
        : hardwareDetector_(detector), numNUMANodes_(1), numaAvailable_(false) {
        initializeNUMA();
        initializeStats();
    }
    
    /**
     * @brief 析构函数
     */
    ~NUMAMemoryManager() {
        cleanup();
    }
    
    /**
     * @brief 初始化NUMA环境
     */
    void initializeNUMA() {
#ifdef __linux__
        if (numa_available() != -1) {
            numaAvailable_ = true;
            numNUMANodes_ = numa_max_node() + 1;
            
            // 构建CPU到NUMA节点的映射
            int maxCPU = numa_num_configured_cpus();
            cpuToNodeMapping_.resize(maxCPU);
            
            for (int cpu = 0; cpu < maxCPU; ++cpu) {
                cpuToNodeMapping_[cpu] = numa_node_of_cpu(cpu);
            }
            
            // 初始化内存池
            memoryPools_.resize(numNUMANodes_);
            for (int i = 0; i < numNUMANodes_; ++i) {
                poolMutexes_.emplace_back(std::make_unique<std::mutex>());
            }
        }
#endif
        
        if (!numaAvailable_) {
            // 单节点配置
            memoryPools_.resize(1);
            poolMutexes_.emplace_back(std::make_unique<std::mutex>());
            cpuToNodeMapping_.resize(std::thread::hardware_concurrency(), 0);
        }
    }
    
    /**
     * @brief 初始化统计信息
     */
    void initializeStats() {
        stats_["total_allocations"] = 0;
        stats_["total_deallocations"] = 0;
        stats_["numa_local_allocations"] = 0;
        stats_["numa_interleaved_allocations"] = 0;
        stats_["huge_page_allocations"] = 0;
        stats_["cache_aligned_allocations"] = 0;
        stats_["memory_migrations"] = 0;
    }
    
    /**
     * @brief 智能内存分配
     */
    void* allocate(size_t size, MemoryAllocationStrategy strategy = MemoryAllocationStrategy::NUMA_LOCAL) {
        void* ptr = nullptr;
        int targetNode = getCurrentNUMANode();
        bool isAligned = false;
        
        switch (strategy) {
            case MemoryAllocationStrategy::NUMA_LOCAL:
                ptr = allocateNUMALocal(size, targetNode);
                isAligned = true;
                break;
                
            case MemoryAllocationStrategy::NUMA_INTERLEAVED:
                ptr = allocateNUMAInterleaved(size);
                isAligned = true;
                break;
                
            case MemoryAllocationStrategy::HUGE_PAGES:
                ptr = allocateHugePages(size);
                isAligned = true;
                break;
                
            case MemoryAllocationStrategy::CACHE_ALIGNED:
                ptr = std::aligned_alloc(64, size);
                isAligned = true;
                stats_["cache_aligned_allocations"]++;
                break;
                
            default:
                ptr = std::malloc(size);
                break;
        }
        
        if (ptr) {
            // 记录分配信息
            std::lock_guard<std::mutex> lock(blocksMutex_);
            allocatedBlocks_[ptr] = std::make_unique<MemoryBlock>(ptr, size, targetNode, strategy, isAligned);
            stats_["total_allocations"]++;
        }
        
        return ptr;
    }
    
    /**
     * @brief 释放内存
     */
    void deallocate(void* ptr) {
        if (!ptr) return;
        
        std::lock_guard<std::mutex> lock(blocksMutex_);
        auto it = allocatedBlocks_.find(ptr);
        if (it != allocatedBlocks_.end()) {
            const auto& block = it->second;
            
#ifdef __linux__
            if (numaAvailable_ && (block->strategy == MemoryAllocationStrategy::NUMA_LOCAL ||
                                 block->strategy == MemoryAllocationStrategy::NUMA_INTERLEAVED)) {
                numa_free(ptr, block->size);
            } else {
                std::free(ptr);
            }
#else
            std::free(ptr);
#endif
            
            allocatedBlocks_.erase(it);
            stats_["total_deallocations"]++;
        }
    }
    
    /**
     * @brief 预取数据到缓存
     */
    void prefetchData(void* ptr, size_t size, int hint = 0) const {
        if (!ptr) return;
        
        const size_t cacheLineSize = 64;
        char* addr = static_cast<char*>(ptr);
        
        for (size_t offset = 0; offset < size; offset += cacheLineSize) {
            __builtin_prefetch(addr + offset, hint, 3); // 预取到所有缓存级别
        }
    }
    
    /**
     * @brief 迁移内存到指定NUMA节点
     */
    bool migrateToNode(void* ptr, int targetNode) {
#ifdef __linux__
        if (!numaAvailable_ || !ptr || targetNode < 0 || targetNode >= numNUMANodes_) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(blocksMutex_);
        auto it = allocatedBlocks_.find(ptr);
        if (it != allocatedBlocks_.end()) {
            const auto& block = it->second;
            
            unsigned long nodemask = 1UL << targetNode;
            int result = mbind(ptr, block->size, MPOL_BIND, &nodemask, sizeof(nodemask) * 8, MPOL_MF_MOVE);
            
            if (result == 0) {
                block->numa_node = targetNode;
                stats_["memory_migrations"]++;
                return true;
            }
        }
#endif
        return false;
    }
    
    /**
     * @brief 获取内存统计信息
     */
    std::unordered_map<std::string, uint64_t> getStats() const {
        std::unordered_map<std::string, uint64_t> result;
        for (const auto& [key, value] : stats_) {
            result[key] = value.load();
        }
        return result;
    }
    
    /**
     * @brief 优化数据局部性
     */
    void optimizeDataLocality(std::vector<int>& data) {
        if (data.empty()) return;
        
        int currentNode = getCurrentNUMANode();
        void* dataPtr = data.data();
        
        // 检查数据是否在当前NUMA节点
        std::lock_guard<std::mutex> lock(blocksMutex_);
        auto it = allocatedBlocks_.find(dataPtr);
        if (it != allocatedBlocks_.end() && it->second->numa_node != currentNode) {
            // 迁移数据到当前节点
            migrateToNode(dataPtr, currentNode);
        }
        
        // 预取数据
        prefetchData(dataPtr, data.size() * sizeof(int));
    }
    
    /**
     * @brief 清理资源
     */
    void cleanup() {
        std::lock_guard<std::mutex> lock(blocksMutex_);
        for (auto& [ptr, block] : allocatedBlocks_) {
#ifdef __linux__
            if (numaAvailable_ && (block->strategy == MemoryAllocationStrategy::NUMA_LOCAL ||
                                 block->strategy == MemoryAllocationStrategy::NUMA_INTERLEAVED)) {
                numa_free(ptr, block->size);
            } else {
                std::free(ptr);
            }
#else
            std::free(ptr);
#endif
        }
        allocatedBlocks_.clear();
    }
};

} // namespace HybridNTT

#endif // NUMA_MEMORY_MANAGER_HPP
