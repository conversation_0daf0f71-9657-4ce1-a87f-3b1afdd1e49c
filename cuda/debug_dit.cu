#include <cuda_runtime.h>
#include <iostream>
#include <vector>
#include <cstring>
#include <algorithm>
using namespace std;

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

typedef long long ll;

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

void calc_powg(int w[], int G, int P, int gen) {
    w[0] = 1; ll f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (ll)f * w[x - (1<<t)] % P;
    }
}

// CPU版本的DIT - 添加调试信息
void DIT_cpu_debug(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    
    cout << "DIT开始，数据: ";
    for(int i = 0; i < lim; i++) cout << f[i] << " ";
    cout << endl;
    
    for (int len = 2; len <= lim; len <<= 1) {
        cout << "len = " << len << endl;
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            cout << "  块 " << t << " (st=" << st << "), w[" << t << "]=" << w[t] << endl;
            for (int i = st; i < st + len/2; ++i) {
                g = f[i];
                h = f[i + len/2];
                cout << "    i=" << i << ", g=" << g << ", h=" << h;
                f[i] = (g + h) % P;
                f[i + len/2] = (P + g - h) * w[t] % P;
                cout << " -> f[" << i << "]=" << f[i] << ", f[" << (i + len/2) << "]=" << f[i + len/2] << endl;
            }
        }
        cout << "  阶段结果: ";
        for(int i = 0; i < lim; i++) cout << f[i] << " ";
        cout << endl;
    }
    
    const ll invl = qpow(lim, P-2, P);
    cout << "缩放因子: " << invl << endl;
    for (int i = 0; i < lim; ++i)
        f[i] = invl * f[i] % P;
    
    cout << "缩放后: ";
    for(int i = 0; i < lim; i++) cout << f[i] << " ";
    cout << endl;
    
    reverse(f + 1, f + lim);
    cout << "反转后: ";
    for(int i = 0; i < lim; i++) cout << f[i] << " ";
    cout << endl;
}

// CUDA Kernel: DIT NTT蝶形运算
__global__ void dit_kernel_debug(int *data, int len, const int *twiddles, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    int g = data[base + local_id];
    int h = data[base + local_id + half_len];
    
    // DIT: 先加减，对差值乘旋转因子
    data[base + local_id] = (g + h) % p;
    long long w = twiddles[block_id];
    data[base + local_id + half_len] = (1LL * (g - h + p) * w) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 数组反转（除了第一个元素）
__global__ void reverse_kernel(int *data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int total_pairs = (n - 1) / 2;
    
    if(idx < total_pairs) {
        int left = idx + 1;
        int right = n - 1 - idx;
        
        int temp = data[left];
        data[left] = data[right];
        data[right] = temp;
    }
}

// CUDA版本的DIT - 添加调试信息
void DIT_cuda_debug(int *h_data, int n, int p) {
    int *d_data, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));
    
    vector<int> twiddles(n);
    calc_powg(twiddles.data(), n, p, 3);
    
    // 对旋转因子取逆
    for(int& w : twiddles) {
        w = qpow(w, p-2, p);
    }
    
    cout << "逆旋转因子: ";
    for(int i = 0; i < n; i++) {
        cout << twiddles[i] << " ";
    }
    cout << endl;
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    cout << "GPU DIT开始，数据: ";
    for(int i = 0; i < n; i++) cout << h_data[i] << " ";
    cout << endl;
    
    for(int len = 2; len <= n; len <<= 1) {
        cout << "GPU len = " << len << endl;
        int half_len = len >> 1;
        int num_blocks = n / len;
        int total_butterflies = num_blocks * half_len;
        
        int threads = min(1024, total_butterflies);
        int blocks = (total_butterflies + threads - 1) / threads;
        
        dit_kernel_debug<<<blocks, threads>>>(d_data, len, d_twiddles, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
        
        // 读取中间结果
        CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
        cout << "  GPU阶段结果: ";
        for(int i = 0; i < n; i++) cout << h_data[i] << " ";
        cout << endl;
    }
    
    // 缩放
    int inv_n = qpow(n, p-2, p);
    cout << "GPU缩放因子: " << inv_n << endl;
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    cout << "GPU缩放后: ";
    for(int i = 0; i < n; i++) cout << h_data[i] << " ";
    cout << endl;
    
    // 反转数组
    reverse_kernel<<<blocks, threads>>>(d_data, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    cout << "GPU反转后: ";
    for(int i = 0; i < n; i++) cout << h_data[i] << " ";
    cout << endl;
    
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_twiddles));
}

int main() {
    const int n = 8;
    const int p = 7340033;
    
    // DIF变换后的数据
    int data_cpu[n] = {36, 7340029, 5454950, 1885075, 3761513, 7148387, 191638, 3578512};
    int data_gpu[n] = {36, 7340029, 5454950, 1885075, 3761513, 7148387, 191638, 3578512};
    
    vector<int> w(n);
    calc_powg(w.data(), n, p, 3);
    
    cout << "=== CPU DIT 调试 ===" << endl;
    DIT_cpu_debug(data_cpu, 3, p, w.data());
    
    cout << "\n=== GPU DIT 调试 ===" << endl;
    DIT_cuda_debug(data_gpu, n, p);
    
    return 0;
}
