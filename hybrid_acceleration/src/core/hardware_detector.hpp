/**
 * @file hardware_detector.hpp
 * @brief 硬件检测模块，自动识别系统硬件配置
 * <AUTHOR> Acceleration Framework
 */

#ifndef HARDWARE_DETECTOR_HPP
#define HARDWARE_DETECTOR_HPP

#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <unordered_map>
#include <thread>
#include <cstdlib>

#ifdef __linux__
#include <sys/sysinfo.h>
#include <unistd.h>
#endif

#ifdef _OPENMP
#include <omp.h>
#endif

#ifdef __CUDA_ARCH__
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

/**
 * @brief CPU特性信息结构体
 */
struct CPUInfo {
    int logicalCores;        // 逻辑核心数
    int physicalCores;       // 物理核心数
    double baseFrequency;    // 基础频率 (GHz)
    double maxFrequency;     // 最大频率 (GHz)
    int l1CacheSize;         // L1缓存大小 (KB)
    int l2CacheSize;         // L2缓存大小 (KB)
    int l3CacheSize;         // L3缓存大小 (KB)
    bool hasSSE;             // SSE支持
    bool hasAVX;             // AVX支持
    bool hasAVX2;            // AVX2支持
    bool hasAVX512;          // AVX512支持
    std::string vendor;      // 厂商 (Intel/AMD)
    std::string model;       // 型号
};

/**
 * @brief GPU特性信息结构体
 */
struct GPUInfo {
    bool available;          // GPU是否可用
    int deviceCount;         // GPU设备数量
    std::string vendor;      // 厂商 (NVIDIA/AMD)
    std::string model;       // 型号
    int computeCapability;   // 计算能力
    size_t globalMemory;     // 全局内存大小 (MB)
    size_t sharedMemory;     // 共享内存大小 (KB)
    int multiProcessors;     // 多处理器数量
    int warpSize;           // Warp大小
    int maxThreadsPerBlock; // 每块最大线程数
    double memoryBandwidth; // 内存带宽 (GB/s)
};

/**
 * @brief 内存特性信息结构体
 */
struct MemoryInfo {
    size_t totalRAM;         // 总内存 (MB)
    size_t availableRAM;     // 可用内存 (MB)
    int numaNodes;           // NUMA节点数
    double memoryBandwidth;  // 内存带宽 (GB/s)
    bool hasHugePage;        // 大页支持
};

/**
 * @brief 网络特性信息结构体
 */
struct NetworkInfo {
    bool hasMPI;             // MPI支持
    int mpiRanks;            // MPI进程数
    std::string interconnect; // 互连网络类型
    double bandwidth;        // 网络带宽 (GB/s)
    double latency;          // 网络延迟 (μs)
};

/**
 * @brief 硬件检测器类
 */
class HardwareDetector {
private:
    CPUInfo cpuInfo_;
    GPUInfo gpuInfo_;
    MemoryInfo memoryInfo_;
    NetworkInfo networkInfo_;
    bool detected_;

    /**
     * @brief 检测CPU特性
     */
    void detectCPU() {
        cpuInfo_ = {};
        
        // 检测核心数
        cpuInfo_.logicalCores = std::thread::hardware_concurrency();
        cpuInfo_.physicalCores = cpuInfo_.logicalCores / 2; // 简化估算

#ifdef _OPENMP
        cpuInfo_.logicalCores = omp_get_max_threads();
#endif

        // 检测CPU信息
        std::ifstream cpuinfo("/proc/cpuinfo");
        std::string line;
        while (std::getline(cpuinfo, line)) {
            if (line.find("vendor_id") != std::string::npos) {
                if (line.find("GenuineIntel") != std::string::npos) {
                    cpuInfo_.vendor = "Intel";
                } else if (line.find("AuthenticAMD") != std::string::npos) {
                    cpuInfo_.vendor = "AMD";
                }
            }
            if (line.find("model name") != std::string::npos) {
                size_t pos = line.find(": ");
                if (pos != std::string::npos) {
                    cpuInfo_.model = line.substr(pos + 2);
                    break;
                }
            }
        }

        // 检测SIMD支持（简化版本）
        cpuInfo_.hasSSE = true;   // 现代CPU都支持
        cpuInfo_.hasAVX = true;   // 大多数现代CPU支持
        cpuInfo_.hasAVX2 = true;  // 假设支持
        cpuInfo_.hasAVX512 = false; // 保守估计

        // 设置默认缓存大小
        cpuInfo_.l1CacheSize = 32;  // KB
        cpuInfo_.l2CacheSize = 256; // KB
        cpuInfo_.l3CacheSize = 8192; // KB

        // 设置默认频率
        cpuInfo_.baseFrequency = 2.4; // GHz
        cpuInfo_.maxFrequency = 3.2;  // GHz
    }

    /**
     * @brief 检测GPU特性
     */
    void detectGPU() {
        gpuInfo_ = {};
        gpuInfo_.available = false;

#ifdef __CUDA_ARCH__
        int deviceCount = 0;
        cudaError_t error = cudaGetDeviceCount(&deviceCount);
        
        if (error == cudaSuccess && deviceCount > 0) {
            gpuInfo_.available = true;
            gpuInfo_.deviceCount = deviceCount;
            
            cudaDeviceProp prop;
            cudaGetDeviceProperties(&prop, 0);
            
            gpuInfo_.model = prop.name;
            gpuInfo_.vendor = "NVIDIA";
            gpuInfo_.computeCapability = prop.major * 10 + prop.minor;
            gpuInfo_.globalMemory = prop.totalGlobalMem / (1024 * 1024); // MB
            gpuInfo_.sharedMemory = prop.sharedMemPerBlock / 1024; // KB
            gpuInfo_.multiProcessors = prop.multiProcessorCount;
            gpuInfo_.warpSize = prop.warpSize;
            gpuInfo_.maxThreadsPerBlock = prop.maxThreadsPerBlock;
            
            // 估算内存带宽
            gpuInfo_.memoryBandwidth = prop.memoryBusWidth * prop.memoryClockRate * 2.0 / 8.0 / 1000000.0;
        }
#endif
    }

    /**
     * @brief 检测内存特性
     */
    void detectMemory() {
        memoryInfo_ = {};

#ifdef __linux__
        struct sysinfo info;
        if (sysinfo(&info) == 0) {
            memoryInfo_.totalRAM = info.totalram / (1024 * 1024); // MB
            memoryInfo_.availableRAM = info.freeram / (1024 * 1024); // MB
        }
#endif

        // 检测NUMA节点数
        memoryInfo_.numaNodes = 1; // 默认值
        std::ifstream numa("/proc/sys/kernel/numa_node");
        if (numa.is_open()) {
            numa >> memoryInfo_.numaNodes;
        }

        // 估算内存带宽（基于DDR4）
        memoryInfo_.memoryBandwidth = 25.6; // GB/s for DDR4-3200

        // 检查大页支持
        memoryInfo_.hasHugePage = false;
        std::ifstream hugepage("/proc/meminfo");
        std::string line;
        while (std::getline(hugepage, line)) {
            if (line.find("HugePages_Total") != std::string::npos) {
                memoryInfo_.hasHugePage = true;
                break;
            }
        }
    }

    /**
     * @brief 检测网络特性
     */
    void detectNetwork() {
        networkInfo_ = {};
        networkInfo_.hasMPI = false;
        networkInfo_.mpiRanks = 1;
        
        // 检查MPI环境变量
        const char* mpi_rank = std::getenv("OMPI_COMM_WORLD_RANK");
        const char* mpi_size = std::getenv("OMPI_COMM_WORLD_SIZE");
        
        if (mpi_rank && mpi_size) {
            networkInfo_.hasMPI = true;
            networkInfo_.mpiRanks = std::atoi(mpi_size);
        }

        // 设置默认网络参数
        networkInfo_.interconnect = "Ethernet";
        networkInfo_.bandwidth = 1.0;  // GB/s
        networkInfo_.latency = 10.0;   // μs
    }

public:
    HardwareDetector() : detected_(false) {}

    /**
     * @brief 执行完整的硬件检测
     */
    void detectAll() {
        std::cout << "开始硬件检测..." << std::endl;
        
        detectCPU();
        detectGPU();
        detectMemory();
        detectNetwork();
        
        detected_ = true;
        std::cout << "硬件检测完成。" << std::endl;
    }

    /**
     * @brief 获取CPU信息
     */
    const CPUInfo& getCPUInfo() const {
        if (!detected_) {
            throw std::runtime_error("请先执行硬件检测");
        }
        return cpuInfo_;
    }

    /**
     * @brief 获取GPU信息
     */
    const GPUInfo& getGPUInfo() const {
        if (!detected_) {
            throw std::runtime_error("请先执行硬件检测");
        }
        return gpuInfo_;
    }

    /**
     * @brief 获取内存信息
     */
    const MemoryInfo& getMemoryInfo() const {
        if (!detected_) {
            throw std::runtime_error("请先执行硬件检测");
        }
        return memoryInfo_;
    }

    /**
     * @brief 获取网络信息
     */
    const NetworkInfo& getNetworkInfo() const {
        if (!detected_) {
            throw std::runtime_error("请先执行硬件检测");
        }
        return networkInfo_;
    }

    /**
     * @brief 打印硬件配置摘要
     */
    void printSummary() const {
        if (!detected_) {
            std::cout << "尚未执行硬件检测" << std::endl;
            return;
        }

        std::cout << "\n======== 硬件配置摘要 ========" << std::endl;
        
        // CPU信息
        std::cout << "CPU:" << std::endl;
        std::cout << "  厂商: " << cpuInfo_.vendor << std::endl;
        std::cout << "  型号: " << cpuInfo_.model << std::endl;
        std::cout << "  逻辑核心: " << cpuInfo_.logicalCores << std::endl;
        std::cout << "  物理核心: " << cpuInfo_.physicalCores << std::endl;
        std::cout << "  SIMD支持: ";
        if (cpuInfo_.hasAVX512) std::cout << "AVX512 ";
        if (cpuInfo_.hasAVX2) std::cout << "AVX2 ";
        if (cpuInfo_.hasAVX) std::cout << "AVX ";
        if (cpuInfo_.hasSSE) std::cout << "SSE";
        std::cout << std::endl;

        // GPU信息
        std::cout << "GPU:" << std::endl;
        if (gpuInfo_.available) {
            std::cout << "  设备数量: " << gpuInfo_.deviceCount << std::endl;
            std::cout << "  厂商: " << gpuInfo_.vendor << std::endl;
            std::cout << "  型号: " << gpuInfo_.model << std::endl;
            std::cout << "  计算能力: " << gpuInfo_.computeCapability / 10.0 << std::endl;
            std::cout << "  全局内存: " << gpuInfo_.globalMemory << " MB" << std::endl;
            std::cout << "  多处理器: " << gpuInfo_.multiProcessors << std::endl;
        } else {
            std::cout << "  无可用GPU设备" << std::endl;
        }

        // 内存信息
        std::cout << "内存:" << std::endl;
        std::cout << "  总内存: " << memoryInfo_.totalRAM << " MB" << std::endl;
        std::cout << "  可用内存: " << memoryInfo_.availableRAM << " MB" << std::endl;
        std::cout << "  NUMA节点: " << memoryInfo_.numaNodes << std::endl;
        std::cout << "  大页支持: " << (memoryInfo_.hasHugePage ? "是" : "否") << std::endl;

        // 网络信息
        std::cout << "网络:" << std::endl;
        std::cout << "  MPI支持: " << (networkInfo_.hasMPI ? "是" : "否") << std::endl;
        if (networkInfo_.hasMPI) {
            std::cout << "  MPI进程数: " << networkInfo_.mpiRanks << std::endl;
        }
        
        std::cout << "============================\n" << std::endl;
    }

    /**
     * @brief 计算硬件评分（用于策略选择）
     * @return 硬件评分
     */
    double calculateHardwareScore() const {
        if (!detected_) return 0.0;
        
        double score = 0.0;
        
        // CPU评分
        score += cpuInfo_.logicalCores * 10.0;
        if (cpuInfo_.hasAVX512) score += 50.0;
        else if (cpuInfo_.hasAVX2) score += 30.0;
        else if (cpuInfo_.hasAVX) score += 20.0;
        
        // GPU评分
        if (gpuInfo_.available) {
            score += gpuInfo_.multiProcessors * 20.0;
            score += (gpuInfo_.globalMemory / 1024.0) * 5.0; // GB
        }
        
        // 内存评分
        score += (memoryInfo_.totalRAM / 1024.0) * 2.0; // GB
        if (memoryInfo_.hasHugePage) score += 20.0;
        
        // 网络评分
        if (networkInfo_.hasMPI) {
            score += networkInfo_.mpiRanks * 15.0;
        }
        
        return score;
    }
};

} // namespace HybridNTT

#endif // HARDWARE_DETECTOR_HPP 