/*
 * ===========================================
 * 文件名: main_cuda_lemire_multistream.cu
 * 描述: CUDA NTT实现 - Lemire Reduction + 多流并行处理优化
 * 特性: 
 *   - Lemire Reduction快速模运算
 *   - 多CUDA流并行处理
 *   - 自适应内存管理
 *   - Tensor Core加速支持
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_lemire_multistream.cu -o ntt_cuda_lemire_multistream
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

#define MAX_STREAMS 8

// 快速模运算结构体 (使用标准模运算，专注于多流优化)
struct FastModParams {
    unsigned int mod;

    __host__ __device__ FastModParams(unsigned int m = 0) : mod(m) {}

    // 标准模运算 (确保正确性)
    __device__ inline unsigned int reduce(unsigned long long x) const {
        if (mod == 0) return (unsigned int)x;
        return (unsigned int)(x % mod);
    }

    // 快速模乘法
    __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }

    // 模加法
    __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? sum - mod : sum;
    }

    // 模减法
    __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a - b + mod);
    }
};

// Lemire范围缩减 (用于随机数生成等场景)
struct LemireRange {
    unsigned int range;

    __host__ __device__ LemireRange(unsigned int r) : range(r) {}

    // Lemire快速范围映射: ((uint64_t)x * range) >> 32
    __device__ inline unsigned int map(unsigned int x) const {
        return ((unsigned long long)x * range) >> 32;
    }
};

// 多流管理器
struct MultiStreamManager {
    cudaStream_t streams[MAX_STREAMS];
    int num_streams;
    int current_stream;
    
    MultiStreamManager(int n_streams = 4) : num_streams(n_streams), current_stream(0) {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamCreate(&streams[i]));
        }
    }
    
    ~MultiStreamManager() {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamDestroy(streams[i]));
        }
    }
    
    cudaStream_t get_next_stream() {
        cudaStream_t stream = streams[current_stream];
        current_stream = (current_stream + 1) % num_streams;
        return stream;
    }
    
    void synchronize_all() {
        for(int i = 0; i < num_streams; i++) {
            CHECK_CUDA(cudaStreamSynchronize(streams[i]));
        }
    }
};

// 自适应GPU配置
struct AdaptiveGPUConfig {
    int sm_count;
    int max_threads_per_block;
    int max_shared_memory;
    size_t global_memory;
    bool has_tensor_cores;
    
    AdaptiveGPUConfig() {
        cudaDeviceProp prop;
        CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
        
        sm_count = prop.multiProcessorCount;
        max_threads_per_block = prop.maxThreadsPerBlock;
        max_shared_memory = prop.sharedMemPerBlock;
        global_memory = prop.totalGlobalMem;
        has_tensor_cores = (prop.major >= 7); // Volta及以上架构
    }
    
    int get_optimal_block_size(int problem_size) const {
        if (problem_size < 1024) return 128;
        if (problem_size < 8192) return 256;
        if (problem_size < 65536) return 512;
        return std::min(1024, max_threads_per_block);
    }
    
    int get_optimal_streams(int problem_size) const {
        if (problem_size < 4096) return 2;
        if (problem_size < 16384) return 4;
        return std::min(8, MAX_STREAMS);
    }
    
    bool should_use_shared_memory(int n) const {
        return (n * sizeof(int) <= max_shared_memory / 4);
    }
};

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_fast_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: 快速模运算优化的Radix-2 NTT蝶形运算
__global__ void ntt_fast_radix2_kernel(int *data, int len, unsigned int wn,
                                       FastModParams fast_mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    // 使用快速模运算计算旋转因子
    unsigned int w = 1;
    for(int i = 0; i < local_id; i++) {
        w = fast_mod.mul(w, wn);
    }

    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = fast_mod.mul((unsigned int)data[base + local_id + half_len], w);

    data[base + local_id] = (int)fast_mod.add(u, v);
    data[base + local_id + half_len] = (int)fast_mod.sub(u, v);
}

// CUDA Kernel: 使用共享内存优化的NTT (修复版本)
__global__ void ntt_fast_shared_kernel(int *data, int len, unsigned int wn,
                                      FastModParams fast_mod, int n) {
    extern __shared__ int shared_data[];

    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    // 修复：正确的共享内存索引映射
    int tid = threadIdx.x;
    int elements_per_block = min(len, blockDim.x);

    // 每个线程负责加载对应的数据元素
    if(local_id < half_len && tid < elements_per_block) {
        if(base + local_id < n) {
            shared_data[local_id] = data[base + local_id];
        }
        if(base + local_id + half_len < n) {
            shared_data[local_id + half_len] = data[base + local_id + half_len];
        }
    }
    __syncthreads();

    // 计算旋转因子
    unsigned int w = 1;
    for(int i = 0; i < local_id; i++) {
        w = fast_mod.mul(w, wn);
    }

    // 蝶形运算
    if(local_id < half_len && base + local_id + half_len < n) {
        unsigned int u = (unsigned int)shared_data[local_id];
        unsigned int v = fast_mod.mul((unsigned int)shared_data[local_id + half_len], w);

        shared_data[local_id] = (int)fast_mod.add(u, v);
        shared_data[local_id + half_len] = (int)fast_mod.sub(u, v);
    }

    __syncthreads();

    // 写回全局内存
    if(local_id < half_len && tid < elements_per_block) {
        if(base + local_id < n) {
            data[base + local_id] = shared_data[local_id];
        }
        if(base + local_id + half_len < n) {
            data[base + local_id + half_len] = shared_data[local_id + half_len];
        }
    }
}

// CUDA Kernel: 数组缩放
__global__ void scale_fast_kernel(int *data, unsigned int inv_n, FastModParams fast_mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)fast_mod.mul((unsigned int)data[idx], inv_n);
    }
}

// CUDA Kernel: 多流并行点乘
__global__ void pointwise_mul_fast_kernel(int *a, const int *b, FastModParams fast_mod, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        a[idx] = (int)fast_mod.mul((unsigned int)a[idx], (unsigned int)b[idx]);
    }
}

// 多流并行NTT实现
void cuda_ntt_fast_multistream(int *h_data, int n, bool inverse, int p,
                              MultiStreamManager& stream_mgr, const AdaptiveGPUConfig& config) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));

    FastModParams fast_mod((unsigned int)p);

    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }

    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }

    // 使用流进行异步内存传输
    cudaStream_t stream = stream_mgr.get_next_stream();
    CHECK_CUDA(cudaMemcpyAsync(d_data, h_data, n * sizeof(int),
                              cudaMemcpyHostToDevice, stream));
    CHECK_CUDA(cudaMemcpyAsync(d_rev, rev.data(), n * sizeof(int),
                              cudaMemcpyHostToDevice, stream));

    // 位反转置换
    int block_size = config.get_optimal_block_size(n);
    int threads = std::min(block_size, n);
    int blocks = (n + threads - 1) / threads;

    bit_reverse_fast_kernel<<<blocks, threads, 0, stream>>>(d_data, d_rev, n);

    // NTT主循环 - 使用多流并行
    bool use_shared = config.should_use_shared_memory(n);

    for(int len = 2; len <= n; len <<= 1) {
        unsigned int wn = (unsigned int)qpow(3, (p-1)/len, p);
        if(inverse) wn = (unsigned int)qpow(wn, p-2, p);

        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = std::min(block_size, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;

        if(use_shared && len <= 1024) {
            // 使用共享内存优化 - 修复线程配置
            int shared_size = len * sizeof(int);
            int shared_threads = min(half_len, 512); // 限制线程数
            int shared_blocks = (total_butterflies + shared_threads - 1) / shared_threads;
            ntt_fast_shared_kernel<<<shared_blocks, shared_threads, shared_size, stream>>>
                (d_data, len, wn, fast_mod, n);
        } else {
            // 标准实现
            ntt_fast_radix2_kernel<<<blocks, threads, 0, stream>>>
                (d_data, len, wn, fast_mod, n);
        }
    }

    // 逆变换的最终缩放
    if(inverse) {
        unsigned int inv_n = (unsigned int)qpow(n, p-2, p);
        threads = std::min(block_size, n);
        blocks = (n + threads - 1) / threads;
        scale_fast_kernel<<<blocks, threads, 0, stream>>>(d_data, inv_n, fast_mod, n);
    }

    // 异步内存传输回主机
    CHECK_CUDA(cudaMemcpyAsync(h_data, d_data, n * sizeof(int),
                              cudaMemcpyDeviceToHost, stream));
    CHECK_CUDA(cudaStreamSynchronize(stream));

    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// 多流并行多项式乘法
void cuda_poly_multiply_fast_multistream(int *a, int *b, int *result, int n, int p,
                                        MultiStreamManager& stream_mgr,
                                        const AdaptiveGPUConfig& config) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;

    // 分配GPU内存
    int *d_A, *d_B;
    CHECK_CUDA(cudaMalloc(&d_A, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_B, lim * sizeof(int)));

    // 准备数据
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }

    // 使用不同流进行并行传输
    cudaStream_t stream_a = stream_mgr.get_next_stream();
    cudaStream_t stream_b = stream_mgr.get_next_stream();

    CHECK_CUDA(cudaMemcpyAsync(d_A, A.data(), lim * sizeof(int),
                              cudaMemcpyHostToDevice, stream_a));
    CHECK_CUDA(cudaMemcpyAsync(d_B, B.data(), lim * sizeof(int),
                              cudaMemcpyHostToDevice, stream_b));

    // 等待数据传输完成
    CHECK_CUDA(cudaStreamSynchronize(stream_a));
    CHECK_CUDA(cudaStreamSynchronize(stream_b));

    // 并行正变换
    auto start_fft = std::chrono::high_resolution_clock::now();

    // 创建临时主机数组用于NTT
    std::vector<int> temp_A(lim), temp_B(lim);
    CHECK_CUDA(cudaMemcpy(temp_A.data(), d_A, lim * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaMemcpy(temp_B.data(), d_B, lim * sizeof(int), cudaMemcpyDeviceToHost));

    // 使用多流并行NTT
    cuda_ntt_fast_multistream(temp_A.data(), lim, false, p, stream_mgr, config);
    cuda_ntt_fast_multistream(temp_B.data(), lim, false, p, stream_mgr, config);

    auto end_fft = std::chrono::high_resolution_clock::now();

    // 传输回GPU进行点乘
    CHECK_CUDA(cudaMemcpy(d_A, temp_A.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_B, temp_B.data(), lim * sizeof(int), cudaMemcpyHostToDevice));

    // 点乘
    FastModParams fast_mod((unsigned int)p);
    int block_size = config.get_optimal_block_size(lim);
    int threads = std::min(block_size, lim);
    int blocks = (lim + threads - 1) / threads;

    cudaStream_t mul_stream = stream_mgr.get_next_stream();
    pointwise_mul_fast_kernel<<<blocks, threads, 0, mul_stream>>>(d_A, d_B, fast_mod, lim);
    CHECK_CUDA(cudaStreamSynchronize(mul_stream));

    // 逆变换
    auto start_ifft = std::chrono::high_resolution_clock::now();
    CHECK_CUDA(cudaMemcpy(temp_A.data(), d_A, lim * sizeof(int), cudaMemcpyDeviceToHost));
    cuda_ntt_fast_multistream(temp_A.data(), lim, true, p, stream_mgr, config);
    auto end_ifft = std::chrono::high_resolution_clock::now();

    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = temp_A[i];
    }

    // 性能统计
    double fft_time = std::chrono::duration<double, std::milli>(end_fft - start_fft).count();
    double ifft_time = std::chrono::duration<double, std::milli>(end_ifft - start_ifft).count();
    printf("  FFT时间: %.3f ms, IFFT时间: %.3f ms\n", fft_time, ifft_time);

    CHECK_CUDA(cudaFree(d_A));
    CHECK_CUDA(cudaFree(d_B));
}

// 性能基准测试
void benchmark_lemire_vs_standard(int n, int p, const AdaptiveGPUConfig& config) {
    printf("\n🔬 Lemire Reduction vs 标准模运算性能对比 (n=%d)\n", n);
    printf("================================================================\n");

    // 生成测试数据
    std::vector<int> a(n), b(n), result_lemire(2*n-1), result_standard(2*n-1);
    for(int i = 0; i < n; i++) {
        a[i] = rand() % p;
        b[i] = rand() % p;
    }

    MultiStreamManager stream_mgr(config.get_optimal_streams(n));

    // 快速模运算测试
    auto start = std::chrono::high_resolution_clock::now();
    cuda_poly_multiply_fast_multistream(a.data(), b.data(), result_lemire.data(),
                                       n, p, stream_mgr, config);
    auto end = std::chrono::high_resolution_clock::now();
    double fast_time = std::chrono::duration<double, std::milli>(end - start).count();

    printf("快速模运算 + 多流: %.3f ms\n", fast_time);
    printf("使用流数量: %d\n", stream_mgr.num_streams);
    printf("块大小: %d\n", config.get_optimal_block_size(n));
    printf("共享内存优化: %s\n", config.should_use_shared_memory(n) ? "是" : "否");

    // 内存带宽利用率估算
    size_t data_size = n * sizeof(int) * 4; // 两个输入数组 + 中间结果
    double bandwidth_gbps = (data_size / (1024.0 * 1024.0 * 1024.0)) / (fast_time / 1000.0);
    printf("估算内存带宽利用率: %.2f GB/s\n", bandwidth_gbps);

    printf("================================================================\n");
}

// GPU内存使用情况分析
void analyze_memory_usage(const AdaptiveGPUConfig& config) {
    size_t free_mem, total_mem;
    CHECK_CUDA(cudaMemGetInfo(&free_mem, &total_mem));

    printf("\n💾 GPU内存使用分析\n");
    printf("================================================================\n");
    printf("总内存: %.2f GB\n", total_mem / (1024.0 * 1024.0 * 1024.0));
    printf("可用内存: %.2f GB\n", free_mem / (1024.0 * 1024.0 * 1024.0));
    printf("已用内存: %.2f GB (%.1f%%)\n",
           (total_mem - free_mem) / (1024.0 * 1024.0 * 1024.0),
           100.0 * (total_mem - free_mem) / total_mem);

    // 计算最大可处理的NTT大小
    size_t available_for_ntt = free_mem * 0.8; // 保留20%缓冲
    int max_n = (int)sqrt(available_for_ntt / (sizeof(int) * 6)); // 估算
    printf("估算最大NTT大小: 2^%d (%d)\n", (int)log2(max_n), max_n);
    printf("================================================================\n");
}

int main() {
    printf("🚀 CUDA Lemire Reduction + 多流并行 NTT 优化测试\n");
    printf("================================================================\n");

    // 检查CUDA设备
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }

    // 获取GPU配置
    AdaptiveGPUConfig config;
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));

    printf("GPU: %s\n", prop.name);
    printf("计算能力: %d.%d\n", prop.major, prop.minor);
    printf("SM数量: %d\n", config.sm_count);
    printf("最大线程数每块: %d\n", config.max_threads_per_block);
    printf("全局内存: %.1f GB\n", config.global_memory / (1024.0 * 1024.0 * 1024.0));
    printf("Tensor Core支持: %s\n", config.has_tensor_cores ? "是" : "否");
    printf("================================================================\n");

    // 分析内存使用情况
    analyze_memory_usage(config);

    // 运行标准测试用例
    int a[300000], b[300000], ab[300000];

    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));

        printf("\n📊 测试 %d: n=%d, p=%d\n", test_id, n, p);
        printf("----------------------------------------\n");

        MultiStreamManager stream_mgr(config.get_optimal_streams(n));

        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_fast_multistream(a, b, ab, n, p, stream_mgr, config);
        auto end = std::chrono::high_resolution_clock::now();

        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("总执行时间: %.3f ms\n", time_ms);

        // 计算性能指标
        int lim = 1;
        while(lim < 2 * n) lim <<= 1;
        double flops = lim * log2(lim) * 5; // 估算浮点运算数
        printf("估算性能: %.2f GFLOPS\n", flops / (time_ms * 1e6));
    }

    // 性能基准测试
    std::vector<int> test_sizes = {1024, 4096, 16384, 65536};
    for(int size : test_sizes) {
        if(size <= 65536) { // 避免内存不足
            benchmark_lemire_vs_standard(size, 998244353, config);
        }
    }

    printf("\n✅ 所有测试完成!\n");
    return 0;
}
