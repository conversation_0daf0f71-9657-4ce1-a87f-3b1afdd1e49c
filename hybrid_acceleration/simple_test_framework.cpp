/**
 * @file simple_test_framework.cpp
 * @brief 简化的NTT并行优化测试框架
 */

#include <iostream>
#include <vector>
#include <chrono>
#include <memory>
#include <string>
#include <iomanip>
#include <cstdlib>

// 简化的硬件检测器
class SimpleHardwareDetector {
public:
    struct CPUInfo {
        int coreCount = 8;
        bool hasAVX = true;
        bool hasAVX2 = true;
        int cacheSize = 8192;
    };
    
    struct GPUInfo {
        bool available = false;
        int deviceCount = 0;
        std::string deviceName = "None";
    };
    
    CPUInfo getCPUInfo() const { return cpuInfo_; }
    GPUInfo getGPUInfo() const { return gpuInfo_; }
    
private:
    CPUInfo cpuInfo_;
    GPUInfo gpuInfo_;
};

// 简化的策略枚举
enum class SimpleParallelStrategy {
    SERIAL,
    OPENMP_PARALLEL,
    SIMD_VECTORIZED,
    OPENMP_SIMD
};

enum class SimpleNTTVariant {
    RADIX_2_DIT,
    RADIX_2_DIF,
    RADIX_4_DIT
};

// 简化的NTT引擎
class SimpleNTTEngine {
public:
    SimpleNTTEngine() : hardwareDetector_(std::make_shared<SimpleHardwareDetector>()) {}
    
    bool computeNTT(std::vector<int>& data, int modulus, bool inverse = false,
                   SimpleParallelStrategy strategy = SimpleParallelStrategy::OPENMP_PARALLEL,
                   SimpleNTTVariant variant = SimpleNTTVariant::RADIX_2_DIT) {
        
        int n = data.size();
        if (n <= 1) return true;
        
        // 检查是否为2的幂
        if ((n & (n - 1)) != 0) {
            std::cerr << "错误: 数据长度必须是2的幂" << std::endl;
            return false;
        }
        
        try {
            switch (strategy) {
                case SimpleParallelStrategy::SERIAL:
                    return serialNTT(data, modulus, inverse);
                case SimpleParallelStrategy::OPENMP_PARALLEL:
                    return openmpNTT(data, modulus, inverse);
                case SimpleParallelStrategy::SIMD_VECTORIZED:
                    return simdNTT(data, modulus, inverse);
                case SimpleParallelStrategy::OPENMP_SIMD:
                    return hybridNTT(data, modulus, inverse);
                default:
                    return serialNTT(data, modulus, inverse);
            }
        } catch (const std::exception& e) {
            std::cerr << "NTT计算错误: " << e.what() << std::endl;
            return false;
        }
    }
    
private:
    std::shared_ptr<SimpleHardwareDetector> hardwareDetector_;
    
    // 串行NTT实现
    bool serialNTT(std::vector<int>& data, int modulus, bool inverse) {
        int n = data.size();
        
        // 位反转置换
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // NTT计算
        for (int len = 2; len <= n; len <<= 1) {
            long long wlen = inverse ? modInverse(primitiveRoot(modulus), modulus) : primitiveRoot(modulus);
            for (int i = len; i < modulus - 1; i <<= 1) {
                wlen = (wlen * wlen) % modulus;
            }
            
            for (int i = 0; i < n; i += len) {
                long long w = 1;
                for (int j = 0; j < len / 2; j++) {
                    long long u = data[i + j];
                    long long v = (data[i + j + len / 2] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (w * wlen) % modulus;
                }
            }
        }
        
        if (inverse) {
            long long n_inv = modInverse(n, modulus);
            for (int& x : data) {
                x = (x * n_inv) % modulus;
            }
        }
        
        return true;
    }
    
    // OpenMP并行NTT
    bool openmpNTT(std::vector<int>& data, int modulus, bool inverse) {
        int n = data.size();
        
        // 位反转置换 (并行)
        #pragma omp parallel for
        for (int i = 1; i < n; i++) {
            int j = 0;
            int bit = n >> 1;
            int temp_i = i;
            while (bit != 0) {
                if (temp_i & 1) j |= bit;
                temp_i >>= 1;
                bit >>= 1;
            }
            if (i < j) {
                #pragma omp critical
                {
                    std::swap(data[i], data[j]);
                }
            }
        }
        
        // NTT计算 (并行)
        for (int len = 2; len <= n; len <<= 1) {
            long long wlen = inverse ? modInverse(primitiveRoot(modulus), modulus) : primitiveRoot(modulus);
            for (int i = len; i < modulus - 1; i <<= 1) {
                wlen = (wlen * wlen) % modulus;
            }
            
            #pragma omp parallel for
            for (int i = 0; i < n; i += len) {
                long long w = 1;
                for (int j = 0; j < len / 2; j++) {
                    long long u = data[i + j];
                    long long v = (data[i + j + len / 2] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (w * wlen) % modulus;
                }
            }
        }
        
        if (inverse) {
            long long n_inv = modInverse(n, modulus);
            #pragma omp parallel for
            for (int i = 0; i < n; ++i) {
                data[i] = (data[i] * n_inv) % modulus;
            }
        }
        
        return true;
    }
    
    // SIMD向量化NTT (简化版本)
    bool simdNTT(std::vector<int>& data, int modulus, bool inverse) {
        // 对于简化版本，直接调用串行版本
        return serialNTT(data, modulus, inverse);
    }
    
    // 混合并行NTT
    bool hybridNTT(std::vector<int>& data, int modulus, bool inverse) {
        // 对于简化版本，直接调用OpenMP版本
        return openmpNTT(data, modulus, inverse);
    }
    
    // 辅助函数
    long long primitiveRoot(int modulus) {
        // 简化的原根查找，对于998244353返回3
        if (modulus == 998244353) return 3;
        return 3; // 默认值
    }
    
    long long modInverse(long long a, long long m) {
        // 扩展欧几里得算法求模逆
        long long m0 = m, x0 = 0, x1 = 1;
        if (m == 1) return 0;
        while (a > 1) {
            long long q = a / m;
            long long t = m;
            m = a % m;
            a = t;
            t = x0;
            x0 = x1 - q * x0;
            x1 = t;
        }
        if (x1 < 0) x1 += m0;
        return x1;
    }
};

// 简化的测试框架
class SimpleTestFramework {
public:
    SimpleTestFramework() : nttEngine_(std::make_unique<SimpleNTTEngine>()) {}
    
    void runAllTests() {
        std::cout << "=== 简化NTT并行优化测试框架 ===" << std::endl;
        std::cout << "版本: 1.0 - 基础验证版" << std::endl << std::endl;
        
        int totalTests = 0;
        int passedTests = 0;
        
        // 测试不同策略
        std::vector<SimpleParallelStrategy> strategies = {
            SimpleParallelStrategy::SERIAL,
            SimpleParallelStrategy::OPENMP_PARALLEL,
            SimpleParallelStrategy::SIMD_VECTORIZED,
            SimpleParallelStrategy::OPENMP_SIMD
        };
        
        std::vector<std::string> strategyNames = {
            "串行",
            "OpenMP并行",
            "SIMD向量化",
            "OpenMP+SIMD混合"
        };
        
        std::vector<int> testSizes = {64, 256, 1024, 4096};
        int modulus = 998244353;
        
        for (size_t s = 0; s < strategies.size(); ++s) {
            std::cout << "测试策略: " << strategyNames[s] << std::endl;
            
            for (int size : testSizes) {
                totalTests++;
                
                // 生成测试数据
                std::vector<int> data(size);
                for (int i = 0; i < size; ++i) {
                    data[i] = rand() % modulus;
                }
                
                std::vector<int> original = data;
                
                // 测试正变换 + 逆变换
                auto start = std::chrono::high_resolution_clock::now();
                
                bool success1 = nttEngine_->computeNTT(data, modulus, false, strategies[s]);
                bool success2 = nttEngine_->computeNTT(data, modulus, true, strategies[s]);
                
                auto end = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration<double>(end - start).count();
                
                // 验证结果
                bool correct = success1 && success2;
                if (correct) {
                    for (int i = 0; i < size; ++i) {
                        if (data[i] != original[i]) {
                            correct = false;
                            break;
                        }
                    }
                }
                
                if (correct) {
                    passedTests++;
                    std::cout << "  ✓ 大小 " << size << ": 通过 (" 
                             << std::fixed << std::setprecision(6) << duration << "s)" << std::endl;
                } else {
                    std::cout << "  ✗ 大小 " << size << ": 失败" << std::endl;
                }
            }
            std::cout << std::endl;
        }
        
        // 输出总结
        std::cout << "=== 测试总结 ===" << std::endl;
        std::cout << "总测试数: " << totalTests << std::endl;
        std::cout << "通过测试: " << passedTests << std::endl;
        std::cout << "失败测试: " << (totalTests - passedTests) << std::endl;
        std::cout << "成功率: " << std::fixed << std::setprecision(1) 
                 << (100.0 * passedTests / totalTests) << "%" << std::endl;
    }
    
private:
    std::unique_ptr<SimpleNTTEngine> nttEngine_;
};

int main() {
    try {
        SimpleTestFramework framework;
        framework.runAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试框架错误: " << e.what() << std::endl;
        return 1;
    }
}
