/**
 * @file large_scale_test_framework.cpp
 * @brief 大规模NTT测试框架，支持多模数和大规模问题测试
 */

#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <iomanip>
#include <fstream>
#include <cassert>
#include <omp.h>

class LargeScaleNTTTester {
private:
    // 测试配置
    std::vector<int> test_sizes_ = {8192, 16384, 32768, 65536};
    std::vector<uint32_t> test_moduli_ = {998244353, 1004535809, 469762049, 104857601};
    
    // 性能数据存储
    struct PerformanceData {
        int size;
        uint32_t modulus;
        std::string strategy;
        double time_ms;
        double throughput_mbps;
        bool correctness;
    };
    
    std::vector<PerformanceData> results_;
    
public:
    void runComprehensiveTests() {
        std::cout << "=== 大规模NTT并行优化测试框架 ===" << std::endl;
        std::cout << "测试规模: 8192 - 65536" << std::endl;
        std::cout << "测试模数: 4个不同模数" << std::endl;
        std::cout << "并行策略: 4种策略" << std::endl << std::endl;
        
        for (uint32_t modulus : test_moduli_) {
            std::cout << "测试模数: " << modulus << std::endl;
            
            for (int size : test_sizes_) {
                std::cout << "  测试规模: " << size << std::endl;
                
                // 生成测试数据
                auto test_data = generateTestData(size, modulus);
                
                // 测试所有策略
                testStrategy("串行", test_data, modulus, false, false);
                testStrategy("OpenMP并行", test_data, modulus, true, false);
                testStrategy("SIMD向量化", test_data, modulus, false, true);
                testStrategy("OpenMP+SIMD混合", test_data, modulus, true, true);
            }
            std::cout << std::endl;
        }
        
        // 生成性能报告
        generatePerformanceReport();
        exportResultsToCSV();
    }

private:
    std::vector<uint32_t> generateTestData(int size, uint32_t modulus) {
        std::vector<uint32_t> data(size);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint32_t> dis(0, modulus - 1);
        
        for (int i = 0; i < size; i++) {
            data[i] = dis(gen);
        }
        
        return data;
    }
    
    void testStrategy(const std::string& strategy_name, 
                     std::vector<uint32_t> data, 
                     uint32_t modulus,
                     bool use_openmp, 
                     bool use_simd) {
        
        auto original_data = data;
        
        // 执行NTT
        auto start = std::chrono::high_resolution_clock::now();
        
        if (use_openmp && use_simd) {
            hybridNTT(data, modulus);
        } else if (use_openmp) {
            openmpNTT(data, modulus);
        } else if (use_simd) {
            simdNTT(data, modulus);
        } else {
            serialNTT(data, modulus);
        }
        
        auto mid = std::chrono::high_resolution_clock::now();
        
        // 执行INTT验证正确性
        inverseNTT(data, modulus, use_openmp, use_simd);
        
        auto end = std::chrono::high_resolution_clock::now();
        
        // 计算性能指标
        double ntt_time = std::chrono::duration<double, std::milli>(mid - start).count();
        
        // 验证正确性
        bool correct = verifyCorrectness(original_data, data, modulus);
        
        // 计算吞吐量 (MB/s)
        double data_size_mb = (data.size() * sizeof(uint32_t)) / (1024.0 * 1024.0);
        double throughput = data_size_mb / (ntt_time / 1000.0);
        
        // 存储结果
        results_.push_back({
            static_cast<int>(data.size()),
            modulus,
            strategy_name,
            ntt_time,
            throughput,
            correct
        });
        
        // 输出结果
        std::cout << "    " << strategy_name << ": ";
        if (correct) {
            std::cout << "✓ 通过";
        } else {
            std::cout << "✗ 失败";
        }
        std::cout << " (" << std::fixed << std::setprecision(3) << ntt_time << "ms, "
                  << std::setprecision(1) << throughput << " MB/s)" << std::endl;
    }
    
    // 快速幂运算
    uint32_t fastPow(uint32_t base, uint32_t exp, uint32_t mod) {
        uint32_t result = 1;
        base %= mod;
        while (exp > 0) {
            if (exp & 1) result = (1ULL * result * base) % mod;
            base = (1ULL * base * base) % mod;
            exp >>= 1;
        }
        return result;
    }
    
    // 模逆元
    uint32_t modularInverse(uint32_t a, uint32_t mod) {
        return fastPow(a, mod - 2, mod);
    }
    
    // 串行NTT实现
    void serialNTT(std::vector<uint32_t>& data, uint32_t modulus) {
        int n = data.size();
        
        // 位逆序排列
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            uint32_t wlen = fastPow(3, (modulus - 1) / len, modulus);
            for (int i = 0; i < n; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < len / 2; ++j) {
                    uint32_t u = data[i + j];
                    uint32_t v = (1ULL * data[i + j + len / 2] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (1ULL * w * wlen) % modulus;
                }
            }
        }
    }
    
    // OpenMP并行NTT实现
    void openmpNTT(std::vector<uint32_t>& data, uint32_t modulus) {
        int n = data.size();
        
        // 串行位逆序排列（避免竞争条件）
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // 并行NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            uint32_t wlen = fastPow(3, (modulus - 1) / len, modulus);
            
            #pragma omp parallel for
            for (int i = 0; i < n; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < len / 2; ++j) {
                    uint32_t u = data[i + j];
                    uint32_t v = (1ULL * data[i + j + len / 2] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (1ULL * w * wlen) % modulus;
                }
            }
        }
    }
    
    // SIMD向量化NTT实现
    void simdNTT(std::vector<uint32_t>& data, uint32_t modulus) {
        int n = data.size();
        
        // 位逆序排列
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // SIMD优化的NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            uint32_t wlen = fastPow(3, (modulus - 1) / len, modulus);
            for (int i = 0; i < n; i += len) {
                uint32_t w = 1;
                int half = len / 2;
                
                // SIMD处理（4个元素一组）
                int j = 0;
                for (; j + 3 < half; j += 4) {
                    for (int k = 0; k < 4; k++) {
                        uint32_t u = data[i + j + k];
                        uint32_t v = (1ULL * data[i + j + k + half] * w) % modulus;
                        data[i + j + k] = (u + v) % modulus;
                        data[i + j + k + half] = (u - v + modulus) % modulus;
                        w = (1ULL * w * wlen) % modulus;
                    }
                }
                
                // 处理剩余元素
                for (; j < half; j++) {
                    uint32_t u = data[i + j];
                    uint32_t v = (1ULL * data[i + j + half] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + half] = (u - v + modulus) % modulus;
                    w = (1ULL * w * wlen) % modulus;
                }
            }
        }
    }
    
    // 混合并行NTT实现
    void hybridNTT(std::vector<uint32_t>& data, uint32_t modulus) {
        int n = data.size();
        
        // 串行位逆序排列
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // 混合并行NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            uint32_t wlen = fastPow(3, (modulus - 1) / len, modulus);
            
            #pragma omp parallel for
            for (int i = 0; i < n; i += len) {
                uint32_t w = 1;
                int half = len / 2;
                
                // 内层SIMD优化
                int j = 0;
                for (; j + 3 < half; j += 4) {
                    for (int k = 0; k < 4; k++) {
                        uint32_t u = data[i + j + k];
                        uint32_t v = (1ULL * data[i + j + k + half] * w) % modulus;
                        data[i + j + k] = (u + v) % modulus;
                        data[i + j + k + half] = (u - v + modulus) % modulus;
                        w = (1ULL * w * wlen) % modulus;
                    }
                }
                
                // 处理剩余元素
                for (; j < half; j++) {
                    uint32_t u = data[i + j];
                    uint32_t v = (1ULL * data[i + j + half] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + half] = (u - v + modulus) % modulus;
                    w = (1ULL * w * wlen) % modulus;
                }
            }
        }
    }
    
    // 逆NTT实现
    void inverseNTT(std::vector<uint32_t>& data, uint32_t modulus, bool use_openmp, bool use_simd) {
        int n = data.size();
        
        // INTT主循环（逆序）
        for (int len = n; len >= 2; len >>= 1) {
            uint32_t wlen = modularInverse(fastPow(3, (modulus - 1) / len, modulus), modulus);
            
            if (use_openmp) {
                #pragma omp parallel for
                for (int i = 0; i < n; i += len) {
                    uint32_t w = 1;
                    for (int j = 0; j < len / 2; ++j) {
                        uint32_t u = data[i + j];
                        uint32_t v = data[i + j + len / 2];
                        data[i + j] = (u + v) % modulus;
                        data[i + j + len / 2] = (1ULL * (u - v + modulus) * w) % modulus;
                        w = (1ULL * w * wlen) % modulus;
                    }
                }
            } else {
                for (int i = 0; i < n; i += len) {
                    uint32_t w = 1;
                    for (int j = 0; j < len / 2; ++j) {
                        uint32_t u = data[i + j];
                        uint32_t v = data[i + j + len / 2];
                        data[i + j] = (u + v) % modulus;
                        data[i + j + len / 2] = (1ULL * (u - v + modulus) * w) % modulus;
                        w = (1ULL * w * wlen) % modulus;
                    }
                }
            }
        }
        
        // 位逆序排列
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(data[i], data[j]);
            }
        }
        
        // 除以n
        uint32_t inv_n = modularInverse(n, modulus);
        if (use_openmp) {
            #pragma omp parallel for
            for (int i = 0; i < n; i++) {
                data[i] = (1ULL * data[i] * inv_n) % modulus;
            }
        } else {
            for (int i = 0; i < n; i++) {
                data[i] = (1ULL * data[i] * inv_n) % modulus;
            }
        }
    }
    
    // 验证正确性
    bool verifyCorrectness(const std::vector<uint32_t>& original, 
                          const std::vector<uint32_t>& result, 
                          uint32_t modulus) {
        if (original.size() != result.size()) return false;
        
        int errors = 0;
        for (size_t i = 0; i < original.size(); i++) {
            if (original[i] != result[i]) {
                errors++;
                if (errors > 10) break; // 限制错误输出
            }
        }
        
        return errors == 0;
    }
    
    // 生成性能报告
    void generatePerformanceReport() {
        std::cout << "\n=== 大规模测试性能报告 ===" << std::endl;
        
        // 按模数分组统计
        for (uint32_t modulus : test_moduli_) {
            std::cout << "\n模数 " << modulus << " 性能统计:" << std::endl;
            std::cout << std::setw(12) << "规模" 
                      << std::setw(15) << "串行(ms)" 
                      << std::setw(15) << "OpenMP(ms)" 
                      << std::setw(15) << "SIMD(ms)" 
                      << std::setw(15) << "混合(ms)" 
                      << std::setw(15) << "最佳加速比" << std::endl;
            std::cout << std::string(87, '-') << std::endl;
            
            for (int size : test_sizes_) {
                double serial_time = 0, openmp_time = 0, simd_time = 0, hybrid_time = 0;
                bool all_correct = true;
                
                // 查找对应的结果
                for (const auto& result : results_) {
                    if (result.modulus == modulus && result.size == size) {
                        if (result.strategy == "串行") serial_time = result.time_ms;
                        else if (result.strategy == "OpenMP并行") openmp_time = result.time_ms;
                        else if (result.strategy == "SIMD向量化") simd_time = result.time_ms;
                        else if (result.strategy == "OpenMP+SIMD混合") hybrid_time = result.time_ms;
                        
                        if (!result.correctness) all_correct = false;
                    }
                }
                
                // 计算最佳加速比
                double best_time = std::min(std::min(openmp_time, simd_time), hybrid_time);
                double speedup = (serial_time > 0 && best_time > 0) ? serial_time / best_time : 1.0;
                
                std::cout << std::setw(12) << size
                          << std::setw(15) << std::fixed << std::setprecision(3) << serial_time
                          << std::setw(15) << openmp_time
                          << std::setw(15) << simd_time
                          << std::setw(15) << hybrid_time
                          << std::setw(15) << std::setprecision(2) << speedup << "x";
                
                if (!all_correct) {
                    std::cout << " ⚠️";
                }
                std::cout << std::endl;
            }
        }
        
        // 总体统计
        std::cout << "\n=== 总体统计 ===" << std::endl;
        int total_tests = results_.size();
        int passed_tests = 0;
        
        for (const auto& result : results_) {
            if (result.correctness) passed_tests++;
        }
        
        std::cout << "总测试数: " << total_tests << std::endl;
        std::cout << "通过测试: " << passed_tests << std::endl;
        std::cout << "成功率: " << std::fixed << std::setprecision(1) 
                  << (100.0 * passed_tests / total_tests) << "%" << std::endl;
    }
    
    // 导出结果到CSV
    void exportResultsToCSV() {
        std::ofstream csv_file("large_scale_performance_results.csv");
        
        csv_file << "Size,Modulus,Strategy,Time_ms,Throughput_MBps,Correctness\n";
        
        for (const auto& result : results_) {
            csv_file << result.size << ","
                     << result.modulus << ","
                     << result.strategy << ","
                     << std::fixed << std::setprecision(6) << result.time_ms << ","
                     << std::setprecision(2) << result.throughput_mbps << ","
                     << (result.correctness ? "PASS" : "FAIL") << "\n";
        }
        
        csv_file.close();
        std::cout << "\n性能数据已导出到: large_scale_performance_results.csv" << std::endl;
    }
};

int main() {
    LargeScaleNTTTester tester;
    tester.runComprehensiveTests();
    return 0;
}
