#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <iomanip>
#include <string>
#include <cmath>
#include <algorithm>
#include <random>
#include <map>
#include <omp.h>

// 包含我们的混合NTT引擎
#include "src/core/hardware_detector.hpp"
#include "src/core/strategy_selector.hpp"
#include "src/algorithms/hybrid_ntt_engine.hpp"

using namespace HybridNTT;

/**
 * @brief 性能对比测试套件
 */
class PerformanceComparison {
private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;
    std::shared_ptr<HybridNTTEngine> nttEngine_;
    
    struct BenchmarkResult {
        std::string implementation;
        int problemSize;
        double avgTime;
        double minTime;
        double maxTime;
        double throughput;
        double speedup;
        std::string strategy;
    };
    
    std::vector<BenchmarkResult> results_;

public:
    PerformanceComparison() {
        hardwareDetector_ = std::make_shared<HardwareDetector>();
        hardwareDetector_->detectAll(); // 执行硬件检测
        strategySelector_ = std::make_shared<StrategySelector>(hardwareDetector_);
        nttEngine_ = std::make_shared<HybridNTTEngine>(hardwareDetector_, strategySelector_);
    }

    /**
     * @brief 简单的串行NTT实现（作为基准）
     */
    void simpleNTT(std::vector<int>& a, int n, int modulus, bool inverse) {
        // 位反转
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(a[i], a[j]);
            }
        }

        // NTT主循环
        for (int len = 2; len <= n; len <<= 1) {
            int wlen = inverse ? fastPow(3, modulus - 1 - (modulus - 1) / len, modulus) 
                              : fastPow(3, (modulus - 1) / len, modulus);
            for (int i = 0; i < n; i += len) {
                int w = 1;
                for (int j = 0; j < len / 2; j++) {
                    int u = a[i + j];
                    int v = (1LL * a[i + j + len / 2] * w) % modulus;
                    a[i + j] = (u + v) % modulus;
                    a[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (1LL * w * wlen) % modulus;
                }
            }
        }

        // 逆变换需要除以n
        if (inverse) {
            int inv_n = fastPow(n, modulus - 2, modulus);
            for (int i = 0; i < n; i++) {
                a[i] = (1LL * a[i] * inv_n) % modulus;
            }
        }
    }

    /**
     * @brief OpenMP并行NTT实现
     */
    void openmpNTT(std::vector<int>& a, int n, int modulus, bool inverse) {
        // 位反转
        for (int i = 1, j = 0; i < n; i++) {
            int bit = n >> 1;
            for (; j & bit; bit >>= 1) {
                j ^= bit;
            }
            j ^= bit;
            if (i < j) {
                std::swap(a[i], a[j]);
            }
        }

        // NTT主循环（并行化）
        for (int len = 2; len <= n; len <<= 1) {
            int wlen = inverse ? fastPow(3, modulus - 1 - (modulus - 1) / len, modulus) 
                              : fastPow(3, (modulus - 1) / len, modulus);
            
            #pragma omp parallel for
            for (int i = 0; i < n; i += len) {
                int w = 1;
                for (int j = 0; j < len / 2; j++) {
                    int u = a[i + j];
                    int v = (1LL * a[i + j + len / 2] * w) % modulus;
                    a[i + j] = (u + v) % modulus;
                    a[i + j + len / 2] = (u - v + modulus) % modulus;
                    w = (1LL * w * wlen) % modulus;
                }
            }
        }

        // 逆变换需要除以n
        if (inverse) {
            int inv_n = fastPow(n, modulus - 2, modulus);
            #pragma omp parallel for
            for (int i = 0; i < n; i++) {
                a[i] = (1LL * a[i] * inv_n) % modulus;
            }
        }
    }

    /**
     * @brief 快速幂运算
     */
    int fastPow(long long base, long long exp, int mod) {
        long long result = 1;
        while (exp > 0) {
            if (exp % 2 == 1) {
                result = (result * base) % mod;
            }
            base = (base * base) % mod;
            exp /= 2;
        }
        return result;
    }

    /**
     * @brief 多项式乘法基准测试
     */
    std::vector<int> polynomialMultiplyBaseline(const std::vector<int>& a, const std::vector<int>& b, 
                                               int modulus, const std::string& method) {
        int n = a.size();
        int resultSize = 1;
        while (resultSize < 2 * n - 1) {
            resultSize <<= 1;
        }
        
        std::vector<int> fa(resultSize, 0), fb(resultSize, 0);
        for (int i = 0; i < n; i++) {
            fa[i] = a[i];
            fb[i] = b[i];
        }
        
        if (method == "serial") {
            simpleNTT(fa, resultSize, modulus, false);
            simpleNTT(fb, resultSize, modulus, false);
        } else if (method == "openmp") {
            openmpNTT(fa, resultSize, modulus, false);
            openmpNTT(fb, resultSize, modulus, false);
        }
        
        // 点乘
        for (int i = 0; i < resultSize; i++) {
            fa[i] = (1LL * fa[i] * fb[i]) % modulus;
        }
        
        if (method == "serial") {
            simpleNTT(fa, resultSize, modulus, true);
        } else if (method == "openmp") {
            openmpNTT(fa, resultSize, modulus, true);
        }
        
        std::vector<int> result(2 * n - 1);
        for (int i = 0; i < 2 * n - 1; i++) {
            result[i] = fa[i];
        }
        
        return result;
    }

    /**
     * @brief 混合并行多项式乘法
     */
    std::vector<int> polynomialMultiplyHybrid(const std::vector<int>& a, const std::vector<int>& b, int modulus) {
        int n = a.size();
        int resultSize = 1;
        while (resultSize < 2 * n - 1) {
            resultSize <<= 1;
        }
        
        std::vector<int> fa(resultSize, 0), fb(resultSize, 0);
        for (int i = 0; i < n; i++) {
            fa[i] = a[i];
            fb[i] = b[i];
        }
        
        // 设置最优策略
        auto strategy = strategySelector_->selectOptimalStrategy(resultSize, modulus);
        nttEngine_->setStrategy(strategy);
        
        // 执行混合并行NTT
        nttEngine_->computeNTT(fa, resultSize, modulus);
        nttEngine_->computeNTT(fb, resultSize, modulus);
        
        // 点乘
        for (int i = 0; i < resultSize; i++) {
            fa[i] = (1LL * fa[i] * fb[i]) % modulus;
        }
        
        // 逆NTT
        nttEngine_->computeINTT(fa, resultSize, modulus);
        
        std::vector<int> result(2 * n - 1);
        for (int i = 0; i < 2 * n - 1; i++) {
            result[i] = fa[i];
        }
        
        return result;
    }

    /**
     * @brief 运行性能对比测试
     */
    void runComparisonBenchmark() {
        std::cout << "\n========================================" << std::endl;
        std::cout << "          性能对比基准测试" << std::endl;
        std::cout << "========================================" << std::endl;
        
        std::vector<int> testSizes = {256, 512, 1024, 2048, 4096, 8192, 16384};
        int modulus = 998244353;
        int iterations = 10;
        
        std::cout << std::setw(8) << "大小" << std::setw(12) << "实现" 
                  << std::setw(12) << "时间(μs)" << std::setw(12) << "加速比" 
                  << std::setw(15) << "吞吐量(MB/s)" << std::setw(12) << "策略" << std::endl;
        std::cout << std::string(71, '-') << std::endl;
        
        for (int size : testSizes) {
            // 生成随机测试数据
            std::vector<int> a(size), b(size);
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, modulus - 1);
            
            for (int i = 0; i < size; i++) {
                a[i] = dis(gen);
                b[i] = dis(gen);
            }
            
            // 测试不同实现
            std::vector<std::string> methods = {"serial", "openmp", "hybrid"};
            std::vector<std::string> methodNames = {"串行", "OpenMP", "混合并行"};
            double serialTime = 0.0;
            
            for (size_t methodIdx = 0; methodIdx < methods.size(); methodIdx++) {
                const std::string& method = methods[methodIdx];
                const std::string& methodName = methodNames[methodIdx];
                
                std::vector<double> times;
                std::string strategyName = "";
                
                for (int iter = 0; iter < iterations; iter++) {
                    auto testA = a, testB = b;
                    
                    auto startTime = std::chrono::high_resolution_clock::now();
                    
                    if (method == "hybrid") {
                        auto result = polynomialMultiplyHybrid(testA, testB, modulus);
                        if (iter == 0) {
                            auto strategy = strategySelector_->selectOptimalStrategy(size * 2, modulus);
                            strategyName = getStrategyName(strategy.parallelStrategy);
                        }
                    } else {
                        auto result = polynomialMultiplyBaseline(testA, testB, modulus, method);
                        if (method == "openmp") strategyName = "OpenMP";
                        else strategyName = "串行";
                    }
                    
                    auto endTime = std::chrono::high_resolution_clock::now();
                    double iterTime = std::chrono::duration<double, std::micro>(endTime - startTime).count();
                    times.push_back(iterTime);
                }
                
                double avgTime = std::accumulate(times.begin(), times.end(), 0.0) / times.size();
                if (method == "serial") serialTime = avgTime;
                
                double speedup = (serialTime > 0) ? serialTime / avgTime : 1.0;
                double throughput = (size * sizeof(int) * 2) / (avgTime / 1e6) / (1024 * 1024);
                
                std::cout << std::setw(8) << size 
                          << std::setw(12) << methodName
                          << std::setw(12) << std::fixed << std::setprecision(1) << avgTime
                          << std::setw(12) << std::fixed << std::setprecision(2) << speedup
                          << std::setw(15) << std::fixed << std::setprecision(1) << throughput
                          << std::setw(12) << strategyName << std::endl;
            }
            std::cout << std::string(71, '-') << std::endl;
        }
    }

private:
    std::string getStrategyName(ParallelStrategy strategy) {
        switch (strategy) {
            case ParallelStrategy::SERIAL: return "串行";
            case ParallelStrategy::OPENMP_SIMD: return "OpenMP+SIMD";
            case ParallelStrategy::MPI_OPENMP_SIMD: return "MPI+OpenMP+SIMD";
            case ParallelStrategy::HYBRID_ALL: return "混合全部";
            default: return "未知";
        }
    }
};

int main() {
    std::cout << "=====================================================" << std::endl;
    std::cout << "    混合并行NTT性能对比测试" << std::endl;
    std::cout << "=====================================================" << std::endl;
    
    try {
        PerformanceComparison comparison;
        comparison.runComparisonBenchmark();
        
        std::cout << "\n=====================================================" << std::endl;
        std::cout << "    性能对比测试完成" << std::endl;
        std::cout << "=====================================================" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
