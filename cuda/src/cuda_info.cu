#include <cuda_runtime.h>
#include <iostream>

int main() {
    int deviceCount;
    cudaGetDeviceCount(&deviceCount);

    for (int device = 0; device < deviceCount; ++device) {
        cudaDeviceProp deviceProp;
        cudaGetDeviceProperties(&deviceProp, device);
        
        std::cout << "Device " << device << ": " << deviceProp.name << std::endl;
        std::cout << "  Memory: " << deviceProp.totalGlobalMem / 1024 / 1024 << " MB" << std::endl;
        std::cout << "  CUDA Compute Capability: " << deviceProp.major << "." << deviceProp.minor << std::endl;
        std::cout << "  Multi Processor Count: " << deviceProp.multiProcessorCount << std::endl;
        std::cout << "  Max Threads per Block: " << deviceProp.maxThreadsPerBlock << std::endl;
    }

    return 0;
}
