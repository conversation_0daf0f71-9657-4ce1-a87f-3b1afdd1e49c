/**
 * @file complete_parallel_strategies.cpp
 * @brief 完整的并行策略实现
 * 
 * 基于现有项目中的所有并行实现，提供完整的并行策略支持
 */

#include "complete_ntt_engine.hpp"
#include <omp.h>
#include <thread>
#include <queue>
#include <mutex>
#include <atomic>

#ifdef __x86_64__
#include <immintrin.h>
#endif

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif
#include <thread>
#include <future>
#include <queue>
#include <condition_variable>

#ifdef USE_MPI
#include <mpi.h>
#endif

#ifdef USE_CUDA
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

namespace HybridNTT {

// ============ OpenMP任务并行实现 ============
void CompleteNTTEngine::openmp_task_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        // 使用OpenMP任务并行处理每个块
        #pragma omp parallel
        {
            #pragma omp single
            {
                for (int i = 0; i < n; i += len) {
                    #pragma omp task firstprivate(i, len, half, wn, modulus)
                    {
                        uint32_t w = 1;
                        for (int j = 0; j < half; j++) {
                            int u = data[i + j];
                            int v = (1LL * data[i + j + half] * w) % modulus;
                            data[i + j] = (u + v) % modulus;
                            data[i + j + half] = (u - v + modulus) % modulus;
                            w = (1LL * w * wn) % modulus;
                        }
                    }
                }
                #pragma omp taskwait
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        #pragma omp parallel for
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ pthread多线程实现 ============
struct ThreadData {
    std::vector<int>* data;
    int start;
    int len;
    int half;
    uint32_t wn;
    int modulus;
};

void* butterfly_worker(void* arg) {
    ThreadData* td = static_cast<ThreadData*>(arg);
    uint32_t w = 1;
    
    for (int j = 0; j < td->half; j++) {
        int u = (*td->data)[td->start + j];
        int v = (1LL * (*td->data)[td->start + j + td->half] * w) % td->modulus;
        (*td->data)[td->start + j] = (u + v) % td->modulus;
        (*td->data)[td->start + j + td->half] = (u - v + td->modulus) % td->modulus;
        w = (1LL * w * td->wn) % td->modulus;
    }
    
    return nullptr;
}

// ============ pthread多线程实现 - 完整Radix-4优化版本 ============
void CompleteNTTEngine::pthread_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    int num_threads = std::min(8, (int)std::thread::hardware_concurrency());

    // 使用Radix-4算法进行pthread并行化
    pthread_radix4_ntt(data, modulus, inverse, num_threads);
}

// 完整的pthread Radix-4 NTT实现
void CompleteNTTEngine::pthread_radix4_ntt(std::vector<int>& data, int modulus, bool inverse, int num_threads) {
    int n = data.size();

    // Radix-4数字反转
    pthread_digit_reverse_4(data, num_threads);

    // Radix-4 NTT主循环
    for (int len = 4; len <= n; len <<= 2) {
        int m = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) wn = modularInverse(wn, modulus);
        uint32_t J = fastPow(wn, m, modulus);

        // 预计算旋转因子表
        std::vector<uint32_t> wtab(m);
        wtab[0] = 1;
        for (int j = 1; j < m; ++j) {
            wtab[j] = (1LL * wtab[j-1] * wn) % modulus;
        }

        // 并行处理块
        pthread_parallel_for(0, n, len, [&](int blk) {
            for (int j = 0; j < m; ++j) {
                uint32_t w1 = wtab[j];
                uint32_t w2 = (1LL * w1 * w1) % modulus;
                uint32_t w3 = (1LL * w2 * w1) % modulus;

                uint32_t A = data[blk + j];
                uint32_t B = (1LL * data[blk + j + m] * w1) % modulus;
                uint32_t C = (1LL * data[blk + j + 2*m] * w2) % modulus;
                uint32_t D = (1LL * data[blk + j + 3*m] * w3) % modulus;

                uint32_t T0 = (A + C) % modulus;
                uint32_t T1 = (A + modulus - C) % modulus;
                uint32_t T2 = (B + D) % modulus;
                uint32_t T3 = (1LL * (B + modulus - D) * J) % modulus;

                data[blk + j] = (T0 + T2) % modulus;
                data[blk + j + m] = (T1 + T3) % modulus;
                data[blk + j + 2*m] = (T0 + modulus - T2) % modulus;
                data[blk + j + 3*m] = (T1 + modulus - T3) % modulus;
            }
        }, num_threads);
    }

    // 逆变换处理
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        pthread_parallel_for(0, n, 1, [&](int i) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }, num_threads);
    }
}

// pthread并行for循环实现
template<typename F>
void CompleteNTTEngine::pthread_parallel_for(int begin, int end, int step, const F& func, int num_threads) {
    if (end - begin <= step * num_threads) {
        // 小任务串行执行
        for (int i = begin; i < end; i += step) {
            func(i);
        }
        return;
    }

    struct ThreadArg {
        int start, end, step;
        const F* func;
    };

    auto thread_worker = [](void* arg) -> void* {
        ThreadArg* targ = static_cast<ThreadArg*>(arg);
        for (int i = targ->start; i < targ->end; i += targ->step) {
            (*targ->func)(i);
        }
        return nullptr;
    };

    int chunk = ((end - begin) + num_threads - 1) / num_threads;
    std::vector<pthread_t> threads(num_threads);
    std::vector<ThreadArg> args(num_threads);

    for (int t = 0; t < num_threads; ++t) {
        int start = begin + t * chunk;
        int thread_end = std::min(end, start + chunk);
        args[t] = {start, thread_end, step, &func};
        pthread_create(&threads[t], nullptr, thread_worker, &args[t]);
    }

    for (auto& thread : threads) {
        pthread_join(thread, nullptr);
    }
}

// pthread数字反转实现
void CompleteNTTEngine::pthread_digit_reverse_4(std::vector<int>& data, int num_threads) {
    int n = data.size();
    std::vector<int> rev(n);

    // 计算4进制反转表
    for (int i = 0; i < n; ++i) {
        rev[i] = 0;
        int temp = i;
        int len = n;
        while (len > 1) {
            rev[i] = (rev[i] << 2) | (temp & 3);
            temp >>= 2;
            len >>= 2;
        }
    }

    // 并行执行反转
    pthread_parallel_for(0, n, 1, [&](int i) {
        if (i < rev[i]) {
            std::swap(data[i], data[rev[i]]);
        }
    }, num_threads);
}

// ============ MPI分布式并行实现 ============
void CompleteNTTEngine::mpi_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_MPI
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    int n = data.size();

    // 初始化Barrett约简参数
    initBarrettParams(modulus);

    // 位反转（仅在rank 0执行）
    if (rank == 0) {
        bitReversePermutation(data);
    }

    // 广播位反转后的数据
    MPI_Bcast(data.data(), n, MPI_INT, 0, MPI_COMM_WORLD);

    // NTT主循环 - 分布式并行处理
    for (int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        // 计算每个进程负责的块数
        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / size;
        int remainder_blocks = total_blocks % size;

        int my_blocks = blocks_per_proc + (rank < remainder_blocks ? 1 : 0);
        int start_block = rank * blocks_per_proc + std::min(rank, remainder_blocks);

        // 每个进程处理分配给它的块
        for (int b = 0; b < my_blocks; ++b) {
            int block_start = (start_block + b) * len;
            uint32_t w = 1;

            for (int j = 0; j < m; ++j) {
                if (block_start + j + m < n) {
                    // 使用Barrett约简优化模运算
                    uint32_t u = data[block_start + j];
                    uint32_t v = barrett_params_.mul(data[block_start + j + m], w);

                    data[block_start + j] = barrett_params_.add(u, v);
                    data[block_start + j + m] = barrett_params_.sub(u, v);

                    w = barrett_params_.mul(w, wn);
                }
            }
        }

        // 准备通信数据
        std::vector<int> recvcounts(size), displs(size);
        for (int r = 0; r < size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + std::min(r, remainder_blocks)) * len;
        }

        // 收集本进程处理的数据
        std::vector<int> temp_data(my_blocks * len);
        if (my_blocks > 0) {
            std::memcpy(temp_data.data(), data.data() + start_block * len,
                       my_blocks * len * sizeof(int));
        }

        // 使用Allgatherv同步所有进程的结果
        MPI_Allgatherv(my_blocks > 0 ? temp_data.data() : MPI_IN_PLACE,
                       my_blocks * len, MPI_INT,
                       data.data(), recvcounts.data(), displs.data(), MPI_INT,
                       MPI_COMM_WORLD);
    }

    // 逆变换处理
    if (inverse) {
        if (rank == 0) {
            uint32_t inv_n = modularInverse(n, modulus);
            for (int& val : data) {
                val = barrett_params_.mul(val, inv_n);
            }
        }
        MPI_Bcast(data.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
    }

#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

// ============ Radix-4 DIT实现 ============
void CompleteNTTEngine::radix4_dit_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    bitReversePermutation(data);
    
    for (int len = 4; len <= n; len <<= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = (1LL * data[i + j + quarter] * w1) % modulus;
                int c = (1LL * data[i + j + 2*quarter] * w2) % modulus;
                int d = (1LL * data[i + j + 3*quarter] * w3) % modulus;
                
                // Radix-4蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (t2 + t4) % modulus;
                data[i + j + 2*quarter] = (t1 - t3 + modulus) % modulus;
                data[i + j + 3*quarter] = (t2 - t4 + modulus) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    // 处理剩余的Radix-2层
    for (int len = 2; len <= n && (len & (len-1)) == 0; len <<= 1) {
        if (len >= 4 && (len & 3) == 0) continue; // 已经被Radix-4处理
        
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;
            for (int j = 0; j < half; j++) {
                int u = data[i + j];
                int v = (1LL * data[i + j + half] * w) % modulus;
                data[i + j] = (u + v) % modulus;
                data[i + j + half] = (u - v + modulus) % modulus;
                w = (1LL * w * wn) % modulus;
            }
        }
    }
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ Radix-4 DIF实现 ============
void CompleteNTTEngine::radix4_dif_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();
    
    // Radix-4 DIF蝶形运算
    for (int len = n; len >= 4; len >>= 2) {
        int quarter = len >> 2;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }
        
        uint32_t wn2 = (1LL * wn * wn) % modulus;
        uint32_t wn3 = (1LL * wn2 * wn) % modulus;
        
        for (int i = 0; i < n; i += len) {
            uint32_t w1 = 1, w2 = 1, w3 = 1;
            
            for (int j = 0; j < quarter; j++) {
                int a = data[i + j];
                int b = data[i + j + quarter];
                int c = data[i + j + 2*quarter];
                int d = data[i + j + 3*quarter];
                
                // Radix-4 DIF蝶形运算
                int t1 = (a + c) % modulus;
                int t2 = (a - c + modulus) % modulus;
                int t3 = (b + d) % modulus;
                int t4 = (b - d + modulus) % modulus;
                
                data[i + j] = (t1 + t3) % modulus;
                data[i + j + quarter] = (1LL * (t2 + t4) * w1) % modulus;
                data[i + j + 2*quarter] = (1LL * (t1 - t3 + modulus) * w2) % modulus;
                data[i + j + 3*quarter] = (1LL * (t2 - t4 + modulus) * w3) % modulus;
                
                w1 = (1LL * w1 * wn) % modulus;
                w2 = (1LL * w2 * wn2) % modulus;
                w3 = (1LL * w3 * wn3) % modulus;
            }
        }
    }
    
    bitReversePermutation(data);
    
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int& val : data) {
            val = (1LL * val * inv_n) % modulus;
        }
    }
}

// ============ CUDA GPU并行实现 - 完整Montgomery约简版本 ============
void CompleteNTTEngine::cuda_parallel_ntt(std::vector<int>& data, int modulus, bool inverse) {
#ifdef USE_CUDA
    // 使用完整的CUDA Montgomery NTT实现
    cuda_montgomery_ntt(data, modulus, inverse);
#else
    // 回退到OpenMP实现
    openmp_data_parallel_ntt(data, modulus, inverse);
#endif
}

#ifdef USE_CUDA
// CUDA Montgomery NTT实现
void CompleteNTTEngine::cuda_montgomery_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();

    // 创建CUDA Montgomery参数
    CudaMontgomeryParams mont(modulus);

    // 分配GPU内存
    int *d_data, *d_rev, *d_twiddles;
    cudaMalloc(&d_data, n * sizeof(int));
    cudaMalloc(&d_rev, n * sizeof(int));
    cudaMalloc(&d_twiddles, n * sizeof(int));

    // 复制数据到GPU
    cudaMemcpy(d_data, data.data(), n * sizeof(int), cudaMemcpyHostToDevice);

    // 预计算位反转表
    std::vector<int> rev(n);
    for (int i = 0; i < n; ++i) {
        rev[i] = 0;
        int temp = i;
        int len = n;
        while (len > 1) {
            rev[i] = (rev[i] << 1) | (temp & 1);
            temp >>= 1;
            len >>= 1;
        }
    }
    cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice);

    // 预计算旋转因子
    std::vector<int> twiddles;
    cuda_precompute_twiddles(twiddles, n, modulus, inverse);
    cudaMemcpy(d_twiddles, twiddles.data(), twiddles.size() * sizeof(int), cudaMemcpyHostToDevice);

    // 执行位反转
    cuda_bit_reverse_kernel<<<(n + 1023) / 1024, 1024>>>(d_data, d_rev, n);
    cudaDeviceSynchronize();

    // 转换到Montgomery域
    cuda_to_montgomery_kernel<<<(n + 1023) / 1024, 1024>>>(d_data, mont, n);
    cudaDeviceSynchronize();

    // 执行NTT
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        int half_len = len >> 1;
        int blocks = ((n / len) * half_len + 1023) / 1024;
        cuda_ntt_montgomery_kernel<<<blocks, 1024>>>(d_data, d_twiddles, len, mont, n, offset);
        offset += half_len;
        cudaDeviceSynchronize();
    }

    // 逆变换处理
    if (inverse) {
        unsigned int inv_n_mont = mont.to_montgomery(modularInverse(n, modulus));
        cuda_final_scaling_kernel<<<(n + 1023) / 1024, 1024>>>(d_data, inv_n_mont, mont, n);
        cudaDeviceSynchronize();
    }

    // 从Montgomery域转换回来
    cuda_from_montgomery_kernel<<<(n + 1023) / 1024, 1024>>>(d_data, mont, n);
    cudaDeviceSynchronize();

    // 复制结果回CPU
    cudaMemcpy(data.data(), d_data, n * sizeof(int), cudaMemcpyDeviceToHost);

    // 释放GPU内存
    cudaFree(d_data);
    cudaFree(d_rev);
    cudaFree(d_twiddles);
}

// CUDA位反转kernel
__global__ void cuda_bit_reverse_kernel(int *data, const int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        int target = rev[idx];
        if (idx < target) {
            int tmp = data[idx];
            data[idx] = data[target];
            data[target] = tmp;
        }
    }
}

// CUDA Montgomery转换kernel
__global__ void cuda_to_montgomery_kernel(int *data, CudaMontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.to_montgomery((unsigned int)data[idx]);
    }
}

// CUDA Montgomery NTT kernel
__global__ void cuda_ntt_montgomery_kernel(int *data, const int *twiddles, int len,
                                         CudaMontgomeryParams mont, int n, int offset) {
    int t = blockIdx.x * blockDim.x + threadIdx.x;
    int k = t % (len >> 1);
    int j = t / (len >> 1);
    int i = j * len + k;

    if (j * len >= n) return;

    unsigned int w = (unsigned int)twiddles[offset + k];
    unsigned int u = (unsigned int)data[i];
    unsigned int v = mont.mul((unsigned int)data[i + (len >> 1)], w);

    data[i] = (int)mont.add(u, v);
    data[i + (len >> 1)] = (int)mont.sub(u, v);
}

// CUDA最终缩放kernel
__global__ void cuda_final_scaling_kernel(int *data, unsigned int factor_mont,
                                         CudaMontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        unsigned int res_mont = mont.mul((unsigned int)data[idx], factor_mont);
        data[idx] = mont.from_montgomery(res_mont);
    }
}

// CUDA从Montgomery域转换kernel
__global__ void cuda_from_montgomery_kernel(int *data, CudaMontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.from_montgomery((unsigned int)data[idx]);
    }
}
// CUDA位反转实现
void CompleteNTTEngine::cuda_bit_reverse(std::vector<int>& data) {
    int n = data.size();
    std::vector<int> rev(n);

    for (int i = 0; i < n; ++i) {
        rev[i] = 0;
        int temp = i;
        int len = n;
        while (len > 1) {
            rev[i] = (rev[i] << 1) | (temp & 1);
            temp >>= 1;
            len >>= 1;
        }
    }

    for (int i = 0; i < n; ++i) {
        if (i < rev[i]) {
            std::swap(data[i], data[rev[i]]);
        }
    }
}

// CUDA旋转因子预计算
void CompleteNTTEngine::cuda_precompute_twiddles(std::vector<int>& twiddles, int n, int modulus, bool inverse) {
    twiddles.assign(n, 0);
    int offset = 0;

    for (int len = 2; len <= n; len <<= 1) {
        // 使用原根3计算旋转因子
        long long wn_normal = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            // 计算逆元 wn^(-1) = wn^(modulus-2) mod modulus
            wn_normal = fastPow(wn_normal, modulus - 2, modulus);
        }

        // 初始化为1
        long long w_normal = 1;
        for (int i = 0; i < len / 2; i++) {
            // 转换为Montgomery形式并存储
            CudaMontgomeryParams mont(modulus);
            twiddles[offset + i] = mont.to_montgomery((unsigned int)w_normal);
            // 计算下一个旋转因子: w *= wn
            w_normal = (w_normal * wn_normal) % modulus;
        }
        offset += len / 2;
    }
}
// ============ Split-Radix NTT实现 - 理论最优复杂度 ============
void CompleteNTTEngine::split_radix_ntt(std::vector<int>& data, int modulus, bool inverse) {
    split_radix_parallel_ntt(data, modulus, inverse, 1);
}

void CompleteNTTEngine::split_radix_parallel_ntt(std::vector<int>& data, int modulus, bool inverse, int num_threads) {
    int n = data.size();

    // 位反转置换
    bitReversePermutation(data);

    // Split-Radix蝶形运算 - 混合Radix-2/4策略
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        // 并行化外层循环
        #pragma omp parallel for num_threads(num_threads) if(n / len >= num_threads && len >= 64)
        for (int i = 0; i < n; i += len) {
            uint32_t w = 1;

            // Split-Radix优化：对于较大的len，使用Radix-4
            if (len >= 4 && (len & (len - 1)) == 0) {
                // Radix-4蝶形运算
                int quarter = len >> 2;
                uint32_t wn2 = (1LL * wn * wn) % modulus;
                uint32_t wn3 = (1LL * wn2 * wn) % modulus;

                for (int j = 0; j < quarter; j++) {
                    uint32_t w2 = (1LL * w * w) % modulus;
                    uint32_t w3 = (1LL * w2 * w) % modulus;

                    int u0 = data[i + j];
                    int u1 = data[i + j + quarter];
                    int u2 = data[i + j + 2 * quarter];
                    int u3 = data[i + j + 3 * quarter];

                    // Radix-4蝶形运算
                    int v1 = (1LL * u1 * w) % modulus;
                    int v2 = (1LL * u2 * w2) % modulus;
                    int v3 = (1LL * u3 * w3) % modulus;

                    data[i + j] = (u0 + v1 + v2 + v3) % modulus;
                    data[i + j + quarter] = (u0 - v1 + v2 - v3 + 4LL * modulus) % modulus;
                    data[i + j + 2 * quarter] = (u0 + v1 - v2 - v3 + 4LL * modulus) % modulus;
                    data[i + j + 3 * quarter] = (u0 - v1 - v2 + v3 + 4LL * modulus) % modulus;

                    w = (1LL * w * wn) % modulus;
                }
            } else {
                // 标准Radix-2蝶形运算
                for (int j = 0; j < half; j++) {
                    int u = data[i + j];
                    int v = (1LL * data[i + j + half] * w) % modulus;
                    data[i + j] = (u + v) % modulus;
                    data[i + j + half] = (u - v + modulus) % modulus;
                    w = (1LL * w * wn) % modulus;
                }
            }
        }
    }

    // 逆变换处理
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        #pragma omp parallel for num_threads(num_threads) if(n >= 1024)
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ Cache-Oblivious NTT实现 - 内存局部性优化 ============
void CompleteNTTEngine::cache_oblivious_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();

    // 位反转置换
    bitReversePermutation(data);

    // 递归Cache-Oblivious实现
    uint32_t omega = fastPow(3, (modulus - 1) / n, modulus);
    if (inverse) {
        omega = modularInverse(omega, modulus);
    }

    recursive_cache_ntt(data, 0, n, 1, omega, modulus, inverse);

    // 逆变换处理
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

void CompleteNTTEngine::recursive_cache_ntt(std::vector<int>& data, int start, int n, int stride,
                                           uint32_t omega, int modulus, bool inverse) {
    if (n <= 64) {
        // 基础情况：使用传统NTT
        std::vector<int> temp(n);
        for (int i = 0; i < n; i++) {
            temp[i] = data[start + i * stride];
        }

        // 简化的基础NTT
        for (int len = 2; len <= n; len <<= 1) {
            int half = len >> 1;
            uint32_t wn = fastPow(omega, n / len, modulus);

            for (int i = 0; i < n; i += len) {
                uint32_t w = 1;
                for (int j = 0; j < half; j++) {
                    int u = temp[i + j];
                    int v = (1LL * temp[i + j + half] * w) % modulus;
                    temp[i + j] = (u + v) % modulus;
                    temp[i + j + half] = (u - v + modulus) % modulus;
                    w = (1LL * w * wn) % modulus;
                }
            }
        }

        for (int i = 0; i < n; i++) {
            data[start + i * stride] = temp[i];
        }
        return;
    }

    // 递归分治
    int half = n >> 1;
    uint32_t omega_half = (1LL * omega * omega) % modulus;

    // 递归处理偶数和奇数位置
    recursive_cache_ntt(data, start, half, stride * 2, omega_half, modulus, inverse);
    recursive_cache_ntt(data, start + stride, half, stride * 2, omega_half, modulus, inverse);

    // 合并结果
    uint32_t w = 1;
    for (int i = 0; i < half; i++) {
        int u = data[start + i * stride * 2];
        int v = (1LL * data[start + (i + half) * stride * 2] * w) % modulus;
        data[start + i * stride * 2] = (u + v) % modulus;
        data[start + (i + half) * stride * 2] = (u - v + modulus) % modulus;
        w = (1LL * w * omega) % modulus;
    }
}

// ============ Work-Stealing并行NTT实现 - 动态负载均衡 ============
void CompleteNTTEngine::work_stealing_ntt(std::vector<int>& data, int modulus, bool inverse) {
    int n = data.size();

    // 位反转置换
    bitReversePermutation(data);

    // Work-Stealing任务队列
    std::vector<std::queue<WorkStealingTask>> task_queues(std::thread::hardware_concurrency());
    std::vector<std::mutex> queue_mutexes(task_queues.size());
    std::atomic<int> active_threads(task_queues.size());

    // 初始化任务
    for (int len = 2; len <= n; len <<= 1) {
        int half = len >> 1;
        uint32_t wn = fastPow(3, (modulus - 1) / len, modulus);
        if (inverse) {
            wn = modularInverse(wn, modulus);
        }

        for (int i = 0; i < n; i += len) {
            WorkStealingTask task(i, i + len, len, wn);
            task_queues[0].push(task);
        }
    }

    // Work-Stealing工作线程
    auto worker = [&](int thread_id) {
        while (active_threads.load() > 0) {
            WorkStealingTask task;
            bool found_task = false;

            // 尝试从自己的队列获取任务
            {
                std::lock_guard<std::mutex> lock(queue_mutexes[thread_id]);
                if (!task_queues[thread_id].empty()) {
                    task = task_queues[thread_id].front();
                    task_queues[thread_id].pop();
                    found_task = true;
                }
            }

            // 如果自己的队列为空，尝试偷取其他线程的任务
            if (!found_task) {
                for (size_t i = 1; i < task_queues.size(); ++i) {
                    int target = (thread_id + i) % task_queues.size();
                    std::lock_guard<std::mutex> lock(queue_mutexes[target]);
                    if (!task_queues[target].empty()) {
                        task = task_queues[target].front();
                        task_queues[target].pop();
                        found_task = true;
                        break;
                    }
                }
            }

            if (found_task) {
                // 执行蝶形运算
                int len = task.level;
                int half = len >> 1;
                uint32_t w = 1;

                for (int j = 0; j < half; j++) {
                    int u = data[task.start + j];
                    int v = (1LL * data[task.start + j + half] * w) % modulus;
                    data[task.start + j] = (u + v) % modulus;
                    data[task.start + j + half] = (u - v + modulus) % modulus;
                    w = (1LL * w * task.omega) % modulus;
                }
            } else {
                // 没有任务，线程退出
                active_threads.fetch_sub(1);
                break;
            }
        }
    };

    // 启动工作线程
    std::vector<std::thread> workers;
    for (size_t i = 0; i < task_queues.size(); ++i) {
        workers.emplace_back(worker, i);
    }

    // 等待所有线程完成
    for (auto& t : workers) {
        t.join();
    }

    // 逆变换处理
    if (inverse) {
        uint32_t inv_n = modularInverse(n, modulus);
        for (int i = 0; i < n; i++) {
            data[i] = (1LL * data[i] * inv_n) % modulus;
        }
    }
}

// ============ CRT多模数并行NTT实现 ============
void CompleteNTTEngine::crt_multi_modulus_ntt(std::vector<int>& data, bool inverse) {
    std::vector<std::vector<int>> modulus_results(crt_params_.moduli.size());

    // 并行计算每个模数的NTT
    #pragma omp parallel for
    for (size_t i = 0; i < crt_params_.moduli.size(); ++i) {
        modulus_results[i] = data;  // 复制数据

        // 对每个模数执行NTT
        int mod = crt_params_.moduli[i];
        for (int& val : modulus_results[i]) {
            val %= mod;
        }

        // 使用Barrett约简优化的NTT
        barrett_params_.setModulus(mod);
        radix2_dit_ntt(modulus_results[i], mod, inverse);
    }

    // 使用CRT合并结果
    crt_merge_results(modulus_results, data);
}

void CompleteNTTEngine::crt_merge_results(std::vector<std::vector<int>>& results, std::vector<int>& final_result) {
    int n = results[0].size();
    final_result.resize(n);

    for (int i = 0; i < n; ++i) {
        std::vector<int> remainders(results.size());
        for (size_t j = 0; j < results.size(); ++j) {
            remainders[j] = results[j][i];
        }

        // CRT重构
        long long result = crt_params_.reconstruct(remainders);
        final_result[i] = static_cast<int>(result);
    }
}

#endif

} // namespace HybridNTT
