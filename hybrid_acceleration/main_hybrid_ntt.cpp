/**
 * @file main_hybrid_ntt.cpp
 * @brief 智能混合并行NTT加速框架主程序
 * <AUTHOR> Acceleration Framework
 */

#include "src/core/hardware_detector.hpp"
#include "src/core/strategy_selector.hpp"
#include "src/algorithms/hybrid_ntt_engine.hpp"
#include "src/utils/io_interface.hpp"
#include <iostream>
#include <vector>
#include <memory>
#include <iomanip>

#ifdef _OPENMP
#include <omp.h>
#endif

#ifdef MPI_VERSION
#include <mpi.h>
#endif

using namespace HybridNTT;

/**
 * @brief 初始化MPI环境（如果可用）
 * @param argc 命令行参数数量
 * @param argv 命令行参数
 * @return MPI是否初始化成功
 */
bool initializeMPI(int argc, char* argv[]) {
#ifdef MPI_VERSION
    int provided;
    MPI_Init_thread(&argc, &argv, MPI_THREAD_MULTIPLE, &provided);
    
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    if (rank == 0) {
        std::cout << "MPI初始化成功，进程数: " << size << std::endl;
    }
    return true;
#else
    return false;
#endif
}

/**
 * @brief 清理MPI环境
 */
void finalizeMPI() {
#ifdef MPI_VERSION
    MPI_Finalize();
#endif
}

/**
 * @brief 运行单个测试用例
 * @param engine NTT引擎
 * @param testId 测试用例ID
 * @param algorithmName 算法名称
 * @param serialTime 串行执行时间（用于计算加速比）
 * @return 是否成功
 */
bool runTestCase(std::shared_ptr<HybridNTTEngine> engine, 
                int testId, 
                const std::string& algorithmName,
                double serialTime = 0.0) {
    try {
        std::vector<int> a, b, ab;
        int n, p;
        
        // 读取测试数据
        IOInterface::fRead(a, b, &n, &p, testId);
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "测试用例 " << testId << ": n=" << n << ", p=" << p << std::endl;
        std::cout << "========================================" << std::endl;
        
        // 执行自动优化的多项式乘法
        double execTime = engine->autoOptimizedMultiply(a, b, ab, n, p);
        
        // 验证结果正确性
        bool isCorrect = IOInterface::fCheck(ab, n, testId);
        
        if (!isCorrect) {
            std::cerr << "测试用例 " << testId << " 结果错误！" << std::endl;
            return false;
        }
        
        // 计算加速比
        double speedup = (serialTime > 0.0) ? (serialTime / execTime) : 0.0;
        
        // 输出结果摘要
        IOInterface::printSummary(algorithmName, n, p, execTime, speedup);
        
        // 记录性能数据
        std::string additionalInfo = "strategy=";
        auto strategy = engine->getCurrentStrategy();
        switch (strategy.parallelStrategy) {
            case ParallelStrategy::OPENMP_SIMD:
                additionalInfo += "OpenMP+SIMD";
                break;
            case ParallelStrategy::MPI_OPENMP_SIMD:
                additionalInfo += "MPI+OpenMP+SIMD";
                break;
            case ParallelStrategy::HYBRID_ALL:
                additionalInfo += "HybridAll";
                break;
            default:
                additionalInfo += "Auto";
                break;
        }
        
        additionalInfo += ",threads=" + std::to_string(strategy.numThreads);
        additionalInfo += ",vectorWidth=" + std::to_string(strategy.vectorWidth);
        
        IOInterface::recordPerformance(algorithmName, testId, n, p, 1, execTime, additionalInfo);
        
        // 写入输出文件
        IOInterface::fWrite(ab, n, testId);
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "测试用例 " << testId << " 执行失败: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief 运行性能对比测试
 * @param engine NTT引擎
 */
void runPerformanceComparison(std::shared_ptr<HybridNTTEngine> engine) {
    std::cout << "\n========================================" << std::endl;
    std::cout << "          性能对比测试" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 测试不同规模的性能表现
    std::vector<int> testSizes = {4, 1024, 4096, 16384, 65536, 131072};
    int modulus = 7340033;
    
    for (int n : testSizes) {
        if (n > 131072) break; // 避免超出测试数据范围
        
        std::cout << "\n--- 规模测试: n = " << n << " ---" << std::endl;
        
        // 生成测试数据
        std::vector<int> a(n), b(n), ab;
        for (int i = 0; i < n; ++i) {
            a[i] = rand() % modulus;
            b[i] = rand() % modulus;
        }
        
        // 测试自动优化策略
        auto startTime = IOInterface::getCurrentTime();
        engine->autoOptimizedMultiply(a, b, ab, n, modulus);
        auto endTime = IOInterface::getCurrentTime();
        
        double execTime = IOInterface::getElapsedTime(startTime, endTime);
        
        std::cout << "执行时间: " << std::fixed << std::setprecision(2) 
                  << execTime << " 微秒" << std::endl;
        
        // 记录性能数据
        IOInterface::recordPerformance("hybrid_auto_scale", -1, n, modulus, 1, execTime, 
                                     "scalability_test");
    }
}

/**
 * @brief 运行策略分析测试
 * @param detector 硬件检测器
 * @param selector 策略选择器
 */
void runStrategyAnalysis(std::shared_ptr<HardwareDetector> detector,
                        std::shared_ptr<StrategySelector> selector) {
    std::cout << "\n========================================" << std::endl;
    std::cout << "          策略分析测试" << std::endl;
    std::cout << "========================================" << std::endl;
    
    std::vector<int> testSizes = {4, 1024, 16384, 131072};
    int modulus = 7340033;
    
    for (int n : testSizes) {
        std::cout << "\n--- 问题规模: n = " << n << " ---" << std::endl;
        
        auto strategy = selector->selectOptimalStrategy(n, modulus);
        selector->printStrategyDetails(strategy);
        
        std::cout << "硬件评分: " << detector->calculateHardwareScore() << std::endl;
    }
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    std::cout << "====================================================" << std::endl;
    std::cout << "    智能多层次混合并行NTT加速框架" << std::endl;
    std::cout << "    Intelligent Multi-Level Hybrid Parallel NTT" << std::endl;
    std::cout << "====================================================" << std::endl;
    
    try {
        // 初始化MPI（如果可用）
        bool mpiEnabled = initializeMPI(argc, argv);
        
        // 创建硬件检测器
        auto hardwareDetector = std::make_shared<HardwareDetector>();
        hardwareDetector->detectAll();
        hardwareDetector->printSummary();
        
        // 创建策略选择器
        auto strategySelector = std::make_shared<StrategySelector>(hardwareDetector);
        
        // 创建混合NTT引擎
        auto nttEngine = std::make_shared<HybridNTTEngine>(hardwareDetector, strategySelector);
        
        // 运行策略分析
        runStrategyAnalysis(hardwareDetector, strategySelector);
        
        // 运行标准测试用例
        std::cout << "\n========================================" << std::endl;
        std::cout << "          标准测试用例" << std::endl;
        std::cout << "========================================" << std::endl;
        
        std::vector<double> serialTimes; // 用于计算加速比
        bool allTestsPassed = true;
        
        for (int testId = 0; testId <= 3; ++testId) {
            bool success = runTestCase(nttEngine, testId, "hybrid_intelligent_ntt");
            if (!success) {
                allTestsPassed = false;
                std::cerr << "测试用例 " << testId << " 失败！" << std::endl;
            }
        }
        
        if (allTestsPassed) {
            std::cout << "\n✅ 所有测试用例通过！" << std::endl;
        } else {
            std::cout << "\n❌ 部分测试用例失败！" << std::endl;
        }
        
        // 运行性能对比测试
        runPerformanceComparison(nttEngine);
        
        // 输出性能分析报告
        std::cout << "\n========================================" << std::endl;
        std::cout << "          框架特性总结" << std::endl;
        std::cout << "========================================" << std::endl;
        
        std::cout << "✓ 硬件自适应检测与配置" << std::endl;
        std::cout << "✓ 智能策略选择与优化" << std::endl;
        std::cout << "✓ 多层次混合并行加速" << std::endl;
        std::cout << "✓ 自动算法变体选择" << std::endl;
        std::cout << "✓ 动态负载均衡调度" << std::endl;
        std::cout << "✓ 兼容原有项目接口" << std::endl;
        
        auto hardwareScore = hardwareDetector->calculateHardwareScore();
        std::cout << "\n硬件配置评分: " << std::fixed << std::setprecision(1) 
                  << hardwareScore << " 分" << std::endl;
        
        if (hardwareScore > 500) {
            std::cout << "硬件配置优秀，建议使用全混合并行策略" << std::endl;
        } else if (hardwareScore > 200) {
            std::cout << "硬件配置良好，建议使用多线程+SIMD策略" << std::endl;
        } else {
            std::cout << "硬件配置基础，建议使用单线程优化策略" << std::endl;
        }
        
        std::cout << "\n性能结果已保存到 hybrid_results.csv" << std::endl;
        std::cout << "输出文件已保存到 files/ 目录" << std::endl;
        
        // 清理MPI环境
        finalizeMPI();
        
        std::cout << "\n程序执行完成。" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "程序执行失败: " << e.what() << std::endl;
        finalizeMPI();
        return 1;
    }
}

/* 
===================================================
编译指令:

基础版本 (仅CPU):
g++ -std=c++17 -O3 -march=native -fopenmp main_hybrid_ntt.cpp -o hybrid_ntt

MPI版本:
mpicxx -std=c++17 -O3 -march=native -fopenmp -DMPI_VERSION main_hybrid_ntt.cpp -o hybrid_ntt_mpi

CUDA版本 (如果有GPU):
nvcc -std=c++17 -O3 -arch=sm_75 -Xcompiler "-fopenmp" main_hybrid_ntt.cpp -o hybrid_ntt_cuda

运行指令:
./hybrid_ntt                    # 单机版本
mpirun -np 4 ./hybrid_ntt_mpi   # MPI版本
./hybrid_ntt_cuda               # CUDA版本

===================================================
*/ 