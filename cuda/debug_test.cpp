#include <iostream>
#include <vector>
#include <cstring>
#include <algorithm>
using namespace std;

typedef long long ll;

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

void calc_powg(int w[], int G, int P, int gen) {
    w[0] = 1; ll f;
    const int g = qpow(gen, (P-1)/G, P);
    for (int t = 0; (1<<(t+1)) < G; ++t) {
        f = w[1<<t] = qpow(g, G>>(t+2), P);
        for (int x = 1<<t; x < 1<<(t+1); ++x)
            w[x] = (ll)f * w[x - (1<<t)] % P;
    }
}

void DIF(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = lim; len > 1; len >>= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len/2; ++i) {
                g = f[i];
                h = (ll)f[i + len/2] * w[t] % P;
                f[i] = (g + h) % P;
                f[i + len/2] = (P + g - h) % P;
            }
        }
    }
}

void DIT(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = 2; len <= lim; len <<= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len/2; ++i) {
                g = f[i];
                h = f[i + len/2];
                f[i] = (g + h) % P;
                f[i + len/2] = (P + g - h) * w[t] % P;
            }
        }
    }
    const ll invl = qpow(lim, P-2, P);
    for (int i = 0; i < lim; ++i)
        f[i] = invl * f[i] % P;
    reverse(f + 1, f + lim);
}

void poly_multiply_optimized(int *a, int *b, int *ab, int n, int p, int gen = 3) {
    memset(ab, 0, sizeof(int) * (2 * n - 1));
    int l = 0;
    while ((1 << l) < 2 * n) l++;
    int lim = 1 << l;
    
    int *A = new int[lim]();
    int *B = new int[lim]();
    int *w = new int[lim]();
    
    for (int i = 0; i < n; ++i) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    calc_powg(w, lim, p, gen);
    DIF(A, l, p, w);
    DIF(B, l, p, w);
    
    for (int i = 0; i < lim; ++i)
        A[i] = (ll)A[i] * B[i] % p;
    
    DIT(A, l, p, w);
    
    for (int i = 0; i < 2 * n - 1; ++i)
        ab[i] = A[i];
    
    delete[] A;
    delete[] B;
    delete[] w;
}

int main() {
    // 测试用例0: n=4, p=7340033
    int a[] = {4, 1, 5, 2};
    int b[] = {1, 5, 5, 4};
    int ab[7];
    
    poly_multiply_optimized(a, b, ab, 4, 7340033);
    
    cout << "结果: ";
    for(int i = 0; i < 7; i++) {
        cout << ab[i] << " ";
    }
    cout << endl;
    
    // 期望结果: 4 21 30 48 39 30 8
    int expected[] = {4, 21, 30, 48, 39, 30, 8};
    bool correct = true;
    for(int i = 0; i < 7; i++) {
        if(ab[i] != expected[i]) {
            correct = false;
            break;
        }
    }
    
    cout << (correct ? "正确" : "错误") << endl;
    
    return 0;
}
