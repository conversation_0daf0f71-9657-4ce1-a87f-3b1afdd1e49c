/**
 * @file complete_ntt_engine.hpp
 * @brief 完整的混合并行NTT引擎实现
 * <AUTHOR> Acceleration Framework
 * 
 * 基于现有项目的所有并行策略，实现完整的混合并行框架
 * 包括MPI、OpenMP、SIMD(AVX/SSE/NEON)、CUDA、pthread等所有并行技术
 */

#pragma once

#include <vector>
#include <memory>
#include <cmath>
#include <algorithm>
#include <immintrin.h>
#include <omp.h>
#include <thread>
#include <mutex>
#include <atomic>

#ifdef __ARM_NEON
#include <arm_neon.h>

// NEON SIMD辅助函数声明
namespace NEONUtils {
    inline int32x4_t mod_add_vec(int32x4_t a, int32x4_t b, int32x4_t mod_vec);
    inline int32x4_t mod_sub_vec(int32x4_t a, int32x4_t b, int32x4_t mod_vec);
    inline int32x4_t mul_mod_vec(int32x4_t v, int w, int modulus);
    inline int32x4_t mod_mul_vec(int32x4_t a, int32x4_t b, int32x4_t mod_vec);
}
#endif

#ifdef USE_MPI
// MPI头文件在需要时包含
#endif

#ifdef USE_CUDA
#include <cuda_runtime.h>
#endif

#include "../core/simple_hardware_detector.hpp"
#include "../core/simple_strategy_selector.hpp"

namespace HybridNTT {

/**
 * @brief 完整的混合并行NTT引擎
 * 
 * 实现所有粒度的并行策略：
 * 1. 数据并行：将数据分块并行处理
 * 2. 任务并行：将计算任务并行分发
 * 3. 混合并行：结合多种并行技术
 * 
 * 支持的算法变体：
 * - Radix-2 DIT/DIF
 * - Radix-4 DIT/DIF  
 * - Mixed-Radix
 * - Split-Radix
 * 
 * 支持的约简算法：
 * - 标准模运算
 * - Barrett约简
 * - Montgomery约简
 */
class CompleteNTTEngine {
public:
    // 算法变体枚举
    enum class NTTVariant {
        RADIX_2_DIT,
        RADIX_2_DIF,
        RADIX_4_DIT,
        RADIX_4_DIF,
        MIXED_RADIX,
        SPLIT_RADIX,
        CACHE_OBLIVIOUS,
        WORK_STEALING
    };
    
    // 约简算法枚举
    enum class ReductionType {
        STANDARD,
        BARRETT,
        MONTGOMERY
    };
    
    // 并行策略枚举
    enum class ParallelStrategy {
        SERIAL,
        OPENMP_DATA_PARALLEL,
        OPENMP_TASK_PARALLEL,
        SIMD_AVX,
        SIMD_SSE,
        SIMD_NEON,
        MPI_DATA_PARALLEL,
        MPI_TASK_PARALLEL,
        CUDA_PARALLEL,
        PTHREAD_PARALLEL,
        HYBRID_ALL
    };

private:
    std::shared_ptr<HardwareDetector> hardwareDetector_;
    std::shared_ptr<StrategySelector> strategySelector_;

    /**
     * @brief 转换智能NTT变体到传统变体
     */
    NTTVariant convertToNTTVariant(IntelligentNTTVariant variant) {
        switch (variant) {
            case IntelligentNTTVariant::RADIX_2_DIT: return NTTVariant::RADIX_2_DIT;
            case IntelligentNTTVariant::RADIX_2_DIF: return NTTVariant::RADIX_2_DIF;
            case IntelligentNTTVariant::RADIX_4_DIT: return NTTVariant::RADIX_4_DIT;
            case IntelligentNTTVariant::RADIX_4_DIF: return NTTVariant::RADIX_4_DIF;
            case IntelligentNTTVariant::SPLIT_RADIX: return NTTVariant::SPLIT_RADIX;
            case IntelligentNTTVariant::CACHE_OBLIVIOUS: return NTTVariant::CACHE_OBLIVIOUS;
            case IntelligentNTTVariant::WORK_STEALING: return NTTVariant::SPLIT_RADIX; // 映射到最接近的
            case IntelligentNTTVariant::CRT_MULTI_MODULUS: return NTTVariant::RADIX_2_DIT; // 映射到基础版本
            default: return NTTVariant::RADIX_2_DIT;
        }
    }

    /**
     * @brief 转换智能约简类型到传统约简类型
     */
    ReductionType convertToReductionType(IntelligentReductionType reduction) {
        switch (reduction) {
            case IntelligentReductionType::STANDARD: return ReductionType::STANDARD;
            case IntelligentReductionType::MONTGOMERY: return ReductionType::MONTGOMERY;
            case IntelligentReductionType::BARRETT: return ReductionType::BARRETT;
            case IntelligentReductionType::LEMIRE: return ReductionType::STANDARD; // 映射到标准版本
            default: return ReductionType::STANDARD;
        }
    }

    /**
     * @brief 转换智能并行策略到传统并行策略
     */
    ParallelStrategy convertToParallelStrategy(IntelligentParallelStrategy strategy) {
        switch (strategy) {
            case IntelligentParallelStrategy::SERIAL: return ParallelStrategy::SERIAL;
            case IntelligentParallelStrategy::OPENMP_PARALLEL: return ParallelStrategy::OPENMP_PARALLEL;
            case IntelligentParallelStrategy::OPENMP_TASK: return ParallelStrategy::OPENMP_TASK;
            case IntelligentParallelStrategy::SIMD_VECTORIZED: return ParallelStrategy::SIMD_VECTORIZED;
            case IntelligentParallelStrategy::OPENMP_SIMD: return ParallelStrategy::OPENMP_SIMD;
            case IntelligentParallelStrategy::PTHREAD_PARALLEL: return ParallelStrategy::PTHREAD_PARALLEL;
            case IntelligentParallelStrategy::MPI_DISTRIBUTED: return ParallelStrategy::MPI_DISTRIBUTED;
            case IntelligentParallelStrategy::CUDA_GPU: return ParallelStrategy::CUDA_GPU;
            case IntelligentParallelStrategy::HYBRID_CPU_GPU: return ParallelStrategy::HYBRID_CPU_GPU;
            case IntelligentParallelStrategy::HYBRID_ALL: return ParallelStrategy::HYBRID_ALL;
            default: return ParallelStrategy::OPENMP_PARALLEL;
        }
    }
    
    // 预计算表
    std::vector<uint32_t> twiddle_factors_;
    std::vector<uint32_t> inv_twiddle_factors_;
    std::vector<int> bit_reverse_table_;
    
    // 完整的Barrett约简参数和方法
    struct BarrettParams {
        uint32_t mod;
        uint64_t inv;

        explicit BarrettParams(uint32_t m = 0) : mod(m) {
            if (m > 0) {
                inv = (static_cast<uint64_t>(1) << 32) / m;
            }
        }

        inline uint32_t reduce(uint64_t x) const {
            uint64_t q = (x * inv) >> 32;
            uint64_t r = x - q * mod;
            if (r >= mod) r -= mod;
            return static_cast<uint32_t>(r);
        }

        inline uint32_t mul(uint32_t a, uint32_t b) const {
            return reduce(static_cast<uint64_t>(a) * b);
        }

        inline uint32_t add(uint32_t a, uint32_t b) const {
            uint32_t s = a + b;
            return s >= mod ? s - mod : s;
        }

        inline uint32_t sub(uint32_t a, uint32_t b) const {
            return a >= b ? a - b : a + mod - b;
        }
    };
    BarrettParams barrett_params_;
    
    // Montgomery约简参数
    struct MontgomeryParams {
        uint64_t mod;
        uint64_t r;
        uint64_t r_inv;
        uint64_t mod_inv;
    };
    MontgomeryParams montgomery_params_;

    // CUDA Montgomery约简参数 (GPU兼容)
    struct CudaMontgomeryParams {
        unsigned int mod;
        unsigned int mod_prime;
        unsigned int r2_mod;

        CudaMontgomeryParams(unsigned int m = 0) : mod(m), mod_prime(0), r2_mod(0) {
            if (m > 0) {
                // 计算Montgomery约简所需参数
                // 计算-m^(-1) mod 2^32 使用Newton迭代法
                unsigned long long p_inv = m;
                for (int i = 0; i < 5; ++i) {
                    p_inv = p_inv * (2 - m * p_inv);
                }
                mod_prime = (unsigned int)(-p_inv);

                // 计算R^2 mod m，其中R=2^32
                unsigned long long r2 = 1ULL << 32;
                r2 %= m;
                r2 = (r2 * r2) % m;
                r2_mod = (unsigned int)r2;
            }
        }

        inline unsigned int reduce(unsigned long long x) const {
            unsigned int q = (unsigned int)x * mod_prime;
            unsigned long long t = x + (unsigned long long)q * mod;
            unsigned int res = (unsigned int)(t >> 32);
            return (res >= mod) ? res - mod : res;
        }

        inline unsigned int to_montgomery(unsigned int x) const {
            return reduce((unsigned long long)x * r2_mod);
        }

        inline unsigned int from_montgomery(unsigned int x) const {
            return reduce(x);
        }

        inline unsigned int mul(unsigned int a, unsigned int b) const {
            return reduce((unsigned long long)a * b);
        }

        inline unsigned int add(unsigned int a, unsigned int b) const {
            unsigned int sum = a + b;
            return (sum >= mod) ? sum - mod : sum;
        }

        inline unsigned int sub(unsigned int a, unsigned int b) const {
            return (a >= b) ? (a - b) : (a - b + mod);
        }
    };

    // CRT多模数参数
    struct CRTParams {
        std::vector<int> moduli;
        std::vector<long long> M_values;  // M_i = M / p_i
        std::vector<long long> inv_values; // M_i^(-1) mod p_i
        long long total_M;  // 所有模数的乘积

        CRTParams() {
            // 使用三个NTT友好的模数
            moduli = {998244353, 1004535809, 469762049};
            total_M = 1LL;
            for (int mod : moduli) {
                total_M *= mod;
            }

            // 计算CRT参数
            M_values.resize(moduli.size());
            inv_values.resize(moduli.size());
            for (size_t i = 0; i < moduli.size(); ++i) {
                M_values[i] = total_M / moduli[i];
                inv_values[i] = computeModularInverse(M_values[i] % moduli[i], moduli[i]);
            }
        }

        // 静态模逆元计算函数
        static long long computeModularInverse(long long a, long long m) {
            long long m0 = m, x0 = 0, x1 = 1;
            if (m == 1) return 0;
            while (a > 1) {
                long long q = a / m;
                long long t = m;
                m = a % m;
                a = t;
                t = x0;
                x0 = x1 - q * x0;
                x1 = t;
            }
            if (x1 < 0) x1 += m0;
            return x1;
        }

        // CRT重构
        long long reconstruct(const std::vector<int>& remainders) const {
            long long result = 0;
            for (size_t i = 0; i < moduli.size(); ++i) {
                long long term = (1LL * remainders[i] * M_values[i] % total_M * inv_values[i]) % total_M;
                result = (result + term) % total_M;
            }
            return result;
        }
    };
    CRTParams crt_params_;

    // Work-Stealing任务队列
    struct WorkStealingTask {
        int start, end, level;
        uint32_t omega;
        bool processed;

        WorkStealingTask(int s = 0, int e = 0, int l = 0, uint32_t w = 1)
            : start(s), end(e), level(l), omega(w), processed(false) {}
    };

public:
    CompleteNTTEngine(std::shared_ptr<HardwareDetector> detector,
                     std::shared_ptr<StrategySelector> selector)
        : hardwareDetector_(detector), strategySelector_(selector) {}
    
    /**
     * @brief 执行NTT变换
     */
    void executeNTT(std::vector<int>& data, int modulus, bool inverse = false,
                   NTTVariant variant = NTTVariant::RADIX_2_DIT,
                   ReductionType reduction = ReductionType::STANDARD,
                   ParallelStrategy strategy = ParallelStrategy::HYBRID_ALL);

    /**
     * @brief 使用智能优化策略执行NTT变换
     */
    bool computeNTT(std::vector<int>& data, int modulus, bool inverse,
                   const IntelligentOptimizationStrategy& strategy) {
        // 转换智能策略到传统策略
        NTTVariant variant = convertToNTTVariant(strategy.nttVariant);
        ReductionType reduction = convertToReductionType(strategy.reductionType);
        ParallelStrategy parallelStrategy = convertToParallelStrategy(strategy.parallelStrategy);

        try {
            executeNTT(data, modulus, inverse, variant, reduction, parallelStrategy);
            return true;
        } catch (...) {
            return false;
        }
    }
    
    /**
     * @brief 多项式乘法（使用NTT）
     */
    void polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b,
                           std::vector<int>& result, int modulus);

    /**
     * @brief 多项式乘法（使用指定策略和变体）
     */
    void polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b,
                           std::vector<int>& result, int modulus,
                           ParallelStrategy strategy, NTTVariant variant);

    /**
     * @brief Split-Radix NTT实现 - 理论最优复杂度
     */
    void split_radix_ntt(std::vector<int>& data, int modulus, bool inverse);
    void split_radix_parallel_ntt(std::vector<int>& data, int modulus, bool inverse, int num_threads);

    /**
     * @brief Cache-Oblivious NTT实现 - 内存局部性优化
     */
    void cache_oblivious_ntt(std::vector<int>& data, int modulus, bool inverse);
    void recursive_cache_ntt(std::vector<int>& data, int start, int n, int stride,
                            uint32_t omega, int modulus, bool inverse);

    /**
     * @brief Work-Stealing并行NTT实现 - 动态负载均衡
     */
    void work_stealing_ntt(std::vector<int>& data, int modulus, bool inverse);

    /**
     * @brief CRT多模数并行NTT实现
     */
    void crt_multi_modulus_ntt(std::vector<int>& data, bool inverse);
    void crt_merge_results(std::vector<std::vector<int>>& results, std::vector<int>& final_result);

private:
    // ============ 核心算法实现 ============
    
    /**
     * @brief Radix-2 DIT NTT实现
     */
    void radix2_dit_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-2 DIF NTT实现  
     */
    void radix2_dif_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-4 DIT NTT实现
     */
    void radix4_dit_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief Radix-4 DIF NTT实现
     */
    void radix4_dif_ntt(std::vector<int>& data, int modulus, bool inverse);

    // 新算法变体函数已移至public部分
    
    // ============ 并行实现 ============
    
    /**
     * @brief OpenMP数据并行实现
     */
    void openmp_data_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief OpenMP任务并行实现
     */
    void openmp_task_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief SIMD向量化实现（AVX）
     */
    void simd_avx_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief SIMD向量化实现（NEON）
     */
    void simd_neon_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief MPI分布式并行实现
     */
    void mpi_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    
    /**
     * @brief CUDA GPU并行实现 - 完整Montgomery约简版本
     */
    void cuda_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    void cuda_montgomery_ntt(std::vector<int>& data, int modulus, bool inverse);
    void cuda_bit_reverse(std::vector<int>& data);
    void cuda_precompute_twiddles(std::vector<int>& twiddles, int n, int modulus, bool inverse);

#ifdef USE_CUDA
    // CUDA kernel函数声明
    __global__ void cuda_bit_reverse_kernel(int *data, const int *rev, int n);
    __global__ void cuda_to_montgomery_kernel(int *data, CudaMontgomeryParams mont, int n);
    __global__ void cuda_ntt_montgomery_kernel(int *data, const int *twiddles, int len,
                                             CudaMontgomeryParams mont, int n, int offset);
    __global__ void cuda_final_scaling_kernel(int *data, unsigned int factor_mont,
                                             CudaMontgomeryParams mont, int n);
    __global__ void cuda_from_montgomery_kernel(int *data, CudaMontgomeryParams mont, int n);
#endif
    
    /**
     * @brief pthread多线程实现 - 完整Radix-4优化版本
     */
    void pthread_parallel_ntt(std::vector<int>& data, int modulus, bool inverse);
    void pthread_radix4_ntt(std::vector<int>& data, int modulus, bool inverse, int num_threads);
    void pthread_digit_reverse_4(std::vector<int>& data, int num_threads);
    template<typename F>
    void pthread_parallel_for(int begin, int end, int step, const F& func, int num_threads);
    
    // ============ 工具函数 ============
    
    /**
     * @brief 预计算旋转因子
     */
    void precomputeTwiddleFactors(int n, int modulus);
    
    /**
     * @brief 预计算位反转表
     */
    void precomputeBitReverseTable(int n);
    
    /**
     * @brief 初始化Barrett约简参数
     */
    void initBarrettParams(int modulus);
    
    /**
     * @brief 初始化Montgomery约简参数
     */
    void initMontgomeryParams(int modulus);
    
    /**
     * @brief 快速幂运算
     */
    uint32_t fastPow(uint32_t base, uint32_t exp, uint32_t mod);
    
    /**
     * @brief 模逆元计算
     */
    uint32_t modularInverse(uint32_t a, uint32_t mod);
    
    /**
     * @brief Barrett约简
     */
    uint32_t barrettReduce(uint64_t x);
    
    /**
     * @brief Montgomery约简
     */
    uint32_t montgomeryReduce(uint64_t x);
    
    /**
     * @brief 位反转置换
     */
    void bitReversePermutation(std::vector<int>& data);
    
    /**
     * @brief 蝶形运算（标准）
     */
    void butterfly(int& a, int& b, uint32_t w, uint32_t mod);
    
    /**
     * @brief 蝶形运算（Barrett约简）
     */
    void butterflyBarrett(int& a, int& b, uint32_t w);
    
    /**
     * @brief 蝶形运算（Montgomery约简）
     */
    void butterflyMontgomery(int& a, int& b, uint32_t w);
};

} // namespace HybridNTT
