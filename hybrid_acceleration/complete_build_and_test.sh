#!/bin/bash

# =============================================================================
# 完整的混合并行NTT构建和测试脚本
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "Checking dependencies..."
    
    # 检查编译器
    if ! command -v g++ &> /dev/null; then
        print_error "g++ not found. Please install g++."
        exit 1
    fi
    
    # 检查OpenMP支持
    if ! echo '#include <omp.h>' | g++ -fopenmp -x c++ -c - -o /dev/null 2>/dev/null; then
        print_warning "OpenMP not available. Some parallel features will be disabled."
    else
        print_success "OpenMP support detected"
    fi
    
    # 检查SIMD支持
    if g++ -march=native -dM -E - < /dev/null | grep -q "__AVX2__"; then
        print_success "AVX2 support detected"
    elif g++ -march=native -dM -E - < /dev/null | grep -q "__AVX__"; then
        print_success "AVX support detected"
    else
        print_warning "No AVX support detected"
    fi
    
    # 检查NEON支持（ARM）
    if g++ -march=native -dM -E - < /dev/null | grep -q "__ARM_NEON"; then
        print_success "NEON support detected"
    fi
    
    # 检查MPI
    if command -v mpirun &> /dev/null; then
        print_success "MPI support detected"
    else
        print_warning "MPI not available. Distributed parallel features will be disabled."
    fi
    
    # 检查CUDA
    if command -v nvcc &> /dev/null; then
        print_success "CUDA support detected"
    else
        print_warning "CUDA not available. GPU acceleration will be disabled."
    fi
    
    print_success "Dependency check completed"
}

# 创建目录结构
setup_directories() {
    print_info "Setting up directory structure..."
    
    mkdir -p bin
    mkdir -p logs
    mkdir -p results
    mkdir -p charts
    
    print_success "Directory structure created"
}

# 编译完整的NTT引擎
compile_complete_engine() {
    print_info "Compiling complete NTT engine..."
    
    # 基本编译选项
    CXX_FLAGS="-std=c++17 -O3 -march=native -Wall -Wextra"
    
    # 添加OpenMP支持
    if command -v g++ &> /dev/null && echo '#include <omp.h>' | g++ -fopenmp -x c++ -c - -o /dev/null 2>/dev/null; then
        CXX_FLAGS="$CXX_FLAGS -fopenmp"
    fi
    
    # 添加pthread支持
    CXX_FLAGS="$CXX_FLAGS -pthread"
    
    # 编译完整测试框架
    print_info "Compiling complete test framework..."
    g++ $CXX_FLAGS \
        complete_test_framework.cpp \
        src/algorithms/complete_ntt_engine.cpp \
        src/algorithms/complete_parallel_strategies.cpp \
        -I. \
        -o bin/complete_test_framework
    
    if [ $? -eq 0 ]; then
        print_success "Complete test framework compiled successfully"
    else
        print_error "Failed to compile complete test framework"
        exit 1
    fi
}

# 运行正确性测试
run_correctness_tests() {
    print_info "Running correctness tests..."
    
    if [ ! -f "bin/complete_test_framework" ]; then
        print_error "Test framework not found. Please compile first."
        exit 1
    fi
    
    # 运行测试并保存日志
    ./bin/complete_test_framework 2>&1 | tee logs/correctness_test.log
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_success "Correctness tests completed"
    else
        print_error "Correctness tests failed"
        exit 1
    fi
}

# 运行性能基准测试
run_performance_benchmark() {
    print_info "Running performance benchmark..."
    
    # 创建性能测试程序
    cat > performance_benchmark.cpp << 'EOF'
#include "src/algorithms/complete_ntt_engine.hpp"
#include "src/core/hardware_detector.hpp"
#include "src/core/strategy_selector.hpp"
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <iomanip>

using namespace HybridNTT;

int main() {
    auto detector = std::make_shared<HardwareDetector>();
    auto selector = std::make_shared<StrategySelector>(detector);
    CompleteNTTEngine engine(detector, selector);
    
    std::vector<int> sizes = {256, 512, 1024, 2048, 4096, 8192, 16384};
    int modulus = 998244353;
    
    std::ofstream csv("results/performance_results.csv");
    csv << "Size,Strategy,Time_us,Throughput_MBps\n";
    
    for (int n : sizes) {
        std::vector<int> a(n), b(n);
        for (int i = 0; i < n; i++) {
            a[i] = rand() % modulus;
            b[i] = rand() % modulus;
        }
        
        std::vector<CompleteNTTEngine::ParallelStrategy> strategies = {
            CompleteNTTEngine::ParallelStrategy::SERIAL,
            CompleteNTTEngine::ParallelStrategy::OPENMP_DATA_PARALLEL,
            CompleteNTTEngine::ParallelStrategy::SIMD_AVX,
            CompleteNTTEngine::ParallelStrategy::PTHREAD_PARALLEL
        };
        
        std::vector<std::string> strategy_names = {
            "Serial", "OpenMP", "SIMD_AVX", "pthread"
        };
        
        for (size_t i = 0; i < strategies.size(); i++) {
            std::vector<int> result;
            
            auto start = std::chrono::high_resolution_clock::now();
            engine.polynomialMultiply(a, b, result, modulus);
            auto end = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            double throughput = (n * sizeof(int) * 2) / (duration.count() / 1e6) / (1024 * 1024);
            
            csv << n << "," << strategy_names[i] << "," << duration.count() << "," << throughput << "\n";
            
            std::cout << "n=" << n << " " << strategy_names[i] << ": " 
                     << duration.count() << "μs, " << throughput << " MB/s" << std::endl;
        }
    }
    
    csv.close();
    return 0;
}
EOF
    
    # 编译性能测试
    g++ -std=c++17 -O3 -march=native -fopenmp -pthread \
        performance_benchmark.cpp \
        src/algorithms/complete_ntt_engine.cpp \
        src/algorithms/complete_parallel_strategies.cpp \
        -I. \
        -o bin/performance_benchmark
    
    if [ $? -eq 0 ]; then
        print_success "Performance benchmark compiled"
        ./bin/performance_benchmark 2>&1 | tee logs/performance_benchmark.log
    else
        print_error "Failed to compile performance benchmark"
    fi
}

# 生成性能图表
generate_charts() {
    print_info "Generating performance charts..."
    
    # 创建Python绘图脚本
    cat > generate_charts.py << 'EOF'
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 读取性能数据
try:
    df = pd.read_csv('results/performance_results.csv')
except FileNotFoundError:
    print("Performance results not found. Please run benchmark first.")
    exit(1)

# 1. 性能对比图
plt.figure(figsize=(12, 8))
for strategy in df['Strategy'].unique():
    strategy_data = df[df['Strategy'] == strategy]
    plt.plot(strategy_data['Size'], strategy_data['Time_us'], 
             marker='o', linewidth=2, label=strategy)

plt.xlabel('Problem Size (n)', fontsize=12)
plt.ylabel('Execution Time (μs)', fontsize=12)
plt.title('NTT Performance Comparison Across Different Parallel Strategies', fontsize=14)
plt.legend(fontsize=10)
plt.grid(True, alpha=0.3)
plt.yscale('log')
plt.xscale('log')
plt.tight_layout()
plt.savefig('charts/performance_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 2. 吞吐量对比图
plt.figure(figsize=(12, 8))
for strategy in df['Strategy'].unique():
    strategy_data = df[df['Strategy'] == strategy]
    plt.plot(strategy_data['Size'], strategy_data['Throughput_MBps'], 
             marker='s', linewidth=2, label=strategy)

plt.xlabel('Problem Size (n)', fontsize=12)
plt.ylabel('Throughput (MB/s)', fontsize=12)
plt.title('NTT Throughput Comparison Across Different Parallel Strategies', fontsize=14)
plt.legend(fontsize=10)
plt.grid(True, alpha=0.3)
plt.xscale('log')
plt.tight_layout()
plt.savefig('charts/throughput_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 3. 加速比图
serial_data = df[df['Strategy'] == 'Serial'].set_index('Size')['Time_us']
plt.figure(figsize=(12, 8))

for strategy in df['Strategy'].unique():
    if strategy == 'Serial':
        continue
    strategy_data = df[df['Strategy'] == strategy].set_index('Size')['Time_us']
    speedup = serial_data / strategy_data
    plt.plot(speedup.index, speedup.values, 
             marker='^', linewidth=2, label=f'{strategy} vs Serial')

plt.xlabel('Problem Size (n)', fontsize=12)
plt.ylabel('Speedup', fontsize=12)
plt.title('Speedup Comparison Against Serial Implementation', fontsize=14)
plt.legend(fontsize=10)
plt.grid(True, alpha=0.3)
plt.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Baseline (Serial)')
plt.xscale('log')
plt.tight_layout()
plt.savefig('charts/speedup_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 4. 热力图
pivot_df = df.pivot(index='Size', columns='Strategy', values='Time_us')
plt.figure(figsize=(10, 8))
sns.heatmap(pivot_df, annot=True, fmt='.0f', cmap='YlOrRd', 
            cbar_kws={'label': 'Execution Time (μs)'})
plt.title('Performance Heatmap: Execution Time by Strategy and Problem Size', fontsize=14)
plt.xlabel('Parallel Strategy', fontsize=12)
plt.ylabel('Problem Size (n)', fontsize=12)
plt.tight_layout()
plt.savefig('charts/performance_heatmap.png', dpi=300, bbox_inches='tight')
plt.close()

print("Charts generated successfully in charts/ directory")
EOF
    
    # 运行绘图脚本
    if command -v python3 &> /dev/null; then
        python3 -c "import matplotlib, seaborn, pandas" 2>/dev/null
        if [ $? -eq 0 ]; then
            python3 generate_charts.py
            print_success "Performance charts generated"
        else
            print_warning "Python plotting libraries not available. Skipping chart generation."
        fi
    else
        print_warning "Python3 not available. Skipping chart generation."
    fi
}

# 清理函数
cleanup() {
    print_info "Cleaning up temporary files..."
    rm -f performance_benchmark.cpp generate_charts.py
    print_success "Cleanup completed"
}

# 显示帮助信息
show_help() {
    echo "Complete Hybrid Parallel NTT Build and Test Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --check-deps     Check dependencies only"
    echo "  --compile        Compile only"
    echo "  --test           Run correctness tests only"
    echo "  --benchmark      Run performance benchmark only"
    echo "  --charts         Generate charts only"
    echo "  --full           Run complete pipeline (default)"
    echo "  --clean          Clean build artifacts"
    echo "  --help           Show this help message"
    echo ""
}

# 清理构建产物
clean_build() {
    print_info "Cleaning build artifacts..."
    rm -rf bin logs results charts
    rm -f *.o *.so *.a
    print_success "Build artifacts cleaned"
}

# 主函数
main() {
    echo "=============================================="
    echo "  Complete Hybrid Parallel NTT Framework"
    echo "=============================================="
    echo ""
    
    case "${1:---full}" in
        --check-deps)
            check_dependencies
            ;;
        --compile)
            check_dependencies
            setup_directories
            compile_complete_engine
            ;;
        --test)
            run_correctness_tests
            ;;
        --benchmark)
            run_performance_benchmark
            ;;
        --charts)
            generate_charts
            ;;
        --full)
            check_dependencies
            setup_directories
            compile_complete_engine
            run_correctness_tests
            run_performance_benchmark
            generate_charts
            cleanup
            print_success "Complete pipeline finished successfully!"
            ;;
        --clean)
            clean_build
            ;;
        --help)
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
