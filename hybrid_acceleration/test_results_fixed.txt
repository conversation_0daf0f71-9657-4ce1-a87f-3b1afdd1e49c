Found 30 test cases in nttdata/
Loaded test case 0: n=4, p=998244353
Loaded test case 1: n=8, p=1004535809
Loaded test case 2: n=16, p=469762049
Loaded test case 3: n=32, p=985661441
Loaded test case 4: n=64, p=998244353
Loaded test case 5: n=128, p=1004535809
Loaded test case 6: n=256, p=469762049
Loaded test case 7: n=512, p=998244353
Loaded test case 8: n=1024, p=1004535809
Loaded test case 9: n=2048, p=998244353
Loaded test case 10: n=4096, p=998244353
Loaded test case 11: n=8192, p=1004535809
Loaded test case 12: n=16384, p=998244353
Loaded test case 13: n=100, p=998244353
Loaded test case 14: n=1000, p=1004535809
Loaded test case 15: n=1, p=998244353
Loaded test case 16: n=2, p=1004535809
Loaded test case 17: n=3, p=469762049
Loaded test case 18: n=32768, p=998244353
Loaded test case 19: n=65536, p=998244353
Loaded test case 20: n=256, p=754974721
Loaded test case 21: n=1024, p=5767169
Loaded test case 22: n=256, p=998244353
Loaded test case 23: n=128, p=7340033
Loaded test case 24: n=64, p=5767169
Loaded test case 25: n=64, p=469762049
Loaded test case 26: n=64, p=985661441
Loaded test case 27: n=128, p=1004535809
Loaded test case 28: n=64, p=985661441
Loaded test case 29: n=64, p=1004535809

============================================================
           CORRECTNESS TESTING
============================================================

=== Testing Serial Radix-2 DIT ===
Test case 0: ✓ PASS (n=4, time=20μs)
Test case 1: ✓ PASS (n=8, time=26μs)
Test case 2: ✓ PASS (n=16, time=36μs)
Test case 3: ✓ PASS (n=32, time=62μs)
Test case 4: ✓ PASS (n=64, time=116μs)
Test case 5: ✓ PASS (n=128, time=208μs)
Test case 6: ✓ PASS (n=256, time=390μs)
Test case 7: ✓ PASS (n=512, time=782μs)
Test case 8: ✓ PASS (n=1024, time=1640μs)
Test case 9: ✓ PASS (n=2048, time=3419μs)
Test case 10: ✓ PASS (n=4096, time=5619μs)
Test case 11: ✓ PASS (n=8192, time=9626μs)
Test case 12: ✓ PASS (n=16384, time=18088μs)
Test case 13: ✓ PASS (n=100, time=110μs)
Test case 14: ✓ PASS (n=1000, time=904μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=9μs)
Test case 18: ✓ PASS (n=32768, time=38242μs)
Test case 19: ✓ PASS (n=65536, time=80051μs)
Test case 20: ✗ FAIL (n=256, time=213μs)
Test case 21: ✓ PASS (n=1024, time=898μs)
Test case 22: ✓ PASS (n=256, time=216μs)
Test case 23: ✓ PASS (n=128, time=108μs)
Test case 24: ✓ PASS (n=64, time=59μs)
Test case 25: ✓ PASS (n=64, time=56μs)
Test case 26: ✓ PASS (n=64, time=55μs)
Test case 27: ✓ PASS (n=128, time=107μs)
Test case 28: ✓ PASS (n=64, time=58μs)
Test case 29: ✓ PASS (n=64, time=56μs)

=== Testing Serial Radix-2 DIF ===
Test case 0: ✓ PASS (n=4, time=10μs)
Test case 1: ✓ PASS (n=8, time=13μs)
Test case 2: ✓ PASS (n=16, time=19μs)
Test case 3: ✓ PASS (n=32, time=32μs)
Test case 4: ✓ PASS (n=64, time=55μs)
Test case 5: ✓ PASS (n=128, time=100μs)
Test case 6: ✓ PASS (n=256, time=196μs)
Test case 7: ✓ PASS (n=512, time=398μs)
Test case 8: ✓ PASS (n=1024, time=809μs)
Test case 9: ✓ PASS (n=2048, time=1695μs)
Test case 10: ✓ PASS (n=4096, time=3549μs)
Test case 11: ✓ PASS (n=8192, time=7540μs)
Test case 12: ✓ PASS (n=16384, time=15879μs)
Test case 13: ✓ PASS (n=100, time=104μs)
Test case 14: ✓ PASS (n=1000, time=812μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=9μs)
Test case 18: ✓ PASS (n=32768, time=33374μs)
Test case 19: ✓ PASS (n=65536, time=69798μs)
Test case 20: ✗ FAIL (n=256, time=185μs)
Test case 21: ✓ PASS (n=1024, time=807μs)
Test case 22: ✓ PASS (n=256, time=197μs)
Test case 23: ✓ PASS (n=128, time=100μs)
Test case 24: ✓ PASS (n=64, time=55μs)
Test case 25: ✓ PASS (n=64, time=53μs)
Test case 26: ✓ PASS (n=64, time=52μs)
Test case 27: ✓ PASS (n=128, time=100μs)
Test case 28: ✓ PASS (n=64, time=54μs)
Test case 29: ✓ PASS (n=64, time=52μs)

=== Testing Serial Radix-4 DIT ===
Test case 0: ✗ FAIL (n=4, time=9μs)
Test case 1: ✗ FAIL (n=8, time=12μs)
Test case 2: ✗ FAIL (n=16, time=16μs)
Test case 3: ✗ FAIL (n=32, time=30μs)
Test case 4: ✗ FAIL (n=64, time=49μs)
Test case 5: ✗ FAIL (n=128, time=99μs)
Test case 6: ✗ FAIL (n=256, time=191μs)
Test case 7: ✗ FAIL (n=512, time=393μs)
Test case 8: ✗ FAIL (n=1024, time=770μs)
Test case 9: ✗ FAIL (n=2048, time=1689μs)
Test case 10: ✗ FAIL (n=4096, time=3374μs)
Test case 11: ✗ FAIL (n=8192, time=7451μs)
Test case 12: ✗ FAIL (n=16384, time=15068μs)
Test case 13: ✗ FAIL (n=100, time=103μs)
Test case 14: ✗ FAIL (n=1000, time=768μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✗ FAIL (n=2, time=7μs)
Test case 17: ✗ FAIL (n=3, time=8μs)
Test case 18: ✗ FAIL (n=32768, time=32818μs)
Test case 19: ✗ FAIL (n=65536, time=65963μs)
Test case 20: ✗ FAIL (n=256, time=174μs)
Test case 21: ✗ FAIL (n=1024, time=770μs)
Test case 22: ✗ FAIL (n=256, time=186μs)
Test case 23: ✗ FAIL (n=128, time=99μs)
Test case 24: ✗ FAIL (n=64, time=51μs)
Test case 25: ✗ FAIL (n=64, time=47μs)
Test case 26: ✗ FAIL (n=64, time=47μs)
Test case 27: ✗ FAIL (n=128, time=99μs)
Test case 28: ✗ FAIL (n=64, time=49μs)
Test case 29: ✗ FAIL (n=64, time=47μs)

=== Testing Serial Radix-4 DIF ===
Test case 0: ✗ FAIL (n=4, time=6μs)
Test case 1: ✗ FAIL (n=8, time=10μs)
Test case 2: ✗ FAIL (n=16, time=13μs)
Test case 3: ✗ FAIL (n=32, time=26μs)
Test case 4: ✗ FAIL (n=64, time=41μs)
Test case 5: ✗ FAIL (n=128, time=94μs)
Test case 6: ✗ FAIL (n=256, time=165μs)
Test case 7: ✗ FAIL (n=512, time=354μs)
Test case 8: ✗ FAIL (n=1024, time=690μs)
Test case 9: ✗ FAIL (n=2048, time=1529μs)
Test case 10: ✗ FAIL (n=4096, time=3042μs)
Test case 11: ✗ FAIL (n=8192, time=6741μs)
Test case 12: ✗ FAIL (n=16384, time=13599μs)
Test case 13: ✗ FAIL (n=100, time=91μs)
Test case 14: ✗ FAIL (n=1000, time=690μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✗ FAIL (n=2, time=6μs)
Test case 17: ✗ FAIL (n=3, time=6μs)
Test case 18: ✗ FAIL (n=32768, time=29822μs)
Test case 19: ✗ FAIL (n=65536, time=59893μs)
Test case 20: ✗ FAIL (n=256, time=153μs)
Test case 21: ✗ FAIL (n=1024, time=694μs)
Test case 22: ✗ FAIL (n=256, time=166μs)
Test case 23: ✗ FAIL (n=128, time=91μs)
Test case 24: ✗ FAIL (n=64, time=45μs)
Test case 25: ✗ FAIL (n=64, time=40μs)
Test case 26: ✗ FAIL (n=64, time=39μs)
Test case 27: ✗ FAIL (n=128, time=90μs)
Test case 28: ✗ FAIL (n=64, time=42μs)
Test case 29: ✗ FAIL (n=64, time=39μs)

=== Testing Serial Split-Radix ===
Test case 0: ✓ PASS (n=4, time=19μs)
Test case 1: ✓ PASS (n=8, time=17μs)
Test case 2: ✓ PASS (n=16, time=24μs)
Test case 3: ✓ PASS (n=32, time=39μs)
Test case 4: ✓ PASS (n=64, time=64μs)
Test case 5: ✓ PASS (n=128, time=114μs)
Test case 6: ✓ PASS (n=256, time=219μs)
Test case 7: ✓ PASS (n=512, time=441μs)
Test case 8: ✓ PASS (n=1024, time=907μs)
Test case 9: ✓ PASS (n=2048, time=1901μs)
Test case 10: ✓ PASS (n=4096, time=3990μs)
Test case 11: ✓ PASS (n=8192, time=8481μs)
Test case 12: ✓ PASS (n=16384, time=17877μs)
Test case 13: ✓ PASS (n=100, time=116μs)
Test case 14: ✓ PASS (n=1000, time=907μs)
Test case 15: ✓ PASS (n=1, time=4μs)
Test case 16: ✓ PASS (n=2, time=9μs)
Test case 17: ✓ PASS (n=3, time=12μs)
Test case 18: ✓ PASS (n=32768, time=37640μs)
Test case 19: ✓ PASS (n=65536, time=78850μs)
Test case 20: ✗ FAIL (n=256, time=208μs)
Test case 21: ✓ PASS (n=1024, time=903μs)
Test case 22: ✓ PASS (n=256, time=220μs)
Test case 23: ✓ PASS (n=128, time=112μs)
Test case 24: ✓ PASS (n=64, time=62μs)
Test case 25: ✓ PASS (n=64, time=60μs)
Test case 26: ✓ PASS (n=64, time=59μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=62μs)
Test case 29: ✓ PASS (n=64, time=60μs)

=== Testing Serial Cache-Oblivious ===
Test case 0: ✓ PASS (n=4, time=10μs)
Test case 1: ✓ PASS (n=8, time=14μs)
Test case 2: ✓ PASS (n=16, time=20μs)
Test case 3: ✓ PASS (n=32, time=34μs)
Test case 4: ✓ PASS (n=64, time=58μs)
Test case 5: ✓ PASS (n=128, time=114μs)
Test case 6: ✓ PASS (n=256, time=214μs)
Test case 7: ✓ PASS (n=512, time=435μs)
Test case 8: ✓ PASS (n=1024, time=905μs)
Test case 9: ✓ PASS (n=2048, time=1892μs)
Test case 10: ✓ PASS (n=4096, time=3979μs)
Test case 11: ✓ PASS (n=8192, time=8446μs)
Test case 12: ✓ PASS (n=16384, time=17877μs)
Test case 13: ✓ PASS (n=100, time=113μs)
Test case 14: ✓ PASS (n=1000, time=902μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=7μs)
Test case 17: ✓ PASS (n=3, time=9μs)
Test case 18: ✓ PASS (n=32768, time=37592μs)
Test case 19: ✓ PASS (n=65536, time=78827μs)
Test case 20: ✗ FAIL (n=256, time=202μs)
Test case 21: ✓ PASS (n=1024, time=896μs)
Test case 22: ✓ PASS (n=256, time=215μs)
Test case 23: ✓ PASS (n=128, time=107μs)
Test case 24: ✓ PASS (n=64, time=58μs)
Test case 25: ✓ PASS (n=64, time=56μs)
Test case 26: ✓ PASS (n=64, time=55μs)
Test case 27: ✓ PASS (n=128, time=112μs)
Test case 28: ✓ PASS (n=64, time=58μs)
Test case 29: ✓ PASS (n=64, time=55μs)

=== Testing OpenMP Data Parallel ===
Test case 0: ✓ PASS (n=4, time=3780μs)
Test case 1: ✓ PASS (n=8, time=220μs)
Test case 2: ✓ PASS (n=16, time=143μs)
Test case 3: ✓ PASS (n=32, time=218μs)
Test case 4: ✓ PASS (n=64, time=197μs)
Test case 5: ✓ PASS (n=128, time=300μs)
Test case 6: ✓ PASS (n=256, time=371μs)
Test case 7: ✓ PASS (n=512, time=570μs)
Test case 8: ✓ PASS (n=1024, time=927μs)
Test case 9: ✓ PASS (n=2048, time=1898μs)
Test case 10: ✓ PASS (n=4096, time=3484μs)
Test case 11: ✓ PASS (n=8192, time=5680μs)
Test case 12: ✓ PASS (n=16384, time=10234μs)
Test case 13: ✓ PASS (n=100, time=207μs)
Test case 14: ✓ PASS (n=1000, time=681μs)
Test case 15: ✓ PASS (n=1, time=58μs)
Test case 16: ✓ PASS (n=2, time=102μs)
Test case 17: ✓ PASS (n=3, time=84μs)
Test case 18: ✓ PASS (n=32768, time=18498μs)
Test case 19: ✓ PASS (n=65536, time=105958μs)
Test case 20: ✗ FAIL (n=256, time=241μs)
Test case 21: ✓ PASS (n=1024, time=634μs)
Test case 22: ✓ PASS (n=256, time=234μs)
Test case 23: ✓ PASS (n=128, time=147μs)
Test case 24: ✓ PASS (n=64, time=128μs)
Test case 25: ✓ PASS (n=64, time=117μs)
Test case 26: ✓ PASS (n=64, time=106μs)
Test case 27: ✓ PASS (n=128, time=146μs)
Test case 28: ✓ PASS (n=64, time=118μs)
Test case 29: ✓ PASS (n=64, time=106μs)

=== Testing OpenMP Task Parallel ===
Test case 0: ✓ PASS (n=4, time=3419μs)
Test case 1: ✓ PASS (n=8, time=283μs)
Test case 2: ✓ PASS (n=16, time=363μs)
Test case 3: ✓ PASS (n=32, time=646μs)
Test case 4: ✓ PASS (n=64, time=1357μs)
Test case 5: ✓ PASS (n=128, time=2595μs)
Test case 6: ✓ PASS (n=256, time=5337μs)
Test case 7: ✓ PASS (n=512, time=20925μs)
Test case 8: ✓ PASS (n=1024, time=22125μs)
Test case 9: ✓ PASS (n=2048, time=42811μs)
Test case 10: ✓ PASS (n=4096, time=87574μs)
Test case 11: ✓ PASS (n=8192, time=171155μs)
Test case 12: ✓ PASS (n=16384, time=390822μs)
Test case 13: ✓ PASS (n=100, time=2707μs)
Test case 14: ✓ PASS (n=1000, time=21307μs)
Test case 15: ✓ PASS (n=1, time=6μs)
Test case 16: ✓ PASS (n=2, time=51μs)
Test case 17: ✓ PASS (n=3, time=121μs)
Test case 18: ✓ PASS (n=32768, time=678452μs)
Test case 19: ✓ PASS (n=65536, time=1416898μs)
Test case 20: ✗ FAIL (n=256, time=5512μs)
Test case 21: ✓ PASS (n=1024, time=42423μs)
Test case 22: ✓ PASS (n=256, time=64828μs)
Test case 23: ✓ PASS (n=128, time=177512μs)
Test case 24: ✓ PASS (n=64, time=166409μs)
Test case 25: ✓ PASS (n=64, time=71700μs)
Test case 26: ✓ PASS (n=64, time=51438μs)
Test case 27: ✓ PASS (n=128, time=85122μs)
Test case 28: ✓ PASS (n=64, time=1422μs)
Test case 29: ✓ PASS (n=64, time=1547μs)

=== Testing OpenMP Split-Radix ===
Test case 0: ✓ PASS (n=4, time=12μs)
Test case 1: ✓ PASS (n=8, time=16μs)
Test case 2: ✓ PASS (n=16, time=21μs)
Test case 3: ✓ PASS (n=32, time=35μs)
Test case 4: ✓ PASS (n=64, time=61μs)
Test case 5: ✓ PASS (n=128, time=123μs)
Test case 6: ✓ PASS (n=256, time=251μs)
Test case 7: ✓ PASS (n=512, time=518μs)
Test case 8: ✓ PASS (n=1024, time=1036μs)
Test case 9: ✓ PASS (n=2048, time=2071μs)
Test case 10: ✓ PASS (n=4096, time=4147μs)
Test case 11: ✓ PASS (n=8192, time=8386μs)
Test case 12: ✓ PASS (n=16384, time=26791μs)
Test case 13: ✓ PASS (n=100, time=124μs)
Test case 14: ✓ PASS (n=1000, time=1039μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=8μs)
Test case 17: ✓ PASS (n=3, time=10μs)
Test case 18: ✓ PASS (n=32768, time=71003μs)
Test case 19: ✓ PASS (n=65536, time=76241μs)
Test case 20: ✗ FAIL (n=256, time=300μs)
Test case 21: ✓ PASS (n=1024, time=1088μs)
Test case 22: ✓ PASS (n=256, time=252μs)
Test case 23: ✓ PASS (n=128, time=119μs)
Test case 24: ✓ PASS (n=64, time=58μs)
Test case 25: ✓ PASS (n=64, time=56μs)
Test case 26: ✓ PASS (n=64, time=56μs)
Test case 27: ✓ PASS (n=128, time=117μs)
Test case 28: ✓ PASS (n=64, time=56μs)
Test case 29: ✓ PASS (n=64, time=56μs)

=== Testing SIMD AVX ===
Test case 0: ✓ PASS (n=4, time=9μs)
Test case 1: ✗ FAIL (n=8, time=12μs)
Test case 2: ✗ FAIL (n=16, time=17μs)
Test case 3: ✗ FAIL (n=32, time=26μs)
Test case 4: ✗ FAIL (n=64, time=63μs)
Test case 5: ✗ FAIL (n=128, time=79μs)
Test case 6: ✗ FAIL (n=256, time=160μs)
Test case 7: ✗ FAIL (n=512, time=313μs)
Test case 8: ✗ FAIL (n=1024, time=651μs)
Test case 9: ✗ FAIL (n=2048, time=1289μs)
Test case 10: ✗ FAIL (n=4096, time=2289μs)
Test case 11: ✗ FAIL (n=8192, time=4544μs)
Test case 12: ✗ FAIL (n=16384, time=9292μs)
Test case 13: ✗ FAIL (n=100, time=73μs)
Test case 14: ✗ FAIL (n=1000, time=541μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✓ PASS (n=2, time=6μs)
Test case 17: ✓ PASS (n=3, time=7μs)
Test case 18: ✗ FAIL (n=32768, time=18824μs)
Test case 19: ✗ FAIL (n=65536, time=38141μs)
Test case 20: ✗ FAIL (n=256, time=141μs)
Test case 21: ✗ FAIL (n=1024, time=543μs)
Test case 22: ✗ FAIL (n=256, time=144μs)
Test case 23: ✗ FAIL (n=128, time=69μs)
Test case 24: ✗ FAIL (n=64, time=36μs)
Test case 25: ✗ FAIL (n=64, time=35μs)
Test case 26: ✗ FAIL (n=64, time=34μs)
Test case 27: ✗ FAIL (n=128, time=68μs)
Test case 28: ✗ FAIL (n=64, time=36μs)
Test case 29: ✗ FAIL (n=64, time=33μs)

=== Testing SIMD NEON ===
Test case 0: ✗ FAIL (n=4, time=9μs)
Test case 1: ✗ FAIL (n=8, time=12μs)
Test case 2: ✗ FAIL (n=16, time=16μs)
Test case 3: ✗ FAIL (n=32, time=28μs)
Test case 4: ✗ FAIL (n=64, time=47μs)
Test case 5: ✗ FAIL (n=128, time=97μs)
Test case 6: ✗ FAIL (n=256, time=211μs)
Test case 7: ✗ FAIL (n=512, time=433μs)
Test case 8: ✗ FAIL (n=1024, time=912μs)
Test case 9: ✗ FAIL (n=2048, time=1920μs)
Test case 10: ✗ FAIL (n=4096, time=4067μs)
Test case 11: ✗ FAIL (n=8192, time=8638μs)
Test case 12: ✗ FAIL (n=16384, time=18227μs)
Test case 13: ✗ FAIL (n=100, time=100μs)
Test case 14: ✗ FAIL (n=1000, time=921μs)
Test case 15: ✓ PASS (n=1, time=3μs)
Test case 16: ✗ FAIL (n=2, time=6μs)
Test case 17: ✗ FAIL (n=3, time=8μs)
Test case 18: ✗ FAIL (n=32768, time=38320μs)
Test case 19: ✗ FAIL (n=65536, time=80204μs)
Test case 20: ✗ FAIL (n=256, time=207μs)
Test case 21: ✗ FAIL (n=1024, time=907μs)
Test case 22: ✗ FAIL (n=256, time=216μs)
Test case 23: ✗ FAIL (n=128, time=99μs)
Test case 24: ✗ FAIL (n=64, time=47μs)
Test case 25: ✗ FAIL (n=64, time=45μs)
Test case 26: ✗ FAIL (n=64, time=45μs)
Test case 27: ✗ FAIL (n=128, time=97μs)
Test case 28: ✗ FAIL (n=64, time=47μs)
Test case 29: ✗ FAIL (n=64, time=45μs)

=== Testing pthread Parallel ===
Test case 0: ✗ FAIL (n=4, time=25μs)
Test case 1: ✓ PASS (n=8, time=707μs)
