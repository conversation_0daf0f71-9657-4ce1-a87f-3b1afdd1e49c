#!/usr/bin/env python3
"""
生成全面的NTT测试数据
包括各种规模和模数的测试用例，确保测试覆盖度
"""

import os
import random
import math

def is_prime(n):
    """检查是否为素数"""
    if n < 2:
        return False
    for i in range(2, int(math.sqrt(n)) + 1):
        if n % i == 0:
            return False
    return True

def find_primitive_root(p):
    """找到模p的原根"""
    if p == 2:
        return 1
    
    # 计算p-1的因子
    factors = []
    n = p - 1
    d = 2
    while d * d <= n:
        if n % d == 0:
            factors.append(d)
            while n % d == 0:
                n //= d
        d += 1
    if n > 1:
        factors.append(n)
    
    # 寻找原根
    for g in range(2, p):
        is_root = True
        for factor in factors:
            if pow(g, (p - 1) // factor, p) == 1:
                is_root = False
                break
        if is_root:
            return g
    return None

def is_ntt_prime(p, max_n):
    """检查是否为适合NTT的素数"""
    if not is_prime(p):
        return False
    
    # 检查p-1是否包含足够大的2的幂次
    k = 0
    temp = p - 1
    while temp % 2 == 0:
        temp //= 2
        k += 1
    
    return (1 << k) >= max_n

def generate_ntt_primes():
    """生成适合NTT的素数"""
    primes = []
    
    # 常用的NTT素数
    common_primes = [
        998244353,  # 119 * 2^23 + 1
        1004535809, # 479 * 2^21 + 1  
        469762049,  # 7 * 2^26 + 1
        985661441,  # 235 * 2^22 + 1
        754974721,  # 45 * 2^24 + 1
        7340033,    # 7 * 2^20 + 1
        5767169,    # 11 * 2^19 + 1
        3145729,    # 3 * 2^20 + 1
    ]
    
    for p in common_primes:
        if is_ntt_prime(p, 65536):
            primes.append(p)
    
    return primes

def ntt_multiply(a, b, p):
    """使用NTT计算多项式乘法（Python参考实现）"""
    n = len(a)
    m = len(b)
    result_size = n + m - 1
    
    # 找到大于等于result_size的最小2的幂
    ntt_size = 1
    while ntt_size < result_size:
        ntt_size <<= 1
    
    # 扩展数组
    A = a + [0] * (ntt_size - n)
    B = b + [0] * (ntt_size - m)
    
    # 找原根
    g = find_primitive_root(p)
    if g is None:
        raise ValueError(f"Cannot find primitive root for {p}")
    
    # 计算主单位根
    wn = pow(g, (p - 1) // ntt_size, p)
    
    # 正向NTT
    A = ntt_transform(A, wn, p)
    B = ntt_transform(B, wn, p)
    
    # 点乘
    for i in range(ntt_size):
        A[i] = (A[i] * B[i]) % p
    
    # 逆向NTT
    inv_wn = pow(wn, p - 2, p)
    A = ntt_transform(A, inv_wn, p)
    
    # 除以n
    inv_n = pow(ntt_size, p - 2, p)
    for i in range(ntt_size):
        A[i] = (A[i] * inv_n) % p
    
    return A[:result_size]

def ntt_transform(a, wn, p):
    """NTT变换"""
    n = len(a)
    
    # 位反转置换
    j = 0
    for i in range(1, n):
        bit = n >> 1
        while j & bit:
            j ^= bit
            bit >>= 1
        j ^= bit
        if i < j:
            a[i], a[j] = a[j], a[i]
    
    # 蝶形运算
    length = 2
    while length <= n:
        w = pow(wn, n // length, p)
        for i in range(0, n, length):
            wn_i = 1
            for j in range(length // 2):
                u = a[i + j]
                v = (a[i + j + length // 2] * wn_i) % p
                a[i + j] = (u + v) % p
                a[i + j + length // 2] = (u - v + p) % p
                wn_i = (wn_i * w) % p
        length <<= 1
    
    return a

def generate_test_case(case_id, n, p):
    """生成单个测试用例"""
    # 生成随机多项式
    a = [random.randint(0, p - 1) for _ in range(n)]
    b = [random.randint(0, p - 1) for _ in range(n)]
    
    # 计算期望结果
    try:
        result = ntt_multiply(a, b, p)
    except Exception as e:
        print(f"Error computing NTT for case {case_id}: {e}")
        return False
    
    # 写入输入文件
    input_file = f"nttdata/{case_id}.in"
    with open(input_file, 'w') as f:
        f.write(f"{n} {p}\n")
        f.write(" ".join(map(str, a)) + " \n")
        f.write(" ".join(map(str, b)) + " \n")
    
    # 写入输出文件
    output_file = f"nttdata/{case_id}.out"
    with open(output_file, 'w') as f:
        f.write(" ".join(map(str, result)) + " \n")
    
    print(f"Generated test case {case_id}: n={n}, p={p}")
    return True

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs("nttdata", exist_ok=True)
    
    # 获取NTT素数
    primes = generate_ntt_primes()
    print(f"Found {len(primes)} suitable NTT primes: {primes}")
    
    # 测试用例配置
    test_configs = [
        # 小规模测试
        (4, primes[0]),
        (8, primes[1] if len(primes) > 1 else primes[0]),
        (16, primes[2] if len(primes) > 2 else primes[0]),
        (32, primes[3] if len(primes) > 3 else primes[0]),
        (64, primes[0]),
        
        # 中等规模测试
        (128, primes[1] if len(primes) > 1 else primes[0]),
        (256, primes[2] if len(primes) > 2 else primes[0]),
        (512, primes[0]),
        (1024, primes[1] if len(primes) > 1 else primes[0]),
        (2048, primes[0]),
        
        # 大规模测试
        (4096, primes[0]),
        (8192, primes[1] if len(primes) > 1 else primes[0]),
        (16384, primes[0]),
        
        # 特殊规模测试（非2的幂）
        (100, primes[0]),
        (1000, primes[1] if len(primes) > 1 else primes[0]),
        
        # 边界测试
        (1, primes[0]),
        (2, primes[1] if len(primes) > 1 else primes[0]),
        (3, primes[2] if len(primes) > 2 else primes[0]),
        
        # 性能测试用例
        (32768, primes[0]),
        (65536, primes[0]),
    ]
    
    # 生成测试用例
    case_id = 0
    successful_cases = 0
    
    for n, p in test_configs:
        if generate_test_case(case_id, n, p):
            successful_cases += 1
        case_id += 1
    
    # 生成额外的随机测试用例
    print("\nGenerating additional random test cases...")
    for i in range(10):
        n = random.choice([64, 128, 256, 512, 1024])
        p = random.choice(primes)
        if generate_test_case(case_id, n, p):
            successful_cases += 1
        case_id += 1
    
    print(f"\nGenerated {successful_cases} test cases successfully!")
    print(f"Test data saved in nttdata/ directory")
    
    # 生成测试用例摘要
    with open("nttdata/test_summary.txt", 'w') as f:
        f.write("NTT Test Cases Summary\n")
        f.write("=====================\n\n")
        f.write(f"Total test cases: {successful_cases}\n")
        f.write(f"Available NTT primes: {primes}\n\n")
        f.write("Test case configurations:\n")
        for i, (n, p) in enumerate(test_configs[:successful_cases]):
            f.write(f"Case {i}: n={n}, p={p}\n")

if __name__ == "__main__":
    main()
